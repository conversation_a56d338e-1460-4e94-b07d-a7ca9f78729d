
// SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS
const fs = require('fs');
const path = require('path');

console.log('⚠️ AVISO: Este script foi modificado para prevenir resets acidentais.');
console.log('Para limpar o cache do Next.js, use: npm run clean-cache');
console.log('Para restaurar um checkpoint, use: npm run restore-checkpoint');

// Apenas limpa o cache do Next.js sem afetar os arquivos da aplicação
const cacheDir = path.join(process.cwd(), '.next');
if (fs.existsSync(cacheDir)) {
  console.log('Removendo cache do Next.js...');
  fs.rmSync(cacheDir, { recursive: true, force: true });
}

console.log('Cache limpo com sucesso. A estrutura da aplicação foi preservada.');
