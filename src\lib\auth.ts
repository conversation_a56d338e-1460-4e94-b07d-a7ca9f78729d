import { auth } from './firebase';
import {
  signInWithPopup,
  GoogleAuthProvider,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { usuariosFirestore } from '@/lib/firestore';
import { Usuario } from '@/types/usuario';

const provider = new GoogleAuthProvider();

export const signInWithGoogle = async () => {
  try {
    const result = await signInWithPopup(auth, provider);
    return result.user;
  } catch (error) {
    console.error('Erro no login:', error);
    throw error;
  }
};

export const signOut = async () => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Erro no logout:', error);
    throw error;
  }
};

export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// Função para registrar novo usuário
export const registrarUsuario = async (dadosUsuario: Partial<Usuario>): Promise<Usuario> => {
  try {
    const id = await usuariosFirestore.salvar(dadosUsuario);
    const usuarioSalvo = await usuariosFirestore.buscarPorId(id);

    if (!usuarioSalvo) {
      throw new Error('Erro ao recuperar usuário após criação');
    }

    return usuarioSalvo;
  } catch (error) {
    console.error('❌ Erro ao registrar usuário:', error);
    throw error;
  }
};

export { auth };
