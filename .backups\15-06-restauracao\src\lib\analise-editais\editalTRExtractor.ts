// Extrator de TR de dentro do Edital (Anexo I)

interface TRExtraido {
  encontrado: boolean;
  conteudo: string;
  localizacao: string;
  confianca: number;
  tipo: 'anexo_i' | 'integrado' | 'nao_encontrado';
}

export async function extrairTRDoEdital(conteudoEdital: string): Promise<TRExtraido> {
  const conteudo = conteudoEdital.toLowerCase();
  
  // Padrões para identificar TR dentro do edital
  const padroesTR = [
    /anexo\s+i[:\s\-]*termo\s+de\s+referência/gi,
    /anexo\s+1[:\s\-]*termo\s+de\s+referência/gi,
    /anexo\s+i[:\s\-]*tr/gi,
    /termo\s+de\s+referência[:\s\-]*anexo\s+i/gi,
    /anexo\s+i[\s\S]*?termo\s+de\s+referência/gi
  ];
  
  let trEncontrado: TRExtraido = {
    encontrado: false,
    conteudo: '',
    localizacao: '',
    confianca: 0,
    tipo: 'nao_encontrado'
  };
  
  // Buscar padrões de TR
  for (const padrao of padroesTR) {
    const matches = conteudoEdital.match(padrao);
    if (matches && matches.length > 0) {
      trEncontrado = await extrairConteudoTR(conteudoEdital, matches[0]);
      if (trEncontrado.encontrado) {
        break;
      }
    }
  }
  
  // Se não encontrou como anexo, buscar TR integrado
  if (!trEncontrado.encontrado) {
    trEncontrado = await buscarTRIntegrado(conteudoEdital);
  }
  
  return trEncontrado;
}

async function extrairConteudoTR(conteudoCompleto: string, marcadorInicio: string): Promise<TRExtraido> {
  const posicaoInicio = conteudoCompleto.toLowerCase().indexOf(marcadorInicio.toLowerCase());
  
  if (posicaoInicio === -1) {
    return {
      encontrado: false,
      conteudo: '',
      localizacao: '',
      confianca: 0,
      tipo: 'nao_encontrado'
    };
  }
  
  // Buscar o final do TR (próximo anexo ou final do documento)
  const marcadoresFim = [
    /anexo\s+ii/gi,
    /anexo\s+2/gi,
    /anexo\s+b/gi,
    /fim\s+do\s+anexo/gi,
    /próximo\s+anexo/gi
  ];
  
  let posicaoFim = conteudoCompleto.length;
  
  for (const marcadorFim of marcadoresFim) {
    const match = conteudoCompleto.slice(posicaoInicio + marcadorInicio.length).match(marcadorFim);
    if (match && match.index !== undefined) {
      const posicaoRelativa = match.index;
      posicaoFim = Math.min(posicaoFim, posicaoInicio + marcadorInicio.length + posicaoRelativa);
    }
  }
  
  // Extrair conteúdo do TR
  const conteudoTR = conteudoCompleto.slice(posicaoInicio, posicaoFim).trim();
  
  // Validar se realmente é um TR
  const validacao = validarConteudoTR(conteudoTR);
  
  return {
    encontrado: validacao.valido,
    conteudo: conteudoTR,
    localizacao: `Anexo I (posição ${posicaoInicio}-${posicaoFim})`,
    confianca: validacao.confianca,
    tipo: 'anexo_i'
  };
}

async function buscarTRIntegrado(conteudoEdital: string): Promise<TRExtraido> {
  const conteudo = conteudoEdital.toLowerCase();
  
  // Buscar seções que podem conter TR integrado
  const secoesTR = [
    /especificações\s+técnicas[\s\S]*?(?=\n\s*\d+\.|\n\s*[a-z]\)|\n\s*anexo|$)/gi,
    /objeto\s+da\s+contratação[\s\S]*?(?=\n\s*\d+\.|\n\s*[a-z]\)|\n\s*anexo|$)/gi,
    /descrição\s+detalhada[\s\S]*?(?=\n\s*\d+\.|\n\s*[a-z]\)|\n\s*anexo|$)/gi
  ];
  
  let melhorTR: TRExtraido = {
    encontrado: false,
    conteudo: '',
    localizacao: '',
    confianca: 0,
    tipo: 'nao_encontrado'
  };
  
  for (const secao of secoesTR) {
    const matches = conteudoEdital.match(secao);
    if (matches && matches.length > 0) {
      const conteudoSecao = matches[0];
      const validacao = validarConteudoTR(conteudoSecao);
      
      if (validacao.valido && validacao.confianca > melhorTR.confianca) {
        melhorTR = {
          encontrado: true,
          conteudo: conteudoSecao,
          localizacao: 'TR integrado no edital',
          confianca: validacao.confianca,
          tipo: 'integrado'
        };
      }
    }
  }
  
  return melhorTR;
}

function validarConteudoTR(conteudo: string): { valido: boolean; confianca: number } {
  const conteudoLower = conteudo.toLowerCase();
  
  // Elementos essenciais de um TR
  const elementosTR = [
    'especificação',
    'técnica',
    'quantidade',
    'unidade',
    'objeto',
    'descrição',
    'critério',
    'aceitação',
    'fiscalização',
    'obrigação',
    'prazo',
    'entrega'
  ];
  
  // Contar elementos presentes
  const elementosPresentes = elementosTR.filter(elemento => 
    conteudoLower.includes(elemento)
  ).length;
  
  // Calcular confiança baseada na presença de elementos
  const confianca = (elementosPresentes / elementosTR.length) * 100;
  
  // Validações adicionais
  const temTamanhoMinimo = conteudo.length > 200;
  const temEstrutura = conteudoLower.includes('especificação') || conteudoLower.includes('objeto');
  
  const valido = confianca >= 30 && temTamanhoMinimo && temEstrutura;
  
  return {
    valido,
    confianca: Math.round(confianca)
  };
}

export function verificarNecessidadeTRSeparado(conteudoEdital: string): {
  precisaTRSeparado: boolean;
  motivo: string;
  tipoProcesso: 'obras' | 'servicos' | 'materiais' | 'outros';
} {
  const conteudo = conteudoEdital.toLowerCase();
  
  // Identificar tipo de processo
  let tipoProcesso: 'obras' | 'servicos' | 'materiais' | 'outros' = 'outros';
  
  if (conteudo.includes('obra') || conteudo.includes('construção') || conteudo.includes('pavimentação')) {
    tipoProcesso = 'obras';
  } else if (conteudo.includes('serviço') || conteudo.includes('prestação')) {
    tipoProcesso = 'servicos';
  } else if (conteudo.includes('material') || conteudo.includes('equipamento') || conteudo.includes('aquisição')) {
    tipoProcesso = 'materiais';
  }
  
  // Regras para necessidade de TR separado
  if (tipoProcesso === 'obras') {
    return {
      precisaTRSeparado: true,
      motivo: 'Processos de obras geralmente requerem TR separado com especificações técnicas detalhadas',
      tipoProcesso
    };
  }
  
  // Verificar se TR está integrado no edital
  const trIntegrado = conteudo.includes('anexo i') && conteudo.includes('termo de referência');
  
  if (trIntegrado) {
    return {
      precisaTRSeparado: false,
      motivo: 'TR identificado como Anexo I do edital',
      tipoProcesso
    };
  }
  
  return {
    precisaTRSeparado: true,
    motivo: 'TR não identificado no edital - arquivo separado necessário',
    tipoProcesso
  };
}

export async function processarEditalComTR(conteudoEdital: string): Promise<{
  edital: string;
  tr: TRExtraido;
  necessidadeTRSeparado: any;
  recomendacoes: string[];
}> {
  
  // Extrair TR do edital
  const trExtraido = await extrairTRDoEdital(conteudoEdital);
  
  // Verificar necessidade de TR separado
  const necessidadeTR = verificarNecessidadeTRSeparado(conteudoEdital);
  
  // Gerar recomendações
  const recomendacoes = [];
  
  if (trExtraido.encontrado) {
    recomendacoes.push(`TR encontrado no edital (${trExtraido.tipo}) com ${trExtraido.confianca}% de confiança`);
    
    if (trExtraido.confianca < 70) {
      recomendacoes.push('TR identificado mas com baixa confiança - revisar manualmente');
    }
  } else {
    recomendacoes.push('TR não encontrado no edital - upload de arquivo separado necessário');
  }
  
  if (necessidadeTR.tipoProcesso === 'obras' && !trExtraido.encontrado) {
    recomendacoes.push('ATENÇÃO: Processo de obras sem TR identificado - verificar urgentemente');
  }
  
  return {
    edital: conteudoEdital,
    tr: trExtraido,
    necessidadeTRSeparado: necessidadeTR,
    recomendacoes
  };
}
