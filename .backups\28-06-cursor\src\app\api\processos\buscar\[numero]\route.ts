import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ numero: string }> }
) {
  try {
    const { numero } = await params;
    const numeroProcesso = decodeURIComponent(numero);
    
    if (!numeroProcesso) {
      return NextResponse.json(
        {
          success: false,
          error: 'Número do processo não fornecido',
        },
        { status: 400 }
      );
    }

    const processo = await CSVReader.getProcessoById(numeroProcesso);
    
    if (processo) {
      return NextResponse.json({
        success: true,
        processo: processo,
        message: 'Processo encontrado'
      });
    } else {
      return NextResponse.json({
        success: false,
        processo: null,
        message: 'Processo não encontrado'
      });
    }
  } catch (error) {
    console.error('Erro ao buscar processo:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
