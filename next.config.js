/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,

  // 🎯 DEPLOY APENAS MÓDULO PROCESSOS
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['src/app/processos', 'src/app/dashboard', 'src/components', 'src/lib', 'src/types'], // Apenas módulos necessários
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // 🔇 SUPRIMIR WARNINGS DESNECESSÁRIOS EM DESENVOLVIMENTO
  logging: {
    fetches: {
      fullUrl: false,
    },
  },
  
  // Configuração para otimizar o carregamento de imagens
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  
  // Otimizações de performance
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // 🔒 CONFIGURAÇÕES ROBUSTAS PARA RECHARTS + NEXT.JS
  webpack: (config, { dev, isServer }) => {
    // Configuração específica para Recharts evitar problemas SSR
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    if (dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 1,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
          // Separar Recharts em chunk próprio
          recharts: {
            test: /[\\/]node_modules[\\/]recharts[\\/]/,
            name: 'recharts',
            priority: 10,
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },

  // 🔒 TRANSPILAÇÃO FORÇADA PARA RECHARTS
  transpilePackages: ['recharts'],

  // 🔧 CONFIGURAÇÕES PARA REDUZIR ERROS DE DESENVOLVIMENTO
  onDemandEntries: {
    // Período em ms para manter as páginas em memória
    maxInactiveAge: 25 * 1000,
    // Número de páginas que devem ser mantidas simultaneamente
    pagesBufferLength: 2,
  },

  // 🎯 REDIRECTS - APENAS MÓDULO PROCESSOS ATIVO
  async redirects() {
    return [
      // Redirecionar módulos não finalizados para processos
      {
        source: '/pesquisa-precos/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/analise-editais/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/contratos/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/relatorios/:path*',
        destination: '/dashboard',
        permanent: false,
      },
      {
        source: '/admin/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/usuarios/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/secretarias/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/configuracoes/:path*',
        destination: '/processos',
        permanent: false,
      },
      {
        source: '/seguranca/:path*',
        destination: '/processos',
        permanent: false,
      },
    ];
  },

  // DESABILITAR PAINEL DE DESENVOLVIMENTO (Remove o "N" maldito)
  devIndicators: {
    position: 'bottom-right',
  },
  
  // Headers de segurança para Firebase Auth
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'ALLOWALL',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin-allow-popups',
          },
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'unsafe-none',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.gstatic.com https://securetoken.googleapis.com https://www.googleapis.com",
              "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
              "font-src 'self' https://fonts.gstatic.com",
              "img-src 'self' data: https: blob:",
              "connect-src 'self' https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://firestore.googleapis.com https://*.firebaseio.com https://*.googleapis.com",
              "frame-src 'self' https://inovaprocess-novo.firebaseapp.com https://accounts.google.com https://content.googleapis.com https://inovaprocess.app",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'"
            ].join('; '),
          },
        ],
      },
    ];
  },

  // Configuração de cache para melhor performance
  // Desativa o cache em memória para evitar problemas em ambientes de produção
  cacheMaxMemorySize: 0,
  cacheHandler: require.resolve('./cache-handler.js'),
};

module.exports = nextConfig;



