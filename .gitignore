# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# backups e checkpoints
backup-*/
backups/
checkpoints/
*.backup
*.bak

# arquivos temporários
*.tmp
*.temp
.cache/

# logs
*.log

# PROTEÇÃO EXTRA - DADOS SENSÍVEIS
# Dados reais CSV
data/
*.csv
Acompanhamento*.csv

# Arquivos de configuração sensíveis
.env*
!.env.example
!.env.local.example

# Chaves e certificados
*.key
*.pem
*.p12
*.pfx

# Arquivos de banco de dados
*.db
*.sqlite
*.sqlite3

# Arquivos de configuração local
config.local.*
settings.local.*

# Arquivos de sessão
sessions/
.sessions

# Uploads e arquivos temporários
uploads/
temp/
tmp/

# Backups antigos (não verificar no build)
backup-antes-*/
backup-critico-*/

.vercel

.vercel
