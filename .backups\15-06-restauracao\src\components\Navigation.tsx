'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function Navigation() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path ? 'bg-blue-700' : '';
  };

  return (
    <nav className="bg-blue-600 text-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center">
              <span className="text-xl font-bold">InovaProcess</span>
            </Link>
          </div>
          
          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-4">
              <Link 
                href="/dashboard" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/dashboard')}`}
              >
                Dashboard
              </Link>
              <Link 
                href="/processos" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/processos')}`}
              >
                Processos
              </Link>
              <Link 
                href="/relatorios" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/relatorios')}`}
              >
                Relatórios
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}



