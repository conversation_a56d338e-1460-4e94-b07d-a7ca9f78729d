# 🔥 CONFIGURAR FIREBASE - URGENTE

## 1. Acesse o Firebase Console
- Vá para: https://console.firebase.google.com
- Faça login com sua conta Google

## 2. Criar/Selecionar Projeto
- Clique em "Criar um projeto" ou use existente
- Nome sugerido: "inovaprocess-prod"

## 3. Ativar Authentication
- No menu lateral: Authentication
- Clique em "Começar"
- A<PERSON> "Sign-in method"
- Ativar "Google"
- Adicionar domínio autorizado: localhost (para teste)

## 4. <PERSON><PERSON>ar as Configurações
- No menu lateral: Configurações do projeto (ícone engrenagem)
- Rolar até "Seus apps"
- Clique no ícone "</>" (Web)
- Registrar app com nome: "InovaProcess"
- COPIAR as configurações que aparecem

## 5. Substituir no .env.local
Substitua estas linhas no arquivo .env.local:

```
NEXT_PUBLIC_FIREBASE_API_KEY=SUA_CHAVE_REAL_AQUI
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=seu-projeto.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=seu-projeto-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=seu-projeto.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef123456789012345678
```

## 6. Testar
- Salve o .env.local
- Reinicie o servidor: Ctrl+C e npm run dev
- Teste o login Google

## ⚠️ IMPORTANTE
- Use apenas as chaves REAIS do Firebase
- NÃO use chaves mockadas/falsas
- Confirme que Google Auth está ativado no projeto
