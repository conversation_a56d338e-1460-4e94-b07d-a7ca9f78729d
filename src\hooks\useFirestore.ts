/**
 * 🪝 HOOKS PARA FIRESTORE
 * Hooks personalizados para gerenciar dados do Firestore
 */

import { useState, useEffect } from 'react';
import { 
  usuariosFirestore, 
  processosFirestore, 
  contratosFirestore, 
  configuracoesFirestore,
  firestoreUtils 
} from '@/lib/firestore';
import type { Usuario } from '@/types/usuario';
import type { Processo } from '@/types/processo';
import type { Contrato } from '@/types/contrato';

// 👤 Hook para gerenciar usuários
export const useUsuarios = () => {
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const carregarUsuarios = async () => {
    try {
      setLoading(true);
      setError(null);
      const dados = await usuariosFirestore.listarTodos();
      setUsuarios(dados);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar usuários');
      console.error('❌ Erro ao carregar usuários:', err);
    } finally {
      setLoading(false);
    }
  };

  const buscarUsuario = async (id: string): Promise<Usuario | null> => {
    try {
      return await usuariosFirestore.buscarPorId(id);
    } catch (err) {
      console.error('❌ Erro ao buscar usuário:', err);
      return null;
    }
  };

  const salvarUsuario = async (usuario: Partial<Usuario>): Promise<string | null> => {
    try {
      const id = await usuariosFirestore.salvar(usuario);
      await carregarUsuarios(); // Recarregar lista
      return id;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao salvar usuário');
      console.error('❌ Erro ao salvar usuário:', err);
      return null;
    }
  };

  useEffect(() => {
    if (firestoreUtils.isAvailable()) {
      carregarUsuarios();
    }
  }, []);

  return {
    usuarios,
    loading,
    error,
    carregarUsuarios,
    buscarUsuario,
    salvarUsuario
  };
};

// 📋 Hook para gerenciar processos
export const useProcessos = (filtros?: { status?: string; secretaria?: string }) => {
  const [processos, setProcessos] = useState<Processo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const carregarProcessos = async () => {
    try {
      setLoading(true);
      setError(null);
      const dados = await processosFirestore.listar(filtros);
      setProcessos(dados);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar processos');
      console.error('❌ Erro ao carregar processos:', err);
    } finally {
      setLoading(false);
    }
  };

  const buscarProcesso = async (id: string): Promise<Processo | null> => {
    try {
      return await processosFirestore.buscarPorId(id);
    } catch (err) {
      console.error('❌ Erro ao buscar processo:', err);
      return null;
    }
  };

  const salvarProcesso = async (processo: Partial<Processo>): Promise<string | null> => {
    try {
      const id = await processosFirestore.salvar(processo);
      await carregarProcessos(); // Recarregar lista
      return id;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao salvar processo');
      console.error('❌ Erro ao salvar processo:', err);
      return null;
    }
  };

  useEffect(() => {
    if (firestoreUtils.isAvailable()) {
      carregarProcessos();
    }
  }, [filtros?.status, filtros?.secretaria]);

  return {
    processos,
    loading,
    error,
    carregarProcessos,
    buscarProcesso,
    salvarProcesso
  };
};

// 📄 Hook para gerenciar contratos
export const useContratos = () => {
  const [contratos, setContratos] = useState<Contrato[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const carregarContratos = async () => {
    try {
      setLoading(true);
      setError(null);
      const dados = await contratosFirestore.listar();
      setContratos(dados);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar contratos');
      console.error('❌ Erro ao carregar contratos:', err);
    } finally {
      setLoading(false);
    }
  };

  const salvarContrato = async (contrato: Partial<Contrato>): Promise<string | null> => {
    try {
      const id = await contratosFirestore.salvar(contrato);
      await carregarContratos(); // Recarregar lista
      return id;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao salvar contrato');
      console.error('❌ Erro ao salvar contrato:', err);
      return null;
    }
  };

  useEffect(() => {
    if (firestoreUtils.isAvailable()) {
      carregarContratos();
    }
  }, []);

  return {
    contratos,
    loading,
    error,
    carregarContratos,
    salvarContrato
  };
};

// ⚙️ Hook para configurações do sistema
export const useConfiguracoes = () => {
  const [configuracoes, setConfiguracoes] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const carregarConfiguracoes = async () => {
    try {
      setLoading(true);
      setError(null);
      const dados = await configuracoesFirestore.buscar();
      setConfiguracoes(dados);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar configurações');
      console.error('❌ Erro ao carregar configurações:', err);
    } finally {
      setLoading(false);
    }
  };

  const salvarConfiguracoes = async (config: any): Promise<boolean> => {
    try {
      await configuracoesFirestore.salvar(config);
      await carregarConfiguracoes(); // Recarregar
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao salvar configurações');
      console.error('❌ Erro ao salvar configurações:', err);
      return false;
    }
  };

  useEffect(() => {
    if (firestoreUtils.isAvailable()) {
      carregarConfiguracoes();
    }
  }, []);

  return {
    configuracoes,
    loading,
    error,
    carregarConfiguracoes,
    salvarConfiguracoes
  };
};

// 🔧 Hook para status do Firestore
export const useFirestoreStatus = () => {
  const [isAvailable, setIsAvailable] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [statusInfo, setStatusInfo] = useState<{
    version?: string;
    initializedBy?: string;
    initDate?: string;
  }>({});

  useEffect(() => {
    const checkStatus = async () => {
      const available = firestoreUtils.isAvailable();
      setIsAvailable(available);

      if (available) {
        try {
          // Importar função de verificação dinamicamente para evitar dependência circular
          const { checkFirestoreStatus } = await import('@/lib/firebase');
          const status = await checkFirestoreStatus();

          setIsInitialized(status.isInitialized);
          setStatusInfo({
            version: status.version,
            initializedBy: status.initializedBy,
            initDate: status.initDate
          });
        } catch (error) {
          console.error('❌ Erro ao verificar status:', error);
          setIsInitialized(false);
        }
      }
    };

    checkStatus();

    // Verificar apenas uma vez no início (não periodicamente para performance)
    // Se precisar atualizar, pode ser chamado manualmente
  }, []);

  return {
    isAvailable,
    isInitialized,
    statusInfo,
    refresh: async () => {
      const available = firestoreUtils.isAvailable();
      setIsAvailable(available);

      if (available) {
        try {
          const { checkFirestoreStatus } = await import('@/lib/firebase');
          const status = await checkFirestoreStatus();

          setIsInitialized(status.isInitialized);
          setStatusInfo({
            version: status.version,
            initializedBy: status.initializedBy,
            initDate: status.initDate
          });
        } catch (error) {
          console.error('❌ Erro ao verificar status:', error);
          setIsInitialized(false);
        }
      }
    }
  };
};
