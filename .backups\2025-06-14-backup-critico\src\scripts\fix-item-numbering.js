const fs = require('fs');
const path = require('path');

// Script para corrigir a numeração sequencial da coluna ITEM

function fixItemNumbering() {
  console.log('🔧 CORRIGINDO NUMERAÇÃO DA COLUNA ITEM...');
  
  const dataDir = path.join(__dirname, '../../data');
  
  // TODAS AS PASTAS QUE PRECISAM SER CORRIGIDAS
  const folders = [
    'backup-abril-v1',
    'databackup-29 e 30-04',
    'databackup-maio-v1', 
    'databackup-junho-v1'
  ];
  
  let totalFixed = 0;
  
  folders.forEach(folder => {
    console.log(`\n📁 CORRIGINDO PASTA: ${folder}`);
    
    const folderPath = path.join(dataDir, folder);
    
    if (!fs.existsSync(folderPath)) {
      console.log(`❌ Pasta não encontrada: ${folderPath}`);
      return;
    }
    
    // Listar todos os arquivos CSV processados na pasta
    const files = fs.readdirSync(folderPath);
    const csvFiles = files.filter(file => file.includes('-processado.csv'));
    
    console.log(`📄 Arquivos processados encontrados: ${csvFiles.length}`);
    
    csvFiles.forEach(file => {
      console.log(`\n🔄 Corrigindo: ${file}`);
      
      const filePath = path.join(folderPath, file);
      
      try {
        // Ler arquivo
        const content = fs.readFileSync(filePath, 'utf-8');
        
        // Dividir em linhas
        const lines = content.split('\n');
        console.log(`   📊 Total de linhas: ${lines.length}`);
        
        // Processar cada linha
        const correctedLines = [];
        let itemCounter = 1;
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          
          // Pular linhas vazias
          if (!line || line === ',,,,,,,,,,,,,,,,,,,,,,,') {
            continue;
          }
          
          if (i === 0) {
            // Header - manter como está
            correctedLines.push(line);
          } else {
            // Dados - corrigir numeração ITEM
            // Usar regex para substituir apenas o primeiro campo (ITEM)
            const correctedLine = line.replace(/^"[^"]*"/, `"${itemCounter}"`);
            correctedLines.push(correctedLine);
            itemCounter++;
          }
        }
        
        console.log(`   ✅ Linhas corrigidas: ${correctedLines.length}`);
        console.log(`   📊 Último ITEM: ${itemCounter - 1}`);
        
        // Escrever arquivo corrigido
        const finalContent = correctedLines.join('\n');
        fs.writeFileSync(filePath, finalContent, 'utf-8');
        
        console.log(`   ✅ Arquivo corrigido salvo!`);
        
        totalFixed++;
        
      } catch (error) {
        console.error(`   ❌ Erro ao corrigir ${file}:`, error.message);
      }
    });
  });
  
  console.log(`\n🎯 CORREÇÃO CONCLUÍDA!`);
  console.log(`📊 Total de arquivos corrigidos: ${totalFixed}`);
}

// Executar se chamado diretamente
if (require.main === module) {
  fixItemNumbering();
}

module.exports = { fixItemNumbering };
