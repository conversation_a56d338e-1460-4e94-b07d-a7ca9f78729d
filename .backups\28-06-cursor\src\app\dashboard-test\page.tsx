'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleChart } from '@/components/dashboard/SimpleChart';

interface Stats {
  total: number;
  porStatus: Record<string, number>;
  porModalidade: Record<string, number>;
  porSecretaria: Record<string, number>;
}

export default function DashboardTest() {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🚀 TESTE: Carregando dados...');
        
        const response = await fetch('/api/processos/stats');
        console.log('📡 TESTE: Response status:', response.status);
        
        const data = await response.json();
        console.log('📊 TESTE: Data recebida:', data);
        
        if (data.success) {
          setStats(data.data.estatisticas);
          console.log('✅ TESTE: Stats definidas:', data.data.estatisticas);
        } else {
          setError(data.error || 'Erro ao carregar dados');
        }
      } catch (err) {
        console.error('❌ TESTE: Erro:', err);
        setError('Erro de conexão');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Dashboard Teste</h1>
        <p>Carregando...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Dashboard Teste</h1>
        <p className="text-red-500">Erro: {error}</p>
      </div>
    );
  }

  // Preparar dados para gráficos
  const chartDataModalidade = stats?.porModalidade 
    ? Object.entries(stats.porModalidade).map(([name, value]) => ({ 
        name: name.replace(/Ã£/g, 'ã').replace(/Ã§/g, 'ç'), 
        value: Number(value) 
      }))
    : [];

  const chartDataSecretaria = stats?.porSecretaria 
    ? Object.entries(stats.porSecretaria)
        .map(([name, value]) => ({ 
          name: name.length > 15 ? name.substring(0, 15) + '...' : name, 
          value: Number(value) 
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 8)
    : [];

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-3xl font-bold">Dashboard Teste</h1>
      
      {/* Métricas básicas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Total de Processos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{stats?.total || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Modalidades</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {stats?.porModalidade ? Object.keys(stats.porModalidade).length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Secretarias</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {stats?.porSecretaria ? Object.keys(stats.porSecretaria).length : 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Processos por Modalidade</CardTitle>
          </CardHeader>
          <CardContent>
            <SimpleChart
              title="Modalidades"
              data={chartDataModalidade}
              type="pie"
              height={300}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Processos por Secretaria</CardTitle>
          </CardHeader>
          <CardContent>
            <SimpleChart
              title="Secretarias"
              data={chartDataSecretaria}
              type="bar"
              height={300}
            />
          </CardContent>
        </Card>
      </div>

      {/* Debug info */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Info</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(stats, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
