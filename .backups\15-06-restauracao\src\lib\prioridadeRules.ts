/**
 * REGRAS COMPLETAS DE PRIORIDADE ALTA - INOVAPROCESS
 * 
 * ⚠️ IMPORTANTE: Estas regras são CRÍTICAS para o funcionamento do sistema
 * Qualquer alteração deve ser aprovada pela CLMP
 * 
 * Data: 09/06/2025
 * Responsável: <PERSON>LM<PERSON>
 */

import { Processo } from '@/types/processo';

/**
 * REGRA 1: FONTES DE RECURSOS NÃO-TESOURO
 * Processos com recursos externos precisam de celeridade para não perder prazos de prestação de contas
 */
export function temFonteNaoTesouro(processo: Processo): boolean {
  const fontes = [
    processo['Fonte 0002 (ESTADUAL)'],
    processo['Fonte 0003 (FUNDO)'],
    processo['Fonte 0005 (FEDERAL)'],
    processo['Fonte 0007 (FINISA)']
  ];

  return fontes.some(fonte => 
    fonte && 
    fonte !== '-' && 
    fonte !== '0' && 
    fonte !== 'R$ 0,00' && 
    fonte !== '' && 
    fonte !== undefined
  );
}

/**
 * REGRA 2: PROCESSOS JUDICIAIS
 * Qualquer demanda judicial tem prioridade máxima
 */
export function temDemandasJudiciais(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  
  const palavrasJudiciais = [
    'judicial',
    'processo judicial',
    'demanda judicial',
    'mandado',
    'liminar',
    'sentença',
    'decisão judicial',
    'ordem judicial'
  ];

  return palavrasJudiciais.some(palavra => objeto.includes(palavra));
}

/**
 * REGRA 3: ALIMENTAÇÃO ESCOLAR (SSAN)
 * Merenda, alimentação, bebidas, panificação para alunos
 */
export function temAlimentacaoEscolar(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  const requisitante = (processo.REQUISITANTE || '').toLowerCase();
  
  const palavrasAlimentacao = [
    'merenda',
    'alimentação',
    'alimentacao',
    'bebidas',
    'panificação',
    'panificacao',
    'lanche',
    'refeição',
    'refeicao',
    'cardápio',
    'cardapio',
    'nutrição',
    'nutricao',
    'alimentos',
    'gêneros alimentícios',
    'generos alimenticios'
  ];

  const isSsan = requisitante.includes('ssan') || 
                 requisitante.includes('secretaria de segurança alimentar');

  const temPalavraAlimentacao = palavrasAlimentacao.some(palavra => objeto.includes(palavra));

  // Se é SSAN OU tem palavra de alimentação = prioridade alta
  return isSsan || temPalavraAlimentacao;
}

/**
 * REGRA 4: MEDICAMENTOS E SAÚDE
 * Demandas de saúde têm prioridade alta
 */
export function temMedicamentos(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  
  const palavrasMedicamentos = [
    'medicamento',
    'medicamentos',
    'remédio',
    'remedios',
    'farmácia',
    'farmacia',
    'hospitalar',
    'cirúrgico',
    'cirurgico',
    'ambulatorial',
    'emergência',
    'emergencia',
    'urgência',
    'urgencia',
    'uti',
    'pronto socorro',
    'saúde',
    'saude',
    'médico',
    'medico',
    'enfermagem',
    'vacina',
    'imunização',
    'imunizacao'
  ];

  return palavrasMedicamentos.some(palavra => objeto.includes(palavra));
}

/**
 * REGRA 5: CLASSIFICAÇÃO MANUAL - PRIORIDADES DO GOVERNO
 * Processos classificados manualmente via botão "Prioridades do Governo"
 * 
 * ⚠️ IMPORTANTE: Esta função será implementada com sistema de usuários
 * Apenas secretários e secretários adjuntos habilitados poderão usar
 */
export function temPrioridadeGoverno(processo: Processo): boolean {
  // TODO: Implementar quando tivermos o campo no banco de dados
  // Por enquanto retorna false
  return processo.prioridadeGoverno === true || false;
}

/**
 * FUNÇÃO PRINCIPAL: VERIFICA SE PROCESSO TEM PRIORIDADE ALTA
 * Aplica TODAS as regras definidas acima
 */
export function temPrioridadeAlta(processo: Processo): boolean {
  return (
    temFonteNaoTesouro(processo) ||
    temDemandasJudiciais(processo) ||
    temAlimentacaoEscolar(processo) ||
    temMedicamentos(processo) ||
    temPrioridadeGoverno(processo)
  );
}

/**
 * OBTÉM O MOTIVO DA PRIORIDADE ALTA
 * Retorna qual regra foi aplicada para classificar como prioridade alta
 */
export function getMotivosPrioridade(processo: Processo): string[] {
  const motivos: string[] = [];

  if (temFonteNaoTesouro(processo)) {
    motivos.push('Fonte de recurso não-tesouro');
  }

  if (temDemandasJudiciais(processo)) {
    motivos.push('Demanda judicial');
  }

  if (temAlimentacaoEscolar(processo)) {
    motivos.push('Alimentação escolar');
  }

  if (temMedicamentos(processo)) {
    motivos.push('Medicamentos/Saúde');
  }

  if (temPrioridadeGoverno(processo)) {
    motivos.push('Prioridade do Governo');
  }

  return motivos;
}

/**
 * OBTÉM O TIPO DE PRIORIDADE
 */
export function getTipoPrioridade(processo: Processo): 'ALTA' | 'NORMAL' {
  return temPrioridadeAlta(processo) ? 'ALTA' : 'NORMAL';
}

/**
 * OBTÉM COR E ÍCONE PARA PRIORIDADE
 */
export function getEstiloPrioridade(processo: Processo) {
  if (temPrioridadeAlta(processo)) {
    return {
      cor: 'text-red-600 bg-red-100 border-red-200',
      icone: '🔴',
      badge: 'PRIORIDADE ALTA',
      descricao: getMotivosPrioridade(processo).join(', ')
    };
  }

  return {
    cor: 'text-blue-600 bg-blue-100 border-blue-200',
    icone: '🔵',
    badge: 'NORMAL',
    descricao: 'Processo com prioridade normal'
  };
}

/**
 * ESTATÍSTICAS DE PRIORIDADES
 */
export function calcularEstatisticasPrioridade(processos: Processo[]) {
  const total = processos.length;
  const prioridadeAlta = processos.filter(temPrioridadeAlta).length;
  
  const porMotivo = {
    fonteNaoTesouro: processos.filter(temFonteNaoTesouro).length,
    judicial: processos.filter(temDemandasJudiciais).length,
    alimentacao: processos.filter(temAlimentacaoEscolar).length,
    medicamentos: processos.filter(temMedicamentos).length,
    governo: processos.filter(temPrioridadeGoverno).length
  };

  return {
    total,
    prioridadeAlta,
    prioridadeNormal: total - prioridadeAlta,
    percentualAlta: total > 0 ? ((prioridadeAlta / total) * 100).toFixed(1) : '0',
    porMotivo
  };
}

/**
 * EXPORTAÇÕES PARA COMPATIBILIDADE
 * Mantém compatibilidade com código existente
 */
export { temFonteNaoTesouro as temFonteNaoTesouroLegacy };
export { temPrioridadeAlta as getPrioridadeProcesso };
