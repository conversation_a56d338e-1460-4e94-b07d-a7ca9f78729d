# InovaProcess

## Sobre o Projeto

O InovaProcess é uma solução moderna para acompanhamento e análise de processos de compras e obras, focada em eficiência, transparência e automação.

## Fonte de Dados

- **ÚNICA FONTE DE DADOS:** Todos os dados do sistema são lidos exclusivamente dos arquivos CSV processados e validados localizados em `/data/backup-abril-v1/`.
- **NÃO existe mais uso de banco SQLite, Prisma ou qualquer banco relacional.**
- **Scripts e APIs** utilizam bibliotecas de leitura de CSV para processar e exibir os dados reais.

## Funcionalidades
- Dashboard com métricas e indicadores reais
- Histórico de movimentações
- Relatórios e análises
- Importação e processamento automatizado dos CSVs oficiais

## Tecnologias Utilizadas
- Next.js 15
- React 19
- Tailwind CSS
- Bibliotecas de leitura e processamento de CSV (`csv-parse/sync`)

## Estrutura do Projeto
```
inovaprocess/
├── src/              # Código fonte
├── data/
│   └── backup-abril-v1/  # Backup oficial - Abril 2024 (única fonte de dados)
├── scripts/          # Scripts de processamento de CSV
└── public/           # Arquivos estáticos
```

## Como Executar
1. Instale as dependências:
   ```bash
   npm install
   ```
2. Execute o projeto:
   ```bash
   npm run dev
   ```
3. Acesse o dashboard em `http://localhost:3000/dashboard`

## Observações
- Não existe mais nenhum vestígio de banco de dados antigo, Prisma ou instrução/documentação ultrapassada.
- Toda a documentação, código e scripts refletem apenas o uso dos CSVs do backup oficial.

---
Desenvolvido para máxima transparência e eficiência na gestão de processos. 