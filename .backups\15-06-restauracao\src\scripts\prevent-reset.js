const fs = require('fs');
const path = require('path');
const { createCheckpoint, createBackup, log } = require('./checkpoint-system');

// Função para proteger scripts contra resets acidentais
function protectScripts() {
  // Modifica fix-routes.js para impedir resets acidentais
  const fixRoutesPath = path.join(process.cwd(), 'src', 'scripts', 'fix-routes.js');
  if (fs.existsSync(fixRoutesPath)) {
    const protectedContent = `
// SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS
const fs = require('fs');
const path = require('path');

console.log('⚠️ AVISO: Este script foi modificado para prevenir resets acidentais.');
console.log('Para limpar o cache do Next.js, use: npm run clean-cache');
console.log('Para restaurar um checkpoint, use: npm run restore-checkpoint');

// Apenas limpa o cache do Next.js sem afetar os arquivos da aplicação
const cacheDir = path.join(process.cwd(), '.next');
if (fs.existsSync(cacheDir)) {
  console.log('Removendo cache do Next.js...');
  fs.rmSync(cacheDir, { recursive: true, force: true });
}

console.log('Cache limpo com sucesso. A estrutura da aplicação foi preservada.');
`;
    fs.writeFileSync(fixRoutesPath, protectedContent);
    log('Script fix-routes.js protegido contra resets acidentais.');
  }
  
  // Modifica reset-app.js se existir
  const resetAppPath = path.join(process.cwd(), 'src', 'scripts', 'reset-app.js');
  if (fs.existsSync(resetAppPath)) {
    const protectedContent = `
// SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS
console.log('⚠️ AVISO: Este script foi desativado para prevenir resets acidentais.');
console.log('Para limpar o cache do Next.js, use: npm run clean-cache');
console.log('Para restaurar um checkpoint, use: npm run restore-checkpoint');
`;
    fs.writeFileSync(resetAppPath, protectedContent);
    log('Script reset-app.js protegido contra resets acidentais.');
  }
  
  // Protege outros scripts potencialmente perigosos
  const scriptsDir = path.join(process.cwd(), 'src', 'scripts');
  if (fs.existsSync(scriptsDir)) {
    const scriptFiles = fs.readdirSync(scriptsDir);
    
    for (const scriptFile of scriptFiles) {
      if (scriptFile.includes('clean') || scriptFile.includes('reset')) {
        const scriptPath = path.join(scriptsDir, scriptFile);
        const content = fs.readFileSync(scriptPath, 'utf8');
        
        // Se o script não estiver protegido, protege-o
        if (!content.includes('SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS')) {
          const protectedContent = `
// SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS
console.log('⚠️ AVISO: Este script foi desativado para prevenir resets acidentais.');
console.log('Para limpar o cache do Next.js, use: npm run clean-cache');
console.log('Para restaurar um checkpoint, use: npm run restore-checkpoint');
`;
          fs.writeFileSync(scriptPath, protectedContent);
          log(`Script ${scriptFile} protegido contra resets acidentais.`);
        }
      }
    }
  }
}

// Atualiza o package.json com scripts de segurança
function updatePackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Adiciona novos scripts
    packageJson.scripts = {
      ...packageJson.scripts,
      'predev': 'node src/scripts/prevent-reset.js --backup && node src/scripts/monitor-resets.js',
      'prebuild': 'node src/scripts/monitor-resets.js',
      'checkpoint': 'node src/scripts/checkpoint-system.js create',
      'checkpoint:list': 'node src/scripts/checkpoint-system.js list',
      'checkpoint:restore': 'node src/scripts/checkpoint-system.js restore',
      'backup': 'node src/scripts/prevent-reset.js --backup',
      'clean-cache': 'node src/scripts/prevent-reset.js --clean-cache',
      'fix': 'node src/scripts/prevent-reset.js --clean-cache',
      'check-protection': 'node src/scripts/monitor-resets.js',
      'restore-checkpoint': 'node src/scripts/prevent-reset.js --restore-checkpoint'
    };
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    log('package.json atualizado com scripts de segurança.');
  }
}

// Função principal
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--backup')) {
    // Cria um backup do estado atual
    createBackup('manual-backup');
    log('Backup criado com sucesso.');
  } else if (args.includes('--clean-cache')) {
    // Apenas limpa o cache do Next.js
    const cacheDir = path.join(process.cwd(), '.next');
    if (fs.existsSync(cacheDir)) {
      log('Removendo cache do Next.js...');
      fs.rmSync(cacheDir, { recursive: true, force: true });
    }
    log('Cache limpo com sucesso. A estrutura da aplicação foi preservada.');
  } else if (args.includes('--checkpoint')) {
    // Cria um checkpoint com nome personalizado
    const nameIndex = args.findIndex(arg => arg.startsWith('--name='));
    const name = nameIndex >= 0 ? args[nameIndex].split('=')[1] : null;
    createCheckpoint(name);
  } else {
    // Executa todas as proteções
    log('Aplicando proteções contra resets acidentais...');
    createBackup('protection-setup');
    protectScripts();
    updatePackageJson();
    log('\n✅ Proteções aplicadas com sucesso!');
    log('\nNovos comandos disponíveis:');
    log('- npm run checkpoint -- --name=NOME: Cria um checkpoint com nome personalizado');
    log('- npm run checkpoint:list: Lista todos os checkpoints disponíveis');
    log('- npm run checkpoint:restore NOME: Restaura um checkpoint específico');
    log('- npm run clean-cache: Limpa o cache do Next.js sem afetar os arquivos');
  }
}

// Executa a função principal
main();


