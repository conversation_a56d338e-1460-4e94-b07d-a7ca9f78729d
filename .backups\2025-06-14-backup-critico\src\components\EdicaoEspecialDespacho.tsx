/**
 * 🔧 COMPONENTE DE EDIÇÃO ESPECIAL DE DESPACHO
 * Permite ao Coordenador habilitar edição para casos específicos
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Edit3,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  Clock,
  User
} from 'lucide-react';
import { GeradorDespachosPesquisa } from '@/lib/despachosPesquisa';

interface EdicaoEspecialProps {
  numeroProcesso: string;
  pesquisador: string;
  coordenador: string;
  onEdicaoHabilitada?: () => void;
}

export default function EdicaoEspecialDespacho({ 
  numeroProcesso, 
  pesquisador, 
  coordenador,
  onEdicaoHabilitada 
}: EdicaoEspecialProps) {
  const [dialogAberto, setDialogAberto] = useState(false);
  const [motivo, setMotivo] = useState('');
  const [loading, setLoading] = useState(false);
  const [edicaoHabilitada, setEdicaoHabilitada] = useState(
    GeradorDespachosPesquisa.verificarEdicaoEspecial(numeroProcesso)
  );

  const habilitarEdicao = async () => {
    if (!motivo.trim()) {
      alert('Informe o motivo para habilitar a edição');
      return;
    }

    setLoading(true);
    try {
      const sucesso = GeradorDespachosPesquisa.habilitarEdicaoEspecial(
        numeroProcesso,
        motivo,
        coordenador
      );

      if (sucesso) {
        setEdicaoHabilitada(true);
        setDialogAberto(false);
        setMotivo('');
        onEdicaoHabilitada?.();
        
        // Notificar pesquisador (simulado)
        console.log(`📧 NOTIFICAÇÃO ENVIADA para ${pesquisador}`);
        console.log(`📝 Processo ${numeroProcesso} liberado para edição especial`);
      }
    } catch (error) {
      console.error('Erro ao habilitar edição:', error);
      alert('Erro ao habilitar edição especial');
    } finally {
      setLoading(false);
    }
  };

  const motivosComuns = [
    'Necessidade de incluir informação específica sobre o objeto',
    'Detalhamento adicional sobre metodologia aplicada',
    'Esclarecimento sobre particularidades do mercado',
    'Inclusão de observação técnica relevante',
    'Adequação de fundamentação jurídica específica'
  ];

  return (
    <div className="space-y-4">
      {/* Status da Edição Especial */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              {edicaoHabilitada ? (
                <Unlock className="mr-2 h-5 w-5 text-green-600" />
              ) : (
                <Lock className="mr-2 h-5 w-5 text-gray-400" />
              )}
              Edição Especial de Despacho
            </span>
            <Badge variant={edicaoHabilitada ? 'default' : 'secondary'}>
              {edicaoHabilitada ? 'HABILITADA' : 'BLOQUEADA'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {edicaoHabilitada ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Edição especial habilitada!</strong>
                <br />
                O pesquisador {pesquisador} pode editar o despacho deste processo.
                <br />
                <span className="text-xs text-muted-foreground">
                  A função será desabilitada automaticamente após o uso.
                </span>
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                O despacho será gerado automaticamente pelo sistema. 
                Use a edição especial apenas quando necessário incluir informações específicas.
              </p>
              
              <Dialog open={dialogAberto} onOpenChange={setDialogAberto}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="w-full">
                    <Edit3 className="mr-2 h-4 w-4" />
                    Habilitar Edição Especial
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>🔧 Habilitar Edição Especial</DialogTitle>
                    <DialogDescription>
                      Permite que o pesquisador edite o despacho para este processo específico.
                      A função será desabilitada automaticamente após o uso.
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Processo:</label>
                      <p className="text-sm text-muted-foreground">{numeroProcesso}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium">Pesquisador:</label>
                      <p className="text-sm text-muted-foreground">{pesquisador}</p>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Motivo da edição especial: *
                      </label>
                      <Textarea
                        placeholder="Descreva o motivo para habilitar a edição..."
                        value={motivo}
                        onChange={(e) => setMotivo(e.target.value)}
                        rows={3}
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Motivos comuns:
                      </label>
                      <div className="space-y-2">
                        {motivosComuns.map((motivoComum, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            className="h-auto p-2 text-left justify-start"
                            onClick={() => setMotivo(motivoComum)}
                          >
                            <span className="text-xs">{motivoComum}</span>
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <DialogFooter>
                    <Button 
                      variant="outline" 
                      onClick={() => setDialogAberto(false)}
                    >
                      Cancelar
                    </Button>
                    <Button 
                      onClick={habilitarEdicao}
                      disabled={!motivo.trim() || loading}
                    >
                      {loading ? (
                        <>
                          <Clock className="mr-2 h-4 w-4 animate-spin" />
                          Habilitando...
                        </>
                      ) : (
                        <>
                          <Unlock className="mr-2 h-4 w-4" />
                          Habilitar Edição
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Informações sobre o Sistema */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">ℹ️ Como Funciona</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-xs text-muted-foreground">
          <div className="flex items-start space-x-2">
            <span className="font-medium">1.</span>
            <span>Você habilita a edição especial informando o motivo</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-medium">2.</span>
            <span>O pesquisador recebe notificação e pode editar o despacho</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-medium">3.</span>
            <span>Após finalizar, a função é desabilitada automaticamente</span>
          </div>
          <div className="flex items-start space-x-2">
            <span className="font-medium">4.</span>
            <span>O processo segue normalmente para sua aprovação</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
