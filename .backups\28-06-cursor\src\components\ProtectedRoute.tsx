'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { isAuthenticated, getUsuarioAtual, hasPermission } from '@/lib/auth';
import { Loader2, Shield, AlertCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

export default function ProtectedRoute({ 
  children, 
  requiredPermission,
  fallback 
}: ProtectedRouteProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = () => {
      // Verificar autenticação
      if (!isAuthenticated()) {
        router.push('/login');
        return;
      }

      // Verificar permissão específica se necessário
      if (requiredPermission && !hasPermission(requiredPermission)) {
        setAuthorized(false);
        setLoading(false);
        return;
      }

      setAuthorized(true);
      setLoading(false);
    };

    checkAuth();
  }, [router, requiredPermission]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
          <p className="text-muted-foreground">Verificando permissões...</p>
        </div>
      </div>
    );
  }

  if (!authorized) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-full">
                <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground">
                Acesso Negado
              </h3>
              <p className="text-sm text-muted-foreground">
                Você não tem permissão para acessar esta página.
              </p>
              {requiredPermission && (
                <p className="text-xs text-muted-foreground bg-muted p-2 rounded">
                  Permissão necessária: <code>{requiredPermission}</code>
                </p>
              )}
            </div>

            <div className="flex flex-col space-y-2">
              <Button 
                onClick={() => router.back()}
                variant="outline"
                size="sm"
              >
                Voltar
              </Button>
              <Button 
                onClick={() => router.push('/dashboard')}
                size="sm"
              >
                Ir para Dashboard
              </Button>
            </div>

            <div className="pt-4 border-t border-border">
              <div className="flex items-center justify-center space-x-2 text-xs text-muted-foreground">
                <AlertCircle className="h-3 w-3" />
                <span>
                  Entre em contato com o administrador se precisar de acesso
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
}

// Hook para verificar permissões
export function usePermissions() {
  const usuario = getUsuarioAtual();
  
  return {
    usuario,
    isAuthenticated: isAuthenticated(),
    hasPermission,
    canCreate: (recurso: string) => hasPermission(`${recurso}.criar`),
    canEdit: (recurso: string) => hasPermission(`${recurso}.editar`),
    canView: (recurso: string) => hasPermission(`${recurso}.visualizar`),
    canDelete: (recurso: string) => hasPermission(`${recurso}.excluir`),
    isAdmin: () => usuario?.perfil === 'admin',
    isGestor: () => usuario?.perfil === 'gestor' || usuario?.perfil === 'admin',
    isUsuarioCadastro: () => usuario?.perfil === 'isabela' || usuario?.perfil === 'tath'
  };
}
