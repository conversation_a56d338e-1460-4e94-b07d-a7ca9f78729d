const fs = require('fs');
const path = require('path');

// Diretório de backups (mesmo usado no checkpoint-system.js)
const BACKUP_DIR = path.join(process.cwd(), 'backups');

// Verificar se o diretório existe
if (!fs.existsSync(BACKUP_DIR)) {
  console.log('Diretório de backups não encontrado.');
  process.exit(1);
}

// Listar todos os backups
const backups = fs.readdirSync(BACKUP_DIR)
  .filter(file => {
    const stats = fs.statSync(path.join(BACKUP_DIR, file));
    return stats.isDirectory();
  })
  .map(dir => {
    const stats = fs.statSync(path.join(BACKUP_DIR, dir));
    return {
      name: dir,
      date: stats.mtime,
      size: getDirectorySize(path.join(BACKUP_DIR, dir))
    };
  })
  .sort((a, b) => b.date - a.date); // Ordenar do mais recente para o mais antigo

// Exibir backups
console.log('Backups disponíveis (mais recentes primeiro):');
backups.forEach((backup, index) => {
  console.log(`${index + 1}. ${backup.name}`);
  console.log(`   Data: ${backup.date.toLocaleString()}`);
  console.log(`   Tamanho: ${formatSize(backup.size)}`);
  console.log('');
});

// Função para calcular o tamanho de um diretório
function getDirectorySize(dir) {
  let size = 0;
  
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      size += getDirectorySize(filePath);
    } else {
      size += stats.size;
    }
  }
  
  return size;
}

// Formatar tamanho em bytes para formato legível
function formatSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}