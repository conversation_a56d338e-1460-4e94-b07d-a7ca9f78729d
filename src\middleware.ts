import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rotas que não precisam de autenticação
const PUBLIC_ROUTES = ['/', '/login'];

// Rotas que sempre redirecionam para dashboard se autenticado
const AUTH_ROUTES = ['/login'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Para desenvolvimento, permitir acesso a todas as rotas após login
  // Em produção seria verificado via JWT/cookies

  // Permitir acesso à página inicial sem redirecionamento
  // O controle de autenticação será feito no ClientLayout

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
