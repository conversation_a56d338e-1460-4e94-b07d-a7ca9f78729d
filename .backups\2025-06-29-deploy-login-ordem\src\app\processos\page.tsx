'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Processo } from '@/types/processo';

import ProcessoCard from '@/components/ProcessoCard';
import Pagination from '@/components/Pagination';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  AlertCircle,
  FileText,
  Plus,
  RefreshCw,
  Search,
  Download
} from 'lucide-react';
import { calcularMetricasEficiencia } from '@/lib/processoUtils';
import DraggableBanners from '@/components/DraggableBanners';
import ProcessoDirectSearch from '@/components/ProcessoDirectSearch';

interface ProcessosResponse {
  success: boolean;
  data: {
    processos: Processo[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  };
  error?: string;
}

export default function ProcessosPage() {
  const router = useRouter();
  const [processos, setProcessos] = useState<Processo[]>([]);
  const [loading, setLoading] = useState(false); // Não carregar por padrão
  const [error, setError] = useState<string | null>(null);
  const [showingAll, setShowingAll] = useState(false);
  const [totalProcessosCount, setTotalProcessosCount] = useState(0);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPrevPage: false,
  });

  const [filters, setFilters] = useState({
    search: '',
    status: '',
    modalidade: '',
    responsavel: '',
    requisitante: '',
  });

  const [metricas, setMetricas] = useState({
    total: 0,
    prioridadeAlta: 0,
    retrabalhoReal: 0,
    gargalosSF: 0,
    gargalosSAJ: 0,
    finalizados: 0,
    taxaRetrabalho: '0',
    taxaFinalizacao: '0',
    riscoPerdaRecursos: 0,
    tempoMedioCLMP: 0,
    tempoMedioSecretaria: 0,
    processosNaSF: 0,
    processosNaSAJ: 0,
    eficienciaCLMP: '0'
  });

  // Função para carregar total de processos (apenas contagem)
  const loadTotalCount = useCallback(async () => {
    try {
      const response = await fetch('/api/processos/stats');
      const data = await response.json();

      if (data.success && data.data.estatisticas) {
        setTotalProcessosCount(data.data.estatisticas.total || 0);
      }
    } catch (err) {
      console.error('Erro ao carregar contagem:', err);
    }
  }, []);

  // Função para carregar processos (apenas quando solicitado)
  const loadProcessos = useCallback(async (page: number = 1, forceFilters?: any) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
      });

      // Usar filtros forçados ou filtros atuais
      const activeFilters = forceFilters || filters;

      // Adicionar apenas filtros que têm valor
      Object.entries(activeFilters).forEach(([key, value]) => {
        if (value && typeof value === 'string' && value.trim()) {
          params.append(key, value);
        }
      });

      const response = await fetch(`/api/processos?${params}`);
      const data: ProcessosResponse = await response.json();

      if (data.success) {
        setProcessos(data.data.processos);
        setPagination(data.data.pagination);
      } else {
        setError(data.error || 'Erro ao carregar processos');
      }
    } catch (err) {
      setError('Erro de conexão. Tente novamente.');
      console.error('Erro ao carregar processos:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Carregar métricas globais (independente dos filtros)
  useEffect(() => {
    const loadMetricasGlobais = async () => {
      try {
        const response = await fetch('/api/processos');
        const data = await response.json();

        if (data.success && data.data.processos) {
          const todosProcessos = data.data.processos;
          const metricasCalculadas = calcularMetricasEficiencia(todosProcessos);
          setMetricas(metricasCalculadas);
        }
      } catch (error) {
        console.error('Erro ao carregar métricas globais:', error);
      }
    };

    loadMetricasGlobais();
    loadTotalCount();
  }, [loadTotalCount]);

  // Escutar busca global do header
  useEffect(() => {
    const handleGlobalSearch = (event: CustomEvent) => {
      const query = event.detail.query;
      console.log('🔍 Busca global recebida:', query);
      console.log('🔍 Filtros atuais:', filters);
      setFilters(prev => {
        const newFilters = { ...prev, search: query };
        console.log('🔍 Novos filtros:', newFilters);
        return newFilters;
      });
    };

    window.addEventListener('globalSearch', handleGlobalSearch as EventListener);

    return () => {
      window.removeEventListener('globalSearch', handleGlobalSearch as EventListener);
    };
  }, [filters]);

  // Verificar parâmetros da URL na inicialização
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    if (searchParam) {
      console.log('🔍 Busca da URL:', searchParam);
      setFilters(prev => ({ ...prev, search: searchParam }));
    }
  }, []);



  // Handler para busca com filtros
  const handleSearch = useCallback((searchFilters: any) => {
    setFilters(prev => ({ ...prev, ...searchFilters }));
    setShowingAll(false);

    // Se não há filtros, limpar a lista
    const hasFilters = Object.values(searchFilters).some(v => v && v.toString().trim());
    if (!hasFilters) {
      setProcessos([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 20,
        hasNextPage: false,
        hasPrevPage: false,
      });
    } else {
      loadProcessos(1, { ...filters, ...searchFilters });
    }
  }, [filters, loadProcessos]);

  // Handler para mostrar todos os processos
  const handleShowAll = useCallback(() => {
    setFilters({
      search: '',
      status: '',
      modalidade: '',
      responsavel: '',
      requisitante: '',
    });
    setShowingAll(true);
    loadProcessos(1, {}); // Carregar sem filtros
  }, [loadProcessos]);

  // Handler para mudança de página
  const handlePageChange = useCallback((page: number) => {
    loadProcessos(page);
  }, [loadProcessos]);

  return (
    <div className="space-y-6 processos-page">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestão de Processos</h1>
          <p className="text-muted-foreground mt-2">
            Acompanhe e gerencie todos os processos de compras e de obras
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            onClick={() => router.push('/receber-processo')}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-medium px-4 py-2 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg"
          >
            <Download className="mr-2 h-4 w-4" />
            Receber Processo
          </Button>



          <Button asChild>
            <Link href="/processos/novo">
              <Plus className="mr-2 h-4 w-4" />
              Novo Processo
            </Link>
          </Button>
        </div>
      </div>

      {/* Estatísticas Estratégicas da CLMP - Draggable */}
      <DraggableBanners
        metricas={metricas}
        totalItems={totalProcessosCount}
        isAdmin={true}
      />

      {/* Painel de Busca Direta */}
      <ProcessoDirectSearch
        onSearch={handleSearch}
        onShowAll={handleShowAll}
        totalProcessos={totalProcessosCount}
      />

      {/* Conteúdo principal */}
      {error ? (
        <Card>
          <CardContent className="p-8 text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Erro ao carregar processos
            </h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button
              onClick={() => loadProcessos(pagination.currentPage)}
              variant="destructive"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar novamente
            </Button>
          </CardContent>
        </Card>
      ) : loading ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Carregando processos...</p>
          </CardContent>
        </Card>
      ) : !showingAll && processos.length === 0 && !Object.values(filters).some(f => f.trim()) ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Faça uma busca ou visualize todos os processos
            </h3>
            <p className="text-muted-foreground mb-4">
              Use os campos de busca acima ou clique em "Todos" para ver a lista completa.
            </p>
          </CardContent>
        </Card>
      ) : processos.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhum processo encontrado
            </h3>
            <p className="text-muted-foreground mb-4">
              Tente ajustar os filtros ou termos de busca.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Lista de processos - 1 por linha - DOS MAIS NOVOS PARA OS MAIS ANTIGOS */}
          <div className="space-y-4">
            {processos
              .sort((a, b) => {
                // Extrair número e ano do processo (formato: NUMERO/ANO)
                const [numAStr, anoAStr] = (a.PROCESSO || '0/2024').split('/');
                const [numBStr, anoBStr] = (b.PROCESSO || '0/2024').split('/');

                const numA = parseInt(numAStr);
                const anoA = parseInt(anoAStr);
                const numB = parseInt(numBStr);
                const anoB = parseInt(anoBStr);

                // Primeiro ordenar por ano (mais recente primeiro)
                if (anoA !== anoB) {
                  return anoB - anoA; // Decrescente: 2025 antes de 2024
                }

                // Se o ano for igual, ordenar por número (mais alto primeiro)
                return numB - numA; // Decrescente: 1255 antes de 1000
              })
              .map((processo) => (
                <ProcessoCard
                  key={`${processo.PROCESSO}-${processo.ITEM}`}
                  processo={processo}
                />
              ))}
          </div>

          {/* Paginação */}
          <Pagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            totalItems={pagination.totalItems}
            itemsPerPage={pagination.itemsPerPage}
            onPageChange={handlePageChange}
            loading={loading}
          />
        </>
      )}
    </div>
  );
}



