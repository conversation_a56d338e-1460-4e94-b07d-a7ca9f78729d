/**
 * API INTELIGENTE PARA PESQUISA NO PNCP
 * Sistema de pesquisa refinada com filtros inteligentes
 */

import { IAConversorMedidas } from './conversorMedidas';
import { PNCP_CONFIG, PNCP_ENDPOINTS, PNCPUtils, PNCPSmartFilters, type PNCPRequestParams } from './pncpEndpoints';

export interface PNCPItem {
  id: string;
  descricao: string;
  unidade: string;
  preco: number;
  fornecedor: string;
  cnpj: string;
  dataContrato: string;
  orgao: string;
  numeroContrato: string;
  especificacao: string;
  relevancia: number; // 0-100 score de relevância
  alertaUnidade?: {
    tipo: 'conversao' | 'incompatibilidade' | 'atencao';
    unidadeOriginal: string;
    unidadeNormalizada: string;
    fatorConversao: number;
    observacao: string;
  };
}

export interface PNCPSearchParams {
  termo: string;
  unidade?: string;
  especificacao?: string;
  valorMinimo?: number;
  valorMaximo?: number;
  dataInicio?: string;
  dataFim?: string;
  orgaoTipo?: 'municipal' | 'estadual' | 'federal';
}

export interface PNCPSearchResult {
  itens: PNCPItem[];
  total: number;
  media: number;
  mediana: number;
  desvio: number;
  filtrosAplicados: string[];
}

/**
 * FILTROS INTELIGENTES PARA REFINAMENTO
 */
class PNCPIntelligentFilter {
  
  /**
   * Extrai especificações numéricas e qualitativas do texto
   * Ex: "álcool gel 70% 500ml" -> { volume: 500, tipo: "gel", concentracao: 70 }
   */
  static extrairEspecificacoes(texto: string): Record<string, any> {
    const specs: Record<string, any> = {};
    const textoLower = texto.toLowerCase();

    // Volumes (ml, l, litros)
    const volumeMatch = texto.match(/(\d+)\s*(ml|l|litros?)/i);
    if (volumeMatch) {
      let volume = parseInt(volumeMatch[1]);
      const unidade = volumeMatch[2].toLowerCase();

      // Converter para ml para padronização
      if (unidade.includes('l') && !unidade.includes('ml')) {
        volume *= 1000;
      }
      specs.volume = volume;
    }

    // Peso (g, kg, gramas, quilos)
    const pesoMatch = texto.match(/(\d+)\s*(g|kg|gramas?|quilos?)/i);
    if (pesoMatch) {
      let peso = parseInt(pesoMatch[1]);
      const unidade = pesoMatch[2].toLowerCase();

      // Converter para gramas
      if (unidade.includes('kg') || unidade.includes('quilo')) {
        peso *= 1000;
      }
      specs.peso = peso;
    }

    // TIPO DE PRODUTO (líquido/gel/spray/etc)
    if (textoLower.includes('gel')) {
      specs.tipo = 'gel';
    } else if (textoLower.includes('líquido') || textoLower.includes('liquido')) {
      specs.tipo = 'liquido';
    } else if (textoLower.includes('spray')) {
      specs.tipo = 'spray';
    } else if (textoLower.includes('espuma')) {
      specs.tipo = 'espuma';
    }

    // CONCENTRAÇÃO DE ÁLCOOL (70%, 46%, etc)
    const concentracaoMatch = texto.match(/(\d+)%/);
    if (concentracaoMatch && textoLower.includes('álcool') || textoLower.includes('alcool')) {
      specs.concentracaoAlcool = parseInt(concentracaoMatch[1]);
    }

    // CONCENTRAÇÃO GERAL (para outros produtos)
    const concentracaoGeralMatch = texto.match(/(\d+(?:\.\d+)?)\s*%/);
    if (concentracaoGeralMatch) {
      specs.concentracao = parseFloat(concentracaoGeralMatch[1]);
    }

    // Dimensões (cm, m, metros)
    const dimensaoMatch = texto.match(/(\d+)\s*x\s*(\d+)(?:\s*x\s*(\d+))?\s*(cm|m|metros?)/i);
    if (dimensaoMatch) {
      specs.largura = parseInt(dimensaoMatch[1]);
      specs.altura = parseInt(dimensaoMatch[2]);
      if (dimensaoMatch[3]) specs.profundidade = parseInt(dimensaoMatch[3]);
      specs.unidadeDimensao = dimensaoMatch[4];
    }

    // Potência (w, watts, kw)
    const potenciaMatch = texto.match(/(\d+)\s*(w|watts?|kw)/i);
    if (potenciaMatch) {
      let potencia = parseInt(potenciaMatch[1]);
      if (potenciaMatch[2].toLowerCase().includes('kw')) {
        potencia *= 1000;
      }
      specs.potencia = potencia;
    }

    return specs;
  }
  
  /**
   * Calcula score de relevância entre item buscado e encontrado
   * AGORA COM DETECÇÃO DE TIPO E CONCENTRAÇÃO
   */
  static calcularRelevancia(termoBusca: string, itemEncontrado: PNCPItem): number {
    let score = 0;

    const specsBusca = this.extrairEspecificacoes(termoBusca);
    const specsItem = this.extrairEspecificacoes(itemEncontrado.descricao);

    // Score base por palavras-chave
    const palavrasBusca = termoBusca.toLowerCase().split(' ');
    const descricaoItem = itemEncontrado.descricao.toLowerCase();

    palavrasBusca.forEach(palavra => {
      if (descricaoItem.includes(palavra)) {
        score += 20;
      }
    });

    // VERIFICAR TIPO (gel/líquido/spray) - CRÍTICO!
    if (specsBusca.tipo && specsItem.tipo) {
      if (specsBusca.tipo === specsItem.tipo) {
        score += 40; // Bonus alto para tipo correto
      } else {
        score -= 60; // Penalidade alta para tipo errado
      }
    }

    // VERIFICAR CONCENTRAÇÃO DE ÁLCOOL - CRÍTICO!
    if (specsBusca.concentracaoAlcool && specsItem.concentracaoAlcool) {
      if (specsBusca.concentracaoAlcool === specsItem.concentracaoAlcool) {
        score += 35; // Bonus alto para concentração correta
      } else {
        score -= 50; // Penalidade alta para concentração errada
      }
    }

    // VERIFICAR VOLUME - MUITO IMPORTANTE!
    if (specsBusca.volume && specsItem.volume) {
      const diferenca = Math.abs(specsBusca.volume - specsItem.volume) / specsBusca.volume;
      if (diferenca === 0) {
        score += 40; // Volume exato
      } else if (diferenca < 0.1) {
        score += 25; // Volume muito próximo
      } else if (diferenca < 0.3) {
        score += 10; // Volume próximo
      } else {
        score -= 40; // Volume muito diferente
      }
    }

    // VERIFICAR PESO
    if (specsBusca.peso && specsItem.peso) {
      const diferenca = Math.abs(specsBusca.peso - specsItem.peso) / specsBusca.peso;
      if (diferenca < 0.1) {
        score += 20;
      } else if (diferenca > 0.3) {
        score -= 30;
      }
    }

    // Bonus para órgãos municipais similares
    if (itemEncontrado.orgao.toLowerCase().includes('prefeitura') ||
        itemEncontrado.orgao.toLowerCase().includes('municipal')) {
      score += 15;
    }

    return Math.max(0, Math.min(100, score));
  }
  
  /**
   * Filtra itens irrelevantes
   */
  static filtrarRelevantes(itens: PNCPItem[], termoBusca: string, limiteRelevancia = 40): PNCPItem[] {
    return itens
      .map(item => ({
        ...item,
        relevancia: this.calcularRelevancia(termoBusca, item)
      }))
      .filter(item => item.relevancia >= limiteRelevancia)
      .sort((a, b) => b.relevancia - a.relevancia);
  }
}

/**
 * CLASSE PRINCIPAL DA API PNCP
 */
export class PNCPApi {
  private static readonly BASE_URL = 'https://pncp.gov.br/api';
  
  /**
   * Busca inteligente no PNCP
   */
  static async buscarItens(params: PNCPSearchParams): Promise<PNCPSearchResult> {
    try {
      // Simular chamada real da API PNCP
      const response = await this.simularBuscaPNCP(params);

      // Aplicar filtros inteligentes
      const itensFiltrados = PNCPIntelligentFilter.filtrarRelevantes(
        response.itens,
        params.termo
      );

      // 🧠 PROCESSAR ALERTAS DE UNIDADES
      const itensComAlertas = this.processarAlertasUnidades(itensFiltrados, params.termo);

      // Calcular estatísticas
      const precos = itensComAlertas.map(item => item.preco);
      const media = precos.reduce((a, b) => a + b, 0) / precos.length;
      const mediana = this.calcularMediana(precos);
      const desvio = this.calcularDesvioPadrao(precos, media);

      return {
        itens: itensComAlertas,
        total: itensComAlertas.length,
        media,
        mediana,
        desvio,
        filtrosAplicados: this.gerarFiltrosAplicados(params)
      };
      
    } catch (error) {
      console.error('Erro na busca PNCP:', error);
      throw new Error('Falha na consulta ao PNCP');
    }
  }
  
  /**
   * 🎭 SIMULAÇÃO INTELIGENTE (REALISTA) DA API PNCP
   * Seu amigo estava certo - API PNCP é muito pesada para frontend!
   */
  private static async simularBuscaPNCP(params: PNCPSearchParams): Promise<{itens: PNCPItem[]}> {
    try {
      console.log('🌐 Chamando API PNCP via backend...');

      const response = await fetch('/api/pncp/buscar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params)
      });

      if (!response.ok) {
        throw new Error(`Erro na API: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Resposta recebida do backend:', data);

      return { itens: data.itens || [] };

    } catch (error) {
      console.error('❌ Erro na busca PNCP:', error);
      return { itens: [] };
    }
  }

  /**
   * Fallback removido - apenas dados reais do PNCP
   */
  private static getDadosSimulados(params: PNCPSearchParams): {itens: PNCPItem[]} {
    console.log('⚠️ Fallback desabilitado - apenas dados reais do PNCP');
    return { itens: [] };
  }

  /**
   * Transformar dados da API PNCP para nosso formato
   */
  private static transformarDadosPNCP(dadosPNCP: any[]): PNCPItem[] {
    if (!Array.isArray(dadosPNCP)) {
      console.warn('⚠️ Dados PNCP não são array:', dadosPNCP);
      return [];
    }

    return dadosPNCP.map((item, index) => {
      // Mapear campos da API PNCP real
      const pncpItem: PNCPItem = {
        id: item.numeroControlePNCP || item.id || `pncp_${index}`,
        descricao: item.objetoContrato || item.objeto || item.descricao || '',
        unidade: item.unidadeMedida || item.unidade || 'UN',
        preco: this.extrairValor(item.valorTotalEstimado || item.valor || item.preco || 0),
        fornecedor: item.razaoSocialFornecedor || item.fornecedor || item.empresa || '',
        cnpj: item.cnpjFornecedor || item.cnpj || '',
        dataContrato: this.formatarData(item.dataAssinatura || item.dataContrato || item.data),
        orgao: item.nomeOrgaoEntidade || item.orgao || item.entidade || '',
        numeroContrato: item.numeroContrato || item.numero || '',
        especificacao: item.especificacaoTecnica || item.especificacao || item.objetoContrato || '',
        relevancia: 0
      };

      return pncpItem;
    }).filter(item => item.descricao && item.preco > 0);
  }

  /**
   * Extrair valor numérico
   */
  private static extrairValor(valor: any): number {
    if (typeof valor === 'number') return valor;
    if (typeof valor === 'string') {
      const numero = parseFloat(valor.replace(/[^\d.,]/g, '').replace(',', '.'));
      return isNaN(numero) ? 0 : numero;
    }
    return 0;
  }

  /**
   * Formatar data
   */
  private static formatarData(data: any): string {
    if (!data) return new Date().toISOString().split('T')[0];

    try {
      const dataObj = new Date(data);
      return dataObj.toISOString().split('T')[0];
    } catch {
      return new Date().toISOString().split('T')[0];
    }
  }
  
  /**
   * Calcula mediana
   */
  private static calcularMediana(valores: number[]): number {
    const sorted = [...valores].sort((a, b) => a - b);
    const meio = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[meio - 1] + sorted[meio]) / 2;
    }
    return sorted[meio];
  }
  
  /**
   * Calcula desvio padrão
   */
  private static calcularDesvioPadrao(valores: number[], media: number): number {
    const variancia = valores.reduce((acc, val) => acc + Math.pow(val - media, 2), 0) / valores.length;
    return Math.sqrt(variancia);
  }
  
  /**
   * Gera lista de filtros aplicados
   */
  private static gerarFiltrosAplicados(params: PNCPSearchParams): string[] {
    const filtros: string[] = [];
    
    if (params.termo) filtros.push(`Termo: "${params.termo}"`);
    if (params.unidade) filtros.push(`Unidade: ${params.unidade}`);
    if (params.valorMinimo) filtros.push(`Valor mínimo: R$ ${params.valorMinimo}`);
    if (params.valorMaximo) filtros.push(`Valor máximo: R$ ${params.valorMaximo}`);
    if (params.orgaoTipo) filtros.push(`Tipo de órgão: ${params.orgaoTipo}`);
    
    filtros.push('Filtro de relevância aplicado');
    
    return filtros;
  }
  
  /**
   * 🧠 PROCESSAR ALERTAS DE UNIDADES
   */
  static processarAlertasUnidades(itens: PNCPItem[], termoBusca: string): PNCPItem[] {
    const unidadeDesejada = IAConversorMedidas.detectarUnidade(termoBusca);

    return itens.map(item => {
      const unidadeItem = IAConversorMedidas.detectarUnidade(item.descricao);

      if (!unidadeDesejada || !unidadeItem) {
        return item;
      }

      // Mesmo tipo mas unidades diferentes
      if (unidadeDesejada.tipo === unidadeItem.tipo &&
          unidadeDesejada.unidade !== unidadeItem.unidade) {

        const fatorConversao = unidadeDesejada.valor / unidadeItem.valor;

        return {
          ...item,
          alertaUnidade: {
            tipo: 'conversao' as const,
            unidadeOriginal: unidadeItem.unidadeOriginal,
            unidadeNormalizada: unidadeDesejada.unidadeOriginal,
            fatorConversao,
            observacao: fatorConversao > 1
              ? `⚠️ Unidade menor: precisa ${fatorConversao.toFixed(2)}x mais`
              : `✅ Unidade maior: cada uma = ${(1/fatorConversao).toFixed(2)} desejadas`
          }
        };
      }

      // Tipos incompatíveis
      else if (unidadeDesejada.tipo !== unidadeItem.tipo) {
        return {
          ...item,
          alertaUnidade: {
            tipo: 'incompatibilidade' as const,
            unidadeOriginal: unidadeItem.unidadeOriginal,
            unidadeNormalizada: unidadeDesejada.unidadeOriginal,
            fatorConversao: 0,
            observacao: `🚫 Incompatível: ${unidadeItem.tipo} vs ${unidadeDesejada.tipo}`
          }
        };
      }

      return item;
    });
  }

  /**
   * Aplica critério do Decreto 9337/24 (remove ±50%)
   */
  static aplicarCriterioDecreto(itens: PNCPItem[]): PNCPItem[] {
    if (itens.length < 3) return itens; // Precisa de pelo menos 3 itens

    const precos = itens.map(item => item.preco).sort((a, b) => a - b);
    const media = precos.reduce((a, b) => a + b, 0) / precos.length;

    const limiteInferior = media * 0.5;
    const limiteSuperior = media * 1.5;

    return itens.map(item => ({
      ...item,
      excluido: item.preco < limiteInferior || item.preco > limiteSuperior,
      motivoExclusao: item.preco < limiteInferior ?
        'Preço 50% abaixo da média' :
        item.preco > limiteSuperior ?
        'Preço 50% acima da média' :
        undefined
    }));
  }
}
