'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Processo } from '@/types/processo';
import {
  ArrowLeft,
  Calendar,
  Building,
  User,
  FileText,
  DollarSign,
  AlertCircle,
  Loader2,
  Printer,
  Clock,
  MapPin,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProcessoResponse {
  success: boolean;
  data: Processo;
  error?: string;
}

export default function ProcessoDetalhesPage() {
  const params = useParams();
  const [processo, setProcesso] = useState<Processo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProcesso = async () => {
      // Aguardar params se necessário (Next.js 15)
      const resolvedParams = await Promise.resolve(params);
      if (!resolvedParams.id) return;

      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/processos/${encodeURIComponent(resolvedParams.id as string)}`);
        const data: ProcessoResponse = await response.json();

        if (data.success) {
          setProcesso(data.data);
        } else {
          setError(data.error || 'Processo não encontrado');
        }
      } catch (err) {
        setError('Erro de conexão. Tente novamente.');
        console.error('Erro ao carregar processo:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProcesso();
  }, [params]);

  // Função para determinar a cor do status (com suporte ao dark mode)
  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();

    if (statusLower.includes('concluído') || statusLower.includes('finalizado')) {
      return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-green-200 dark:border-green-700';
    }
    if (statusLower.includes('andamento') || statusLower.includes('aberta')) {
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700';
    }
    if (statusLower.includes('cancelado') || statusLower.includes('suspenso')) {
      return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-red-200 dark:border-red-700';
    }
    if (statusLower.includes('aguardando') || statusLower.includes('análise') || statusLower.includes('parecer') || statusLower.includes('prosseguimento')) {
      return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-700';
    }

    return 'bg-gray-100 dark:bg-gray-800/50 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-600';
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    return value;
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    return dateString;
  };

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = (processo: Processo) => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  // Função para calcular dias entre duas datas
  const calcularDiasEntreDatas = (dataInicio: string, dataFim: string) => {
    try {
      const [diaI, mesI, anoI] = dataInicio.split('/');
      const [diaF, mesF, anoF] = dataFim.split('/');
      const inicio = new Date(parseInt(anoI), parseInt(mesI) - 1, parseInt(diaI));
      const fim = new Date(parseInt(anoF), parseInt(mesF) - 1, parseInt(diaF));
      const diff = Math.floor((fim.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24));
      return diff;
    } catch {
      return 0;
    }
  };

  // Função para calcular data estimada em DIAS ÚTEIS
  const calcularDataUtilEstimada = (dataInicio: Date, diasUteis: number): string => {
    const data = new Date(dataInicio);
    let diasAdicionados = 0;

    while (diasAdicionados < diasUteis) {
      data.setDate(data.getDate() + 1);

      // Verificar se é dia útil (segunda a sexta)
      const diaSemana = data.getDay();
      if (diaSemana >= 1 && diaSemana <= 5) {
        diasAdicionados++;
      }
    }

    return data.toLocaleDateString('pt-BR');
  };

  // Função para analisar TODO o histórico e calcular tempos corretos
  const analisarHistoricoCompleto = async (numeroProcesso: string) => {
    try {
      // Buscar TODAS as movimentações do processo em TODOS os CSVs
      const response = await fetch(`/api/processos/historico/${numeroProcesso}`);
      if (!response.ok) {
        console.log('⚠️ API de histórico não disponível, usando dados atuais');
        return calcularTemposSimples();
      }

      const historico = await response.json();
      return calcularTemposDetalhados(historico);
    } catch (error) {
      console.log('⚠️ Erro ao buscar histórico, usando cálculo simples:', error);
      return calcularTemposSimples();
    }
  };

  // Cálculo simples (atual) - TEMPORÁRIO até implementar API de histórico
  const calcularTemposSimples = () => {
    let tempoNaCLMP = 0;
    let tempoForaCLMP = 0;
    let tempoTotal = 0;
    const houveEncaminhamentoDireto = detectarEncaminhamentoDireto(processo);

    // Calcular tempo total (da abertura até hoje)
    if (processo['DATA DE INÍCIO DO PROCESSO']) {
      const hoje = new Date();
      const dataInicio = processo['DATA DE INÍCIO DO PROCESSO'];
      tempoTotal = calcularDiasEntreDatas(dataInicio, hoje.toLocaleDateString('pt-BR'));
    }

    // Tempo inicial na secretaria (antes de chegar na CLMP)
    if (processo['DATA DE INÍCIO DO PROCESSO'] && processo['DATA ENTRADA NA CLMP']) {
      const diasInicialSecretaria = calcularDiasEntreDatas(
        processo['DATA DE INÍCIO DO PROCESSO'],
        processo['DATA ENTRADA NA CLMP']
      );

      if (houveEncaminhamentoDireto) {
        // Se houve encaminhamento direto, a maior parte do tempo foi em análise externa (SF/SAJ)
        // Estimar que apenas 20% foi realmente na secretaria, 80% foi em análise externa
        tempoForaCLMP += Math.round(diasInicialSecretaria * 0.2); // Tempo real na secretaria
        // Os outros 80% foram em SF/SAJ (não contar como tempo da CLMP)
      } else {
        tempoForaCLMP += diasInicialSecretaria;
      }
    }

    // Tempo na CLMP (entrada até última movimentação ou hoje)
    if (processo['DATA ENTRADA NA CLMP']) {
      const hoje = new Date();
      const dataFinal = processo.DATA || hoje.toLocaleDateString('pt-BR');
      const totalDias = calcularDiasEntreDatas(processo['DATA ENTRADA NA CLMP'], dataFinal);

      // CORREÇÃO: Se processo está fora da CLMP, não contar todo o tempo como "na CLMP"
      if (processo.LOCAL && !processo.LOCAL.includes('CLMP')) {
        if (houveEncaminhamentoDireto) {
          // Se houve encaminhamento direto, o processo pode ter voltado da análise externa
          // Estimar tempo real na CLMP de forma mais conservadora
          tempoNaCLMP = Math.round(totalDias * 0.2);
          tempoForaCLMP += Math.round(totalDias * 0.8);
        } else {
          // Processo está fora - estimar tempo real na CLMP (30% do total como estimativa)
          tempoNaCLMP = Math.round(totalDias * 0.3);
          tempoForaCLMP += Math.round(totalDias * 0.7);
        }
      } else {
        // Processo ainda na CLMP
        tempoNaCLMP = totalDias;
      }
    }

    const detalhamento = [
      { local: 'Secretaria (inicial)', dias: Math.round(tempoForaCLMP * 0.3), tipo: 'fora' as const },
      { local: 'CLMP', dias: tempoNaCLMP, tipo: 'dentro' as const }
    ];

    if (houveEncaminhamentoDireto) {
      detalhamento.push({
        local: `Análise Externa (${processo.LOCAL})`,
        dias: Math.round(tempoForaCLMP * 0.7),
        tipo: 'fora' as const
      });
    } else {
      detalhamento[0].dias = tempoForaCLMP;
    }

    return {
      tempoTotal,
      tempoNaCLMP,
      tempoForaCLMP,
      detalhamento,
      encaminhamentoDireto: houveEncaminhamentoDireto
    };
  };

  // Cálculo detalhado baseado em histórico completo - FUTURO
  const calcularTemposDetalhados = (historico: any[]) => {
    let tempoNaCLMP = 0;
    let tempoForaCLMP = 0;
    const detalhamento: Array<{local: string, dias: number, tipo: 'dentro' | 'fora'}> = [];

    // Analisar cada movimentação no histórico
    for (let i = 0; i < historico.length - 1; i++) {
      const atual = historico[i];
      const proximo = historico[i + 1];

      const dias = calcularDiasEntreDatas(atual.data, proximo.data);

      if (atual.local.includes('CLMP')) {
        tempoNaCLMP += dias;
        detalhamento.push({ local: atual.local, dias, tipo: 'dentro' });
      } else {
        tempoForaCLMP += dias;
        detalhamento.push({ local: atual.local, dias, tipo: 'fora' });
      }
    }

    return { tempoNaCLMP, tempoForaCLMP, detalhamento };
  };

  // Função para imprimir relatório completo
  const imprimirRelatorio = () => {
    const tempos = calcularTemposSimples();
    const cronologia = criarCronologiaDetalhada(processo);

    const conteudoImpressao = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Relatório do Processo ${processo.PROCESSO}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
          .section { margin-bottom: 20px; }
          .section h3 { background: #f5f5f5; padding: 8px; margin: 0 0 10px 0; }
          .cronologia { border-left: 3px solid #007bff; padding-left: 15px; }
          .evento { margin-bottom: 15px; }
          .tempo-resumo { display: flex; gap: 20px; }
          .tempo-card { border: 1px solid #ddd; padding: 10px; flex: 1; }
          .tempo-clmp { border-color: #28a745; }
          .tempo-fora { border-color: #dc3545; }
          .dados-compactos { margin-top: 10px; }
          .linha-dados { display: flex; gap: 20px; margin-bottom: 8px; flex-wrap: wrap; }
          .linha-dados.objeto { flex-direction: column; }
          .linha-dados span { flex: 1; min-width: 200px; }
          .linha-dados.objeto span { flex: none; width: 100%; }
          table { width: 100%; border-collapse: collapse; margin-top: 10px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f5f5f5; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>RELATÓRIO DE PROCESSO</h1>
          <h2>Processo: ${processo.PROCESSO}</h2>
          <p>Gerado em: ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}</p>
        </div>

        <div class="section">
          <h3>📋 DADOS GERAIS</h3>
          <div class="dados-compactos">
            <div class="linha-dados">
              <span><strong>Processo:</strong> ${processo.PROCESSO}</span>
              <span><strong>Requisitante:</strong> ${processo.REQUISITANTE || 'Não informado'}</span>
              <span><strong>Modalidade:</strong> ${processo.MODALIDADE || 'Não informado'}</span>
              <span><strong>Prioridade:</strong> ${processo.PRIORIDADE || 'Normal'}</span>
            </div>
            <div class="linha-dados objeto">
              <span><strong>Objeto:</strong> ${processo.OBJETO || 'Não informado'}</span>
            </div>
            <div class="linha-dados">
              <span><strong>Status Atual:</strong> ${processo.STATUS || 'Não informado'}</span>
            </div>
            <div class="linha-dados">
              <span><strong>Localização:</strong> ${processo.LOCAL || 'Não informado'}</span>
              <span><strong>Responsável:</strong> ${processo.RESPONSÁVEL || 'Não informado'}</span>
            </div>
          </div>
        </div>

        <div class="section">
          <h3>⏱️ RESUMO DE TEMPOS</h3>
          <div class="tempo-resumo">
            <div class="tempo-card" style="border-color: #007bff;">
              <h4>Tempo Total</h4>
              <p><strong>${tempos.tempoTotal} dias</strong></p>
              <p>Da abertura até hoje</p>
            </div>
            <div class="tempo-card tempo-clmp">
              <h4>Tempo na CLMP</h4>
              <p><strong>${tempos.tempoNaCLMP} dias</strong></p>
              <p>${tempos.tempoTotal > 0 ? Math.round((tempos.tempoNaCLMP / tempos.tempoTotal) * 100) : 0}% do tempo total</p>
            </div>
            <div class="tempo-card tempo-fora">
              <h4>Tempo Fora da CLMP</h4>
              <p><strong>${tempos.tempoForaCLMP} dias</strong></p>
              <p>${tempos.tempoTotal > 0 ? Math.round((tempos.tempoForaCLMP / tempos.tempoTotal) * 100) : 0}% do tempo total</p>
            </div>
          </div>
        </div>

        <div class="section">
          <h3>📊 CRONOLOGIA COMPLETA</h3>
          <div class="cronologia">
            ${cronologia.map(evento => `
              <div class="evento">
                <strong>${formatDate(evento.data)}</strong> - ${evento.evento}
                <br><small>Local: ${evento.local}</small>
                ${evento.diasProximo ? `<br><small>Próximo evento em: ${evento.diasProximo} dias</small>` : ''}
              </div>
            `).join('')}
          </div>
        </div>

        <div class="section">
          <h3>💰 INFORMAÇÕES FINANCEIRAS</h3>
          <table>
            <tr><td><strong>Valor Estimado:</strong></td><td>${processo['VALOR ESTIMADO'] || 'Não informado'}</td></tr>
            <tr><td><strong>Tesouro (0001):</strong></td><td>${processo['Fonte 0001 (TESOURO)'] || '-'}</td></tr>
            <tr><td><strong>Estadual (0002):</strong></td><td>${processo['Fonte 0002 (ESTADUAL)'] || '-'}</td></tr>
            <tr><td><strong>Federal (0005):</strong></td><td>${processo['Fonte 0005 (FEDERAL)'] || '-'}</td></tr>
            <tr><td><strong>FINISA (0007):</strong></td><td>${processo['Fonte 0007 (FINISA)'] || '-'}</td></tr>
          </table>
        </div>

        <div class="section">
          <h3>📄 INFORMAÇÕES DO CERTAME</h3>
          <table>
            <tr><td><strong>Nº do Certame:</strong></td><td>${processo['Nº DO CERTAME'] || 'Não iniciado'}</td></tr>
            <tr><td><strong>Data Publicação:</strong></td><td>${processo['DATA PUBLICAÇÃO'] ? formatDate(processo['DATA PUBLICAÇÃO']) : 'Não publicado'}</td></tr>
            <tr><td><strong>Data Abertura:</strong></td><td>${processo['DATA ABERTURA'] ? formatDate(processo['DATA ABERTURA']) : 'Não definida'}</td></tr>
          </table>
        </div>

        <div class="section">
          <p><small>Relatório gerado pelo Sistema InovaProcess - CLMP</small></p>
        </div>
      </body>
      </html>
    `;

    const novaJanela = window.open('', '_blank');
    if (novaJanela) {
      novaJanela.document.write(conteudoImpressao);
      novaJanela.document.close();
      novaJanela.print();
    }
  };

  // Função para detectar encaminhamento direto do requisitante
  const detectarEncaminhamentoDireto = (processo: Processo) => {
    const status = processo.STATUS?.toLowerCase() || '';
    const local = processo.LOCAL?.toLowerCase() || '';

    const statusEncaminhamentoDireto = [
      'para prosseguimento após impacto financeiro',
      'encaminhado para manifestação - adequação ao pca e disponibilidade orçamentária',
      'para prosseguimento após parecer jurídico',
      'encaminhado para emissão de parecer jurídico'
    ];

    const localSFouSAJ = local.includes('sf') || local.includes('saj');
    const statusIndicaEncaminhamentoDireto = statusEncaminhamentoDireto.some(s => status.includes(s));

    return statusIndicaEncaminhamentoDireto && localSFouSAJ;
  };

  // Função para criar cronologia detalhada
  const criarCronologiaDetalhada = (processo: Processo) => {
    const eventos: Array<{
      data: string;
      evento: string;
      local: string;
      cor: string;
      diasProximo?: number;
      especial?: boolean;
    }> = [];

    if (processo['DATA DE INÍCIO DO PROCESSO']) {
      eventos.push({
        data: processo['DATA DE INÍCIO DO PROCESSO'],
        evento: 'Início do Processo',
        local: 'Secretaria',
        cor: 'bg-blue-500'
      });
    }

    // Verificar se houve encaminhamento direto
    const houveEncaminhamentoDireto = detectarEncaminhamentoDireto(processo);

    if (houveEncaminhamentoDireto && processo['DATA DE INÍCIO DO PROCESSO']) {
      // Adicionar evento estimado de encaminhamento direto
      const dataInicio = processo['DATA DE INÍCIO DO PROCESSO'];
      try {
        const [dia, mes, ano] = dataInicio.split('/');
        const dataInicioObj = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

        // Calcular data estimada em DIAS ÚTEIS (3-5 dias úteis após início)
        const dataEstimada = calcularDataUtilEstimada(dataInicioObj, 3);

        eventos.push({
          data: dataEstimada,
          evento: `⚠️ ENCAMINHAMENTO DIRETO pelo requisitante para ${processo.LOCAL} (data estimada - dias úteis)`,
          local: processo.REQUISITANTE || 'Secretaria',
          cor: 'bg-orange-500',
          especial: true
        });
      } catch (error) {
        // Se não conseguir calcular a data, usar a data de início
        eventos.push({
          data: dataInicio,
          evento: `⚠️ ENCAMINHAMENTO DIRETO pelo requisitante para ${processo.LOCAL} (data estimada)`,
          local: processo.REQUISITANTE || 'Secretaria',
          cor: 'bg-orange-500',
          especial: true
        });
      }
    }

    if (processo['DATA ENTRADA NA CLMP']) {
      eventos.push({
        data: processo['DATA ENTRADA NA CLMP'],
        evento: houveEncaminhamentoDireto ? 'Retorno à CLMP após análise externa' : 'Entrada na CLMP',
        local: 'CLMP',
        cor: houveEncaminhamentoDireto ? 'bg-yellow-500' : 'bg-green-500'
      });
    }

    if (processo['DATA PUBLICAÇÃO']) {
      eventos.push({
        data: processo['DATA PUBLICAÇÃO'],
        evento: 'Publicação do Edital',
        local: 'CLMP',
        cor: 'bg-purple-500'
      });
    }

    if (processo['DATA ABERTURA']) {
      // Verificar se a data de abertura é futura
      const hoje = new Date();
      const dataAbertura = processo['DATA ABERTURA'];
      const [dia, mes, ano] = dataAbertura.split('/');
      const dataAberturaObj = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      if (dataAberturaObj > hoje) {
        // Data futura - calcular dias até a abertura
        const diasAteAbertura = Math.ceil((dataAberturaObj.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24));
        eventos.push({
          data: dataAbertura,
          evento: `Abertura da Licitação (em ${diasAteAbertura} dias)`,
          local: 'CLMP',
          cor: 'bg-orange-500'
        });
      } else {
        // Data passada
        eventos.push({
          data: dataAbertura,
          evento: 'Abertura da Licitação',
          local: 'CLMP',
          cor: 'bg-orange-500'
        });
      }
    }

    // Status atual sempre com data de hoje (data da impressão)
    const hoje = new Date().toLocaleDateString('pt-BR');
    eventos.push({
      data: hoje,
      evento: `Status Atual: ${processo.STATUS}`,
      local: processo.LOCAL || 'CLMP',
      cor: 'bg-yellow-500'
    });

    // Calcular dias entre eventos (corrigido)
    for (let i = 0; i < eventos.length - 1; i++) {
      const dias = calcularDiasEntreDatas(eventos[i].data, eventos[i + 1].data);
      eventos[i].diasProximo = dias;
    }

    return eventos;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Carregando detalhes do processo...</p>
        </div>
      </div>
    );
  }

  if (error || !processo) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>

        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium text-destructive mb-2">
            Erro ao carregar processo
          </h3>
          <p className="text-destructive/80 mb-4">{error}</p>
          <Link
            href="/processos"
            className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors"
          >
            Voltar para lista
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>

        {/* Botão de Impressão */}
        <Button
          onClick={imprimirRelatorio}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Printer className="mr-2 h-4 w-4" />
          Imprimir Relatório
        </Button>
      </div>

      {/* Título e Status */}
      <div className="bg-background rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              {processo.PROCESSO || 'Processo sem número'}
            </h1>
            <p className="text-muted-foreground">
              Item: {processo.ITEM || 'Não informado'}
            </p>
          </div>

          {processo.STATUS && (
            <div className="mt-4 sm:mt-0">
              <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(processo.STATUS)}`}>
                {processo.STATUS}
              </span>
            </div>
          )}
        </div>

        {/* Alerta de Encaminhamento Direto */}
        {detectarEncaminhamentoDireto(processo) && (
          <div className="border-t pt-4 mb-4">
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    ⚠️ ENCAMINHAMENTO DIRETO DETECTADO
                  </h3>
                  <div className="mt-2 text-sm text-orange-700 dark:text-orange-300">
                    <p>Este processo parece ter sido encaminhado diretamente pelo requisitante para <strong>{processo.LOCAL}</strong> sem tramitar pela CLMP.</p>
                    <p className="mt-1">
                      <strong>Ação necessária:</strong> Confirmar se houve encaminhamento direto e registrar na cronologia. Qualquer usuário da CLMP pode confirmar esta informação.
                    </p>
                    <p className="mt-1 text-xs">
                      <strong>Quando confirmar:</strong> Ao dar entrada, ao processar retorno, ou ao detectar a situação.
                    </p>
                  </div>
                  <div className="mt-3 flex items-center text-xs text-orange-600 dark:text-orange-400">
                    <span className="font-medium">Requisitante:</span>
                    <span className="ml-1 font-bold">{processo.REQUISITANTE}</span>
                    <span className="mx-2">•</span>
                    <span className="font-medium">Status:</span>
                    <span className="ml-1">{processo.STATUS}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tempo no setor atual */}
        {calcularTempoNoSetor(processo) && (
          <div className="border-t pt-4 mb-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="mr-3 text-yellow-600 dark:text-yellow-400" size={20} />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">Tempo no Setor Atual</h3>
                    <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                      {calcularTempoNoSetor(processo)}
                    </p>
                    <div className="flex items-center gap-6 text-sm text-yellow-700 dark:text-yellow-300">
                      {/* Grupo 1: Local atual e Responsável */}
                      <div className="flex items-center gap-2">
                        <span><strong>Local atual:</strong> {processo.LOCAL || 'Não informado'}</span>
                        <span><strong>Responsável:</strong> {processo.RESPONSÁVEL || 'Não informado'}</span>
                      </div>

                      {/* Grupo 2: Secretaria requisitante e Valor estimado */}
                      <div className="flex items-center gap-2 ml-4">
                        {processo.REQUISITANTE && (
                          <span><strong>Secretaria:</strong> {processo.REQUISITANTE}</span>
                        )}
                        {processo['VALOR ESTIMADO'] && processo['VALOR ESTIMADO'] !== '-' && (
                          <span><strong>Estimado:</strong> {processo['VALOR ESTIMADO']}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                {processo['DATA DE INÍCIO DO PROCESSO'] && (
                  <div className="text-right">
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">Processo Aberto Há</h3>
                    <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                      {(() => {
                        try {
                          const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
                          const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
                          const agora = new Date();
                          const diffMs = agora.getTime() - dataInicio.getTime();
                          const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                          return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
                        } catch {
                          return 'N/A';
                        }
                      })()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Objeto - com scroll interno */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">OBJETO</h3>
          <div className="max-h-20 overflow-y-auto bg-muted/30 rounded p-3">
            <p className="text-foreground leading-relaxed">
              {processo.OBJETO || 'Objeto não informado'}
            </p>
          </div>
        </div>
      </div>

      {/* Informações Principais */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informações Gerais */}
        <div className="bg-background rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <FileText className="mr-2" size={20} />
            Informações Gerais
          </h2>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            <div className="flex items-center">
              <Building className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Secretaria Requisitante</span>
                <p className="font-medium text-foreground">{processo.REQUISITANTE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <FileText className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Modalidade</span>
                <p className="font-medium text-foreground">{processo.MODALIDADE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <User className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Responsável Atual</span>
                <p className="font-medium text-foreground">{processo.RESPONSÁVEL || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <Building className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Local Atual</span>
                <p className="font-medium text-foreground">{processo.LOCAL || 'Não informado'}</p>
              </div>
            </div>

            {processo.PRIORIDADE && (
              <div className="flex items-center">
                <div className="mr-3 text-muted-foreground">⚡</div>
                <div>
                  <span className="text-sm text-muted-foreground">Prioridade</span>
                  <p className="font-medium text-foreground">{processo.PRIORIDADE}</p>
                </div>
              </div>
            )}

            {processo['Nº DO CERTAME'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Número do Certame</span>
                  <p className="font-medium text-foreground">{processo['Nº DO CERTAME']}</p>
                </div>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Número do Contrato</span>
                  <p className="font-medium text-foreground">{processo['CONTRATO NÚMERO']}</p>
                </div>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div className="flex items-center">
                <Calendar className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Vencimento</span>
                  <p className="font-medium text-foreground">{formatDate(processo.VENCIMENTO)}</p>
                </div>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Processo de Gerenciamento</span>
                  <p className="font-medium text-foreground">{processo['PROCESSO DE GERENCIAMENTO']}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Cronologia Completa */}
        <div className="bg-background rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <Calendar className="mr-2" size={20} />
            Cronologia e Tramitação
          </h2>

          <div className="space-y-4">
            {/* Timeline visual com scroll */}
            <div className="max-h-80 overflow-y-auto border border-border/30 rounded-lg p-3 bg-muted/10 scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border"></div>

                {criarCronologiaDetalhada(processo).map((evento, index) => (
                  <div key={index} className="relative flex items-start pb-6 last:pb-0">
                    <div className={`absolute left-2 w-4 h-4 ${evento.cor} rounded-full border-2 border-background shadow z-10`}></div>
                    <div className="ml-10 flex-1">
                      <div className="flex items-center justify-between">
                        <span className={`text-sm font-medium text-foreground ${evento.especial ? 'text-orange-600 dark:text-orange-400' : ''}`}>
                          {evento.evento}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded ${evento.especial ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300' : 'text-muted-foreground bg-muted'}`}>
                          {evento.local}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{formatDate(evento.data)}</p>
                      {evento.especial && (
                        <div className="mt-2 text-xs bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 px-2 py-1 rounded inline-block">
                          ⚠️ Situação especial detectada - Qualquer usuário da CLMP pode confirmar
                        </div>
                      )}
                      {evento.diasProximo !== undefined && evento.diasProximo > 0 && (
                        <div className="mt-2 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded inline-block">
                          ⏱️ {evento.diasProximo} dia{evento.diasProximo > 1 ? 's' : ''} até próximo evento
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Resumo de tempos - com cálculos reais */}
            <div className="mt-6 pt-4 border-t border-border">
              <h3 className="text-sm font-medium text-foreground mb-3">Resumo de Tempos</h3>
              {(() => {
                const tempos = calcularTemposSimples();
                return (
                  <>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-4">
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                        <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">Tempo Total</span>
                        <p className="font-bold text-lg text-blue-800 dark:text-blue-200">
                          {tempos.tempoTotal} dia{tempos.tempoTotal !== 1 ? 's' : ''}
                        </p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Da abertura até hoje</p>
                      </div>

                      <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                        <span className="text-xs text-green-600 dark:text-green-400 font-medium">Tempo na CLMP</span>
                        <p className="font-bold text-lg text-green-800 dark:text-green-200">
                          {tempos.tempoNaCLMP} dia{tempos.tempoNaCLMP !== 1 ? 's' : ''}
                        </p>
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1">{tempos.tempoTotal > 0 ? Math.round((tempos.tempoNaCLMP / tempos.tempoTotal) * 100) : 0}% do total</p>
                      </div>

                      <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                        <span className="text-xs text-red-600 dark:text-red-400 font-medium">Tempo Fora da CLMP</span>
                        <p className="font-bold text-lg text-red-800 dark:text-red-200">
                          {tempos.tempoForaCLMP} dia{tempos.tempoForaCLMP !== 1 ? 's' : ''}
                        </p>
                        <p className="text-xs text-red-600 dark:text-red-400 mt-1">{tempos.tempoTotal > 0 ? Math.round((tempos.tempoForaCLMP / tempos.tempoTotal) * 100) : 0}% do total</p>
                      </div>
                    </div>

                    {/* Detalhamento por local */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                      <h4 className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2">
                        📊 Detalhamento:
                        {tempos.encaminhamentoDireto && (
                          <span className="ml-2 text-orange-600 dark:text-orange-400">⚠️ Encaminhamento Direto</span>
                        )}
                      </h4>
                      <div className="space-y-1">
                        {tempos.detalhamento.map((item, index) => (
                          <div key={index} className="flex justify-between text-xs">
                            <span className={
                              item.tipo === 'dentro'
                                ? 'text-green-700 dark:text-green-300'
                                : item.local.includes('Análise Externa')
                                  ? 'text-orange-700 dark:text-orange-300'
                                  : 'text-red-700 dark:text-red-300'
                            }>
                              {item.local}
                              {item.local.includes('Análise Externa') && (
                                <span className="text-xs ml-1">*</span>
                              )}
                            </span>
                            <span className="font-medium">
                              {item.dias} dia{item.dias !== 1 ? 's' : ''}
                            </span>
                          </div>
                        ))}
                      </div>

                      {tempos.encaminhamentoDireto && (
                        <div className="mt-2 text-xs text-orange-600 dark:text-orange-400">
                          * Tempo em análise externa (SF/SAJ) por encaminhamento direto do requisitante
                        </div>
                      )}

                      {/* Percentual */}
                      <div className="mt-3 pt-2 border-t border-blue-200 dark:border-blue-700">
                        <div className="flex justify-between text-xs font-medium">
                          <span className="text-blue-700 dark:text-blue-300">% do tempo na CLMP:</span>
                          <span className="text-blue-800 dark:text-blue-200">
                            {tempos.tempoTotal > 0
                              ? Math.round((tempos.tempoNaCLMP / tempos.tempoTotal) * 100)
                              : 0}%
                          </span>
                        </div>
                        {tempos.encaminhamentoDireto && (
                          <div className="flex justify-between text-xs font-medium mt-1">
                            <span className="text-orange-700 dark:text-orange-300">% em análise externa:</span>
                            <span className="text-orange-800 dark:text-orange-200">
                              {tempos.tempoTotal > 0
                                ? Math.round(((tempos.tempoForaCLMP * 0.7) / tempos.tempoTotal) * 100)
                                : 0}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                );
              })()}
            </div>
          </div>
        </div>
      </div>

      {/* Valores e Fontes */}
      <div className="bg-background rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <DollarSign className="mr-2" size={20} />
          Informações Financeiras
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {processo['VALOR ESTIMADO'] && processo['VALOR ESTIMADO'] !== '-' && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">Valor Estimado</span>
              <p className="text-lg font-bold text-blue-900 dark:text-blue-100">{formatCurrency(processo['VALOR ESTIMADO'])}</p>
            </div>
          )}

          {processo['VALOR CONTRATADO'] && processo['VALOR CONTRATADO'] !== '-' && (
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <span className="text-sm text-green-600 dark:text-green-400 font-medium">Valor Contratado</span>
              <p className="text-lg font-bold text-green-900 dark:text-green-100">{formatCurrency(processo['VALOR CONTRATADO'])}</p>
            </div>
          )}

          {/* Fontes de Recursos */}
          <div className="md:col-span-2 lg:col-span-1">
            <h3 className="text-sm font-medium text-foreground mb-3">Fontes de Recursos</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {processo['Fonte 0001 (TESOURO)'] && processo['Fonte 0001 (TESOURO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Tesouro:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0001 (TESOURO)'])}</span>
                </div>
              )}
              {processo['Fonte 0002 (ESTADUAL)'] && processo['Fonte 0002 (ESTADUAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Estadual:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0002 (ESTADUAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0003 (FUNDO)'] && processo['Fonte 0003 (FUNDO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Fundo:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0003 (FUNDO)'])}</span>
                </div>
              )}
              {processo['Fonte 0005 (FEDERAL)'] && processo['Fonte 0005 (FEDERAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Federal:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0005 (FEDERAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0007 (FINISA)'] && processo['Fonte 0007 (FINISA)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">FINISA:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0007 (FINISA)'])}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Informações do Certame e Contrato */}
      {(processo['Nº DO CERTAME'] || processo['CONTRATO NÚMERO']) && (
        <div className="bg-background dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-foreground dark:text-white mb-4">
            Certame e Contrato
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {processo['Nº DO CERTAME'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Certame</span>
                <p className="font-medium text-lg">{processo['Nº DO CERTAME']}</p>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Contrato</span>
                <p className="font-medium text-lg">{processo['CONTRATO NÚMERO']}</p>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div>
                <span className="text-sm text-gray-500">Vencimento</span>
                <p className="font-medium">{formatDate(processo.VENCIMENTO)}</p>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div>
                <span className="text-sm text-gray-500">Processo de Gerenciamento</span>
                <p className="font-medium">{processo['PROCESSO DE GERENCIAMENTO']}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

