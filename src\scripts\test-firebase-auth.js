// 🔍 SCRIPT DE DIAGNÓSTICO FIREBASE AUTH
// Este script testa a configuração do Firebase e identifica problemas específicos

console.log('🔍 INICIANDO DIAGNÓSTICO FIREBASE AUTH...\n');

// 1. Verificar variáveis de ambiente
console.log('📋 VERIFICANDO VARIÁVEIS DE AMBIENTE:');
const requiredEnvVars = [
  'NEXT_PUBLIC_FIREBASE_API_KEY',
  'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN', 
  'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
  'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
  'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
  'NEXT_PUBLIC_FIREBASE_APP_ID',
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID'
];

let allEnvVarsPresent = true;
requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar];
  if (value) {
    console.log(`✅ ${envVar}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${envVar}: AUSENTE`);
    allEnvVarsPresent = false;
  }
});

if (!allEnvVarsPresent) {
  console.log('\n🚨 PROBLEMA: Variáveis de ambiente ausentes!');
  process.exit(1);
}

// 2. Verificar configuração do Firebase
console.log('\n🔧 VERIFICANDO CONFIGURAÇÃO FIREBASE:');
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

console.log('Firebase Config:', JSON.stringify(firebaseConfig, null, 2));

// 3. Verificar se authDomain está correto
console.log('\n🌐 VERIFICANDO DOMÍNIO DE AUTH:');
const authDomain = process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN;
if (authDomain === 'inovaprocess.app') {
  console.log('✅ AuthDomain configurado para domínio customizado');
} else if (authDomain?.includes('firebaseapp.com')) {
  console.log('⚠️ AuthDomain usando domínio Firebase padrão');
} else {
  console.log('❌ AuthDomain inválido:', authDomain);
}

// 4. Verificar Client ID do Google
console.log('\n🔑 VERIFICANDO GOOGLE CLIENT ID:');
const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
if (clientId?.includes('apps.googleusercontent.com')) {
  console.log('✅ Google Client ID válido');
} else {
  console.log('❌ Google Client ID inválido:', clientId);
}

// 5. Verificar porta do servidor
console.log('\n🌐 VERIFICANDO CONFIGURAÇÃO DE PORTA:');
console.log('Porta atual do processo:', process.env.PORT || '3000 (padrão)');
console.log('URL esperada para OAuth: http://localhost:3000');

console.log('\n✅ DIAGNÓSTICO CONCLUÍDO!');
console.log('\n📋 PRÓXIMOS PASSOS:');
console.log('1. Verificar se localhost:3000 está autorizado no Google OAuth');
console.log('2. Verificar se inovaprocess.app está autorizado no Firebase');
console.log('3. Testar login no navegador');
