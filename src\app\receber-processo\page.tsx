'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Download, FileText, Calendar, User, Building, CheckCircle, AlertCircle, Clock } from 'lucide-react';

export default function ReceberProcessoPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [processoExistente, setProcessoExistente] = useState<any>(null);
  const [isNovoProcesso, setIsNovoProcesso] = useState(false);
  const [locaisDisponiveis, setLocaisDisponiveis] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    numeroProcesso: '',
    origem: '',
    responsavel: '',
    dataRecebimento: new Date().toISOString().split('T')[0],
    horaRecebimento: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
    observacoes: '',
    prioridade: 'normal',
    objeto: '',
    modalidade: '',
    valorEstimado: '',
    novoStatus: '',
    novoLocal: '',
    // Campos completos do CSV
    dataInicio: new Date().toISOString().split('T')[0],
    dataEntradaCLMP: new Date().toISOString().split('T')[0],
    // Fontes de recursos
    fonteTesouro: '',
    fonteEstadual: '',
    fonteFundo: '',
    fonteFederal: '',
    fonteFinisa: '',
    // Datas de vencimento das fontes não-tesouro
    dataVencimentoEstadual: '',
    dataVencimentoFundo: '',
    dataVencimentoFederal: '',
    dataVencimentoFinisa: '',
    // Campos de licitação
    numeroCertame: '',
    dataPublicacao: '',
    dataAbertura: '',
    valorContratado: '',
    contratoNumero: '',
    vencimento: '',
    processoGerenciamento: ''
  });

  // Carregar locais disponíveis ao montar o componente
  useEffect(() => {
    const carregarLocais = async () => {
      try {
        const response = await fetch('/api/processos/locais');
        if (response.ok) {
          const data = await response.json();
          setLocaisDisponiveis(data.locais || []);
        }
      } catch (error) {
        console.error('Erro ao carregar locais:', error);
      }
    };

    carregarLocais();
  }, []);

  // Função para buscar processo existente
  const buscarProcesso = async () => {
    if (!formData.numeroProcesso.trim()) {
      setError('Digite o número do processo para buscar');
      return;
    }

    setSearching(true);
    setError('');
    setProcessoExistente(null);

    try {
      const response = await fetch(`/api/processos/buscar/${encodeURIComponent(formData.numeroProcesso)}`);
      const data = await response.json();

      if (data.success && data.processo) {
        setProcessoExistente(data.processo);
        setIsNovoProcesso(false);

        // Preencher dados do processo existente
        setFormData(prev => ({
          ...prev,
          origem: data.processo.REQUISITANTE || '',
          prioridade: data.processo.PRIORIDADE?.toLowerCase() || 'normal'
        }));
      } else {
        setProcessoExistente(null);
        setIsNovoProcesso(true);
        setSuccess('Processo não encontrado. Será criado como novo processo.');
      }
    } catch (error) {
      console.error('Erro ao buscar processo:', error);
      setError('Erro ao buscar processo. Tente novamente.');
    } finally {
      setSearching(false);
    }
  };

  // Função para classificar prioridade automaticamente
  const classificarPrioridadeAutomatica = (objeto: string): string => {
    const objetoLower = objeto.toLowerCase();

    // Critérios de alta prioridade
    const criteriosAlta = [
      'urgente', 'emergencia', 'emergencial', 'saude publica',
      'bombeiros', 'defesa civil', 'combustivel', 'energia', 'agua'
    ];

    if (criteriosAlta.some(criterio => objetoLower.includes(criterio))) {
      return 'alta';
    }

    return 'normal';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/processos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(`Processo ${formData.numeroProcesso} recebido com sucesso!`);

        // Limpar formulário
        setFormData({
          numeroProcesso: '',
          origem: '',
          responsavel: '',
          dataRecebimento: new Date().toISOString().split('T')[0],
          horaRecebimento: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
          observacoes: '',
          prioridade: 'normal',
          objeto: '',
          modalidade: '',
          valorEstimado: '',
          novoStatus: '',
          novoLocal: '',
          dataInicio: new Date().toISOString().split('T')[0],
          dataEntradaCLMP: new Date().toISOString().split('T')[0],
          fonteTesouro: '',
          fonteEstadual: '',
          fonteFundo: '',
          fonteFederal: '',
          fonteFinisa: '',
          dataVencimentoEstadual: '',
          dataVencimentoFundo: '',
          dataVencimentoFederal: '',
          dataVencimentoFinisa: '',
          numeroCertame: '',
          dataPublicacao: '',
          dataAbertura: '',
          valorContratado: '',
          contratoNumero: '',
          vencimento: '',
          processoGerenciamento: ''
        });

        setProcessoExistente(null);
        setIsNovoProcesso(false);

        // Redirecionar após 2 segundos
        setTimeout(() => {
          router.push('/processos');
        }, 2000);
      } else {
        setError(data.error || 'Erro ao receber processo');
      }
    } catch (error) {
      console.error('Erro ao receber processo:', error);
      setError('Erro de conexão. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="p-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Busca Processo</h1>
          <p className="text-muted-foreground mt-2">
            Digite o número do processo - o sistema identificará se é existente ou novo
          </p>
        </div>
      </div>

      {/* Alertas */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/30 text-green-800 dark:text-green-200">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertDescription className="text-green-800 dark:text-green-200">{success}</AlertDescription>
        </Alert>
      )}

      {/* Busca de Processo */}
      <Card className="bg-card dark:bg-card border-border dark:border-border">
        <CardHeader>
          <CardTitle className="text-foreground">
            Busca Processo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Label htmlFor="buscarProcesso">Número do Processo</Label>
              <Input
                id="buscarProcesso"
                placeholder="Ex: 3372 ou 3372/2025"
                value={formData.numeroProcesso}
                onChange={(e) => handleInputChange('numeroProcesso', e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && buscarProcesso()}
              />
            </div>
            <div className="flex items-end">
              <Button
                type="button"
                onClick={buscarProcesso}
                disabled={searching || !formData.numeroProcesso.trim()}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {searching ? 'Buscando...' : 'Buscar'}
              </Button>
            </div>
          </div>

          {processoExistente && (
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">✅ Processo Existente Encontrado</h4>
              <div className="grid grid-cols-2 gap-2 text-sm text-foreground">
                <div><strong>Processo:</strong> {processoExistente.PROCESSO}</div>
                <div><strong>Requisitante:</strong> {processoExistente.REQUISITANTE}</div>
                <div><strong>Objeto:</strong> {processoExistente.OBJETO?.substring(0, 50)}...</div>
                <div><strong>Status Atual:</strong> {processoExistente.STATUS}</div>
                <div><strong>Local Atual:</strong> {processoExistente.LOCAL}</div>
                <div><strong>Responsável:</strong> {processoExistente['RESPONSÁVEL']}</div>
              </div>
            </div>
          )}

          {isNovoProcesso && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg">
              <h4 className="font-semibold text-green-800 dark:text-green-200">🆕 Novo Processo</h4>
              <p className="text-sm text-green-700 dark:text-green-300">Este processo não existe no sistema e será criado como novo.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Formulário para Processo Existente */}
      {processoExistente && (
        <Card className="bg-card dark:bg-card border-border dark:border-border">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-foreground">
              <Download className="h-5 w-5 text-blue-600" />
              <span>Tramitação de Processo Existente</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Registre a movimentação do processo existente
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                {/* Dados do Processo (Readonly) */}
                <div className="md:col-span-2 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                  <h4 className="font-semibold mb-3 text-foreground">📋 Dados do Processo</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <Label className="text-xs text-muted-foreground">Processo</Label>
                      <p className="font-medium text-foreground">{processoExistente.PROCESSO}</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Requisitante</Label>
                      <p className="font-medium text-foreground">{processoExistente.REQUISITANTE}</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Modalidade</Label>
                      <p className="font-medium text-foreground">{processoExistente.MODALIDADE}</p>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Prioridade</Label>
                      <p className="font-medium text-foreground">{processoExistente.PRIORIDADE}</p>
                    </div>
                    <div className="md:col-span-4">
                      <Label className="text-xs text-muted-foreground">Objeto</Label>
                      <p className="font-medium text-sm text-foreground">{processoExistente.OBJETO}</p>
                    </div>
                  </div>
                </div>

                {/* Status Atual */}
                <div className="md:col-span-2">
                  <Label className="text-sm font-medium text-foreground">📍 Status Atual</Label>
                  <div className="mt-1 p-3 bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <p className="text-sm text-foreground"><strong>Status:</strong> {processoExistente.STATUS}</p>
                    <p className="text-sm text-foreground"><strong>Local:</strong> {processoExistente.LOCAL}</p>
                    <p className="text-sm text-foreground"><strong>Responsável:</strong> {processoExistente['RESPONSÁVEL']}</p>
                    <p className="text-sm text-foreground"><strong>Data:</strong> {processoExistente.DATA}</p>
                  </div>
                </div>

                {/* Novo Status */}
                <div className="space-y-2">
                  <Label htmlFor="novoStatus">Novo Status *</Label>
                  <Select onValueChange={(value) => handleInputChange('novoStatus', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o novo status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Para análise da assessora">Para análise da assessora</SelectItem>
                      <SelectItem value="Para análise do coordenador">Para análise do coordenador</SelectItem>
                      <SelectItem value="Encaminhado para adequações">Encaminhado para adequações</SelectItem>
                      <SelectItem value="Para análise após adequações">Para análise após adequações</SelectItem>
                      <SelectItem value="Encaminhado para emissão de parecer jurídico">Encaminhado para emissão de parecer jurídico</SelectItem>
                      <SelectItem value="Para prosseguimento após parecer jurídico">Para prosseguimento após parecer jurídico</SelectItem>
                      <SelectItem value="Encaminhado para manifestação - adequação ao PCA e disponibilidade orçamentária">Encaminhado para manifestação - adequação ao PCA e disponibilidade orçamentária</SelectItem>
                      <SelectItem value="Para prosseguimento após análise orçamentária">Para prosseguimento após análise orçamentária</SelectItem>
                      <SelectItem value="Para pesquisa de preços">Para pesquisa de preços</SelectItem>
                      <SelectItem value="Encaminhado para ratificação da pesquisa de preços">Encaminhado para ratificação da pesquisa de preços</SelectItem>
                      <SelectItem value="Para prosseguimento após ratificação da pesquisa">Para prosseguimento após ratificação da pesquisa</SelectItem>
                      <SelectItem value="Para publicação de edital">Para publicação de edital</SelectItem>
                      <SelectItem value="Aguardando abertura da licitação">Aguardando abertura da licitação</SelectItem>
                      <SelectItem value="Licitação em andamento">Licitação em andamento</SelectItem>
                      <SelectItem value="Para homologação e AUDESP">Para homologação e AUDESP</SelectItem>
                      <SelectItem value="Para publicação da homologação">Para publicação da homologação</SelectItem>
                      <SelectItem value="Para formalização de contrato/ ata">Para formalização de contrato/ ata</SelectItem>
                      <SelectItem value="Finalizado – processo encaminhado à secretaria para gerenciamento do contrato">Finalizado – processo encaminhado à secretaria para gerenciamento do contrato</SelectItem>
                      <SelectItem value="Finalizado – processo(s) de gerenciamento da ata encaminhado(s) à secretaria">Finalizado – processo(s) de gerenciamento da ata encaminhado(s) à secretaria</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Novo Local */}
                <div className="space-y-2">
                  <Label htmlFor="novoLocal">Novo Local *</Label>
                  <Select onValueChange={(value) => handleInputChange('novoLocal', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o novo local" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CLMP">CLMP</SelectItem>
                      <SelectItem value="SF">Secretaria de Finanças (SF)</SelectItem>
                      <SelectItem value="SAJ">Secretaria de Assuntos Jurídicos (SAJ)</SelectItem>
                      <SelectItem value="SS">Secretaria de Saúde (SS)</SelectItem>
                      <SelectItem value="SE">Secretaria de Educação (SE)</SelectItem>
                      <SelectItem value="SG">Secretaria de Governo (SG)</SelectItem>
                      <SelectItem value="SSU">Secretaria de Serviços Urbanos (SSU)</SelectItem>
                      <SelectItem value="SAS">Secretaria de Assistência Social (SAS)</SelectItem>
                      <SelectItem value="SSAN">Secretaria de Segurança Alimentar e Nutricional (SSAN)</SelectItem>
                      <SelectItem value="SMU">Secretaria da Mulher (SMU)</SelectItem>
                      <SelectItem value="STRE">Secretaria de Trabalho e Renda (STRE)</SelectItem>
                      <SelectItem value="SMA">Secretaria do Meio Ambiente (SMA)</SelectItem>
                      <SelectItem value="SEL">Secretaria de Esporte e Lazer (SEL)</SelectItem>
                      <SelectItem value="SSP">Secretaria de Segurança Pública (SSP)</SelectItem>
                      <SelectItem value="SAM">Secretaria de Agricultura (SAM)</SelectItem>
                      <SelectItem value="SO">Secretaria de Obras (SO)</SelectItem>
                      <SelectItem value="SPDC">Secretaria de Proteção e Defesa Civil (SPDC)</SelectItem>
                      <SelectItem value="SPDPD">Secretaria de Políticas de Drogas (SPDPD)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Novo Responsável */}
                <div className="space-y-2">
                  <Label htmlFor="novoResponsavel">Novo Responsável *</Label>
                  <Input
                    id="novoResponsavel"
                    placeholder="Nome do responsável"
                    value={formData.responsavel}
                    onChange={(e) => handleInputChange('responsavel', e.target.value)}
                    required
                  />
                </div>

                {/* Data de Tramitação */}
                <div className="space-y-2">
                  <Label htmlFor="dataTramitacao" className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Data da Tramitação *</span>
                  </Label>
                  <Input
                    id="dataTramitacao"
                    type="date"
                    value={formData.dataRecebimento}
                    onChange={(e) => handleInputChange('dataRecebimento', e.target.value)}
                    required
                  />
                </div>

                {/* Hora da Tramitação */}
                <div className="space-y-2">
                  <Label htmlFor="horaTramitacao" className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Hora da Tramitação</span>
                  </Label>
                  <Input
                    id="horaTramitacao"
                    type="time"
                    value={formData.horaRecebimento}
                    onChange={(e) => handleInputChange('horaRecebimento', e.target.value)}
                  />
                </div>

                {/* Observações */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="observacoesTramitacao">Observações da Tramitação</Label>
                  <Textarea
                    id="observacoesTramitacao"
                    placeholder="Observações sobre a movimentação do processo..."
                    value={formData.observacoes}
                    onChange={(e) => handleInputChange('observacoes', e.target.value)}
                    rows={3}
                  />
                </div>
              </div>

              {/* Botões */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setProcessoExistente(null);
                    setIsNovoProcesso(false);
                    setFormData({
                      numeroProcesso: '',
                      origem: '',
                      responsavel: '',
                      dataRecebimento: new Date().toISOString().split('T')[0],
                      horaRecebimento: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
                      observacoes: '',
                      prioridade: 'normal',
                      objeto: '',
                      modalidade: '',
                      valorEstimado: '',
                      novoStatus: '',
                      novoLocal: '',
                      dataInicio: new Date().toISOString().split('T')[0],
                      dataEntradaCLMP: new Date().toISOString().split('T')[0],
                      fonteTesouro: '',
                      fonteEstadual: '',
                      fonteFundo: '',
                      fonteFederal: '',
                      fonteFinisa: '',
                      dataVencimentoEstadual: '',
                      dataVencimentoFundo: '',
                      dataVencimentoFederal: '',
                      dataVencimentoFinisa: '',
                      numeroCertame: '',
                      dataPublicacao: '',
                      dataAbertura: '',
                      valorContratado: '',
                      contratoNumero: '',
                      vencimento: '',
                      processoGerenciamento: ''
                    });
                  }}
                >
                  Nova Busca
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? 'Registrando...' : 'Registrar Tramitação'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Formulário para Novo Processo */}
      {isNovoProcesso && (
        <Card className="bg-card dark:bg-card border-border dark:border-border">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-foreground">
              <Download className="h-5 w-5 text-green-600" />
              <span>Cadastro de Novo Processo</span>
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Preencha todos os dados obrigatórios para criar o novo processo
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                {/* Número do Processo */}
                <div className="space-y-2">
                  <Label htmlFor="numeroProcessoNovo" className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>Número do Processo *</span>
                  </Label>
                  <Input
                    id="numeroProcessoNovo"
                    placeholder="Ex: 4086/2025"
                    value={formData.numeroProcesso}
                    onChange={(e) => handleInputChange('numeroProcesso', e.target.value)}
                    required
                  />
                </div>

                {/* Requisitante/Origem */}
                <div className="space-y-2">
                  <Label htmlFor="requisitante" className="flex items-center space-x-2">
                    <Building className="h-4 w-4" />
                    <span>Requisitante/Secretaria *</span>
                  </Label>
                  <Select onValueChange={(value) => handleInputChange('origem', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o requisitante" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SS">Secretaria de Saúde (SS)</SelectItem>
                      <SelectItem value="SE">Secretaria de Educação (SE)</SelectItem>
                      <SelectItem value="SG">Secretaria de Governo (SG)</SelectItem>
                      <SelectItem value="SSAN">Secretaria de Segurança Alimentar e Nutricional (SSAN)</SelectItem>
                      <SelectItem value="SSU">Secretaria de Serviços Urbanos (SSU)</SelectItem>
                      <SelectItem value="SF">Secretaria de Finanças (SF)</SelectItem>
                      <SelectItem value="SAJ">Secretaria de Assuntos Jurídicos (SAJ)</SelectItem>
                      <SelectItem value="SAS">Secretaria de Assistência Social (SAS)</SelectItem>
                      <SelectItem value="SMU">Secretaria da Mulher (SMU)</SelectItem>
                      <SelectItem value="STRE">Secretaria de Trabalho e Renda (STRE)</SelectItem>
                      <SelectItem value="SMA">Secretaria do Meio Ambiente (SMA)</SelectItem>
                      <SelectItem value="SEL">Secretaria de Esporte e Lazer (SEL)</SelectItem>
                      <SelectItem value="SSP">Secretaria de Segurança Pública (SSP)</SelectItem>
                      <SelectItem value="SAM">Secretaria de Agricultura (SAM)</SelectItem>
                      <SelectItem value="SO">Secretaria de Obras (SO)</SelectItem>
                      <SelectItem value="SPDC">Secretaria de Proteção e Defesa Civil (SPDC)</SelectItem>
                      <SelectItem value="SPDPD">Secretaria de Políticas de Drogas (SPDPD)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Objeto do Processo */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="objeto" className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>Objeto do Processo *</span>
                  </Label>
                  <Textarea
                    id="objeto"
                    placeholder="Descreva o objeto/finalidade do processo..."
                    value={formData.objeto || ''}
                    onChange={(e) => handleInputChange('objeto', e.target.value)}
                    rows={3}
                    required
                  />
                </div>

                {/* Modalidade */}
                <div className="space-y-2">
                  <Label htmlFor="modalidade">Modalidade *</Label>
                  <Select onValueChange={(value) => handleInputChange('modalidade', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a modalidade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Pregão">Pregão</SelectItem>
                      <SelectItem value="Dispensa">Dispensa</SelectItem>
                      <SelectItem value="Inexigibilidade">Inexigibilidade</SelectItem>
                      <SelectItem value="Chamamento">Chamamento Público</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Valor Estimado */}
                <div className="space-y-2">
                  <Label htmlFor="valorEstimado">Valor Estimado</Label>
                  <Input
                    id="valorEstimado"
                    placeholder="Ex: R$ 10.000,00"
                    value={formData.valorEstimado || ''}
                    onChange={(e) => handleInputChange('valorEstimado', e.target.value)}
                  />
                </div>

                {/* Prioridade */}
                <div className="space-y-2">
                  <Label>Prioridade</Label>
                  <Select
                    onValueChange={(value) => handleInputChange('prioridade', value)}
                    value={formData.prioridade}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a prioridade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="baixa">Baixa</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="alta">Alta</SelectItem>
                      <SelectItem value="urgente">Urgente</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    💡 A prioridade será classificada automaticamente baseada no objeto
                  </p>
                </div>

                {/* Responsável pelo Recebimento */}
                <div className="space-y-2">
                  <Label htmlFor="responsavelRecebimento" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>Responsável pelo Recebimento *</span>
                  </Label>
                  <Input
                    id="responsavelRecebimento"
                    placeholder="Nome do responsável"
                    value={formData.responsavel}
                    onChange={(e) => handleInputChange('responsavel', e.target.value)}
                    required
                  />
                </div>

                {/* Data de Recebimento */}
                <div className="space-y-2">
                  <Label htmlFor="dataRecebimentoNovo" className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Data de Recebimento *</span>
                  </Label>
                  <Input
                    id="dataRecebimentoNovo"
                    type="date"
                    value={formData.dataRecebimento}
                    onChange={(e) => handleInputChange('dataRecebimento', e.target.value)}
                    required
                  />
                </div>

                {/* Hora de Recebimento */}
                <div className="space-y-2">
                  <Label htmlFor="horaRecebimentoNovo" className="flex items-center space-x-2">
                    <Clock className="h-4 w-4" />
                    <span>Hora de Recebimento</span>
                  </Label>
                  <Input
                    id="horaRecebimentoNovo"
                    type="time"
                    value={formData.horaRecebimento}
                    onChange={(e) => handleInputChange('horaRecebimento', e.target.value)}
                  />
                </div>

                {/* Data de Início do Processo */}
                <div className="space-y-2">
                  <Label htmlFor="dataInicio" className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Data de Início do Processo *</span>
                  </Label>
                  <Input
                    id="dataInicio"
                    type="date"
                    value={formData.dataInicio}
                    onChange={(e) => handleInputChange('dataInicio', e.target.value)}
                    required
                  />
                </div>

                {/* Data de Entrada na CLMP */}
                <div className="space-y-2">
                  <Label htmlFor="dataEntradaCLMP" className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Data de Entrada na CLMP *</span>
                  </Label>
                  <Input
                    id="dataEntradaCLMP"
                    type="date"
                    value={formData.dataEntradaCLMP}
                    onChange={(e) => handleInputChange('dataEntradaCLMP', e.target.value)}
                    required
                  />
                </div>

                {/* Seção de Fontes de Recursos */}
                <div className="md:col-span-2">
                  <h4 className="font-semibold mb-4 text-lg border-b border-border pb-2 text-foreground">💰 Fontes de Recursos</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    {/* Fonte Tesouro */}
                    <div className="space-y-2">
                      <Label htmlFor="fonteTesouro">Fonte 0001 (TESOURO)</Label>
                      <Input
                        id="fonteTesouro"
                        placeholder="Ex: R$ 10.000,00"
                        value={formData.fonteTesouro || ''}
                        onChange={(e) => handleInputChange('fonteTesouro', e.target.value)}
                      />
                    </div>

                    {/* Fonte Estadual */}
                    <div className="space-y-2">
                      <Label htmlFor="fonteEstadual">Fonte 0002 (ESTADUAL)</Label>
                      <Input
                        id="fonteEstadual"
                        placeholder="Ex: R$ 5.000,00"
                        value={formData.fonteEstadual || ''}
                        onChange={(e) => handleInputChange('fonteEstadual', e.target.value)}
                      />
                    </div>

                    {/* Fonte Fundo */}
                    <div className="space-y-2">
                      <Label htmlFor="fonteFundo">Fonte 0003 (FUNDO)</Label>
                      <Input
                        id="fonteFundo"
                        placeholder="Ex: R$ 3.000,00"
                        value={formData.fonteFundo || ''}
                        onChange={(e) => handleInputChange('fonteFundo', e.target.value)}
                      />
                    </div>

                    {/* Fonte Federal */}
                    <div className="space-y-2">
                      <Label htmlFor="fonteFederal">Fonte 0005 (FEDERAL)</Label>
                      <Input
                        id="fonteFederal"
                        placeholder="Ex: R$ 8.000,00"
                        value={formData.fonteFederal || ''}
                        onChange={(e) => handleInputChange('fonteFederal', e.target.value)}
                      />
                    </div>

                    {/* Fonte Finisa */}
                    <div className="space-y-2">
                      <Label htmlFor="fonteFinisa">Fonte 0007 (FINISA)</Label>
                      <Input
                        id="fonteFinisa"
                        placeholder="Ex: R$ 2.000,00"
                        value={formData.fonteFinisa || ''}
                        onChange={(e) => handleInputChange('fonteFinisa', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Seção de Datas de Vencimento */}
                <div className="md:col-span-2">
                  <h4 className="font-semibold mb-4 text-lg border-b border-border pb-2 text-foreground">📅 Datas de Vencimento das Fontes</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                    {/* Data Vencimento Estadual */}
                    <div className="space-y-2">
                      <Label htmlFor="dataVencimentoEstadual">Vencimento Fonte Estadual</Label>
                      <Input
                        id="dataVencimentoEstadual"
                        type="date"
                        value={formData.dataVencimentoEstadual || ''}
                        onChange={(e) => handleInputChange('dataVencimentoEstadual', e.target.value)}
                      />
                    </div>

                    {/* Data Vencimento Fundo */}
                    <div className="space-y-2">
                      <Label htmlFor="dataVencimentoFundo">Vencimento Fonte Fundo</Label>
                      <Input
                        id="dataVencimentoFundo"
                        type="date"
                        value={formData.dataVencimentoFundo || ''}
                        onChange={(e) => handleInputChange('dataVencimentoFundo', e.target.value)}
                      />
                    </div>

                    {/* Data Vencimento Federal */}
                    <div className="space-y-2">
                      <Label htmlFor="dataVencimentoFederal">Vencimento Fonte Federal</Label>
                      <Input
                        id="dataVencimentoFederal"
                        type="date"
                        value={formData.dataVencimentoFederal || ''}
                        onChange={(e) => handleInputChange('dataVencimentoFederal', e.target.value)}
                      />
                    </div>

                    {/* Data Vencimento Finisa */}
                    <div className="space-y-2">
                      <Label htmlFor="dataVencimentoFinisa">Vencimento Fonte Finisa</Label>
                      <Input
                        id="dataVencimentoFinisa"
                        type="date"
                        value={formData.dataVencimentoFinisa || ''}
                        onChange={(e) => handleInputChange('dataVencimentoFinisa', e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* Observações */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="observacoesNovo">Observações</Label>
                  <Textarea
                    id="observacoesNovo"
                    placeholder="Observações sobre o recebimento do processo..."
                    value={formData.observacoes}
                    onChange={(e) => handleInputChange('observacoes', e.target.value)}
                    rows={3}
                  />
                </div>
              </div>

              {/* Botões */}
              <div className="flex justify-end space-x-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setProcessoExistente(null);
                    setIsNovoProcesso(false);
                    setFormData({
                      numeroProcesso: '',
                      origem: '',
                      responsavel: '',
                      dataRecebimento: new Date().toISOString().split('T')[0],
                      horaRecebimento: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
                      observacoes: '',
                      prioridade: 'normal',
                      objeto: '',
                      modalidade: '',
                      valorEstimado: '',
                      novoStatus: '',
                      novoLocal: '',
                      dataInicio: new Date().toISOString().split('T')[0],
                      dataEntradaCLMP: new Date().toISOString().split('T')[0],
                      fonteTesouro: '',
                      fonteEstadual: '',
                      fonteFundo: '',
                      fonteFederal: '',
                      fonteFinisa: '',
                      dataVencimentoEstadual: '',
                      dataVencimentoFundo: '',
                      dataVencimentoFederal: '',
                      dataVencimentoFinisa: '',
                      numeroCertame: '',
                      dataPublicacao: '',
                      dataAbertura: '',
                      valorContratado: '',
                      contratoNumero: '',
                      vencimento: '',
                      processoGerenciamento: ''
                    });
                  }}
                >
                  Nova Busca
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white"
                >
                  {loading ? 'Criando...' : 'Criar Novo Processo'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
