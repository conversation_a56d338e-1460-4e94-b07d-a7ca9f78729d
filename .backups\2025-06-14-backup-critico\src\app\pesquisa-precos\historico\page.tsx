/**
 * 📊 PÁGINA DE HISTÓRICO DE PESQUISAS DE PREÇOS
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Calendar,
  User,
  FileText,
  Download,
  Eye,
  Filter
} from 'lucide-react';

interface PesquisaHistorico {
  id: string;
  numeroProcesso: string;
  objeto: string;
  pesquisador: string;
  tipoPesquisa: 'simples' | 'combinada';
  dataFinalizacao: string;
  status: 'concluida' | 'em_andamento' | 'aprovada';
  resultados: {
    totalItens: number;
    precoMedio: number;
    economia: number;
  };
}

export default function HistoricoPesquisaPage() {
  const [filtro, setFiltro] = useState('');
  const [tipoFiltro, setTipoFiltro] = useState<'todos' | 'simples' | 'combinada'>('todos');

  // Dados mock do histórico
  const historicoPesquisas: PesquisaHistorico[] = [
    {
      id: '1',
      numeroProcesso: '2025/001',
      objeto: 'Álcool gel antisséptico 70%',
      pesquisador: 'Carla',
      tipoPesquisa: 'combinada',
      dataFinalizacao: '2025-06-14',
      status: 'concluida',
      resultados: {
        totalItens: 15,
        precoMedio: 12.50,
        economia: 2500.00
      }
    },
    {
      id: '2',
      numeroProcesso: '2025/002',
      objeto: 'Papel A4 sulfite branco',
      pesquisador: 'Fernando',
      tipoPesquisa: 'simples',
      dataFinalizacao: '2025-06-13',
      status: 'aprovada',
      resultados: {
        totalItens: 8,
        precoMedio: 25.90,
        economia: 1200.00
      }
    },
    {
      id: '3',
      numeroProcesso: '2025/003',
      objeto: 'Material de limpeza diversos',
      pesquisador: 'Gilson',
      tipoPesquisa: 'combinada',
      dataFinalizacao: '2025-06-12',
      status: 'em_andamento',
      resultados: {
        totalItens: 22,
        precoMedio: 45.80,
        economia: 0
      }
    }
  ];

  const pesquisasFiltradas = historicoPesquisas.filter(pesquisa => {
    const matchFiltro = pesquisa.objeto.toLowerCase().includes(filtro.toLowerCase()) ||
                       pesquisa.numeroProcesso.includes(filtro) ||
                       pesquisa.pesquisador.toLowerCase().includes(filtro.toLowerCase());

    const matchTipo = tipoFiltro === 'todos' || pesquisa.status === tipoFiltro;

    return matchFiltro && matchTipo;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'concluida': return 'bg-green-100 text-green-800';
      case 'aprovada': return 'bg-blue-100 text-blue-800';
      case 'em_andamento': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'concluida': return 'Concluída';
      case 'aprovada': return 'Aprovada';
      case 'em_andamento': return 'Em Andamento';
      default: return status;
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">📊 Histórico de Pesquisas de Preços</h1>
        <p className="text-muted-foreground mt-2">
          Acompanhe todas as pesquisas realizadas pela equipe
        </p>
      </div>

      {/* Filtros */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Buscar:</label>
              <Input
                placeholder="Processo, objeto ou pesquisador..."
                value={filtro}
                onChange={(e) => setFiltro(e.target.value)}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Status:</label>
              <select
                value={tipoFiltro}
                onChange={(e) => setTipoFiltro(e.target.value as any)}
                className="w-full p-2 border rounded-md"
              >
                <option value="todos">Todos os status</option>
                <option value="concluida">Concluídas</option>
                <option value="em_andamento">Em Andamento</option>
                <option value="aprovada">Aprovadas</option>
              </select>
            </div>
            
            <div className="flex items-end">
              <Button variant="outline" className="w-full">
                <Search className="mr-2 h-4 w-4" />
                Filtrar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{historicoPesquisas.length}</div>
            <div className="text-sm text-muted-foreground">Total de Pesquisas</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {historicoPesquisas.filter(p => p.status === 'concluida').length}
            </div>
            <div className="text-sm text-muted-foreground">Concluídas</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {historicoPesquisas.filter(p => p.tipoPesquisa === 'combinada').length}
            </div>
            <div className="text-sm text-muted-foreground">Pesquisas Combinadas</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              R$ {historicoPesquisas.reduce((sum, p) => sum + p.resultados.economia, 0).toFixed(0)}
            </div>
            <div className="text-sm text-muted-foreground">Economia Total</div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Pesquisas */}
      <Card>
        <CardHeader>
          <CardTitle>Pesquisas Realizadas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pesquisasFiltradas.map((pesquisa) => (
              <div key={pesquisa.id} className="border border-border rounded-lg p-4 hover:bg-muted/50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h3 className="font-semibold">{pesquisa.numeroProcesso}</h3>
                    <Badge className={getStatusColor(pesquisa.status)}>
                      {getStatusText(pesquisa.status)}
                    </Badge>
                    <Badge variant="outline">
                      {pesquisa.tipoPesquisa === 'simples' ? '🔍 PNCP' : '🔄 Combinada'}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Eye className="mr-2 h-3 w-3" />
                      Ver
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-3 w-3" />
                      Baixar
                    </Button>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3">{pesquisa.objeto}</p>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Pesquisador:</span>
                    <p className="font-medium">{pesquisa.pesquisador}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Data:</span>
                    <p className="font-medium">{new Date(pesquisa.dataFinalizacao).toLocaleDateString('pt-BR')}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Itens:</span>
                    <p className="font-medium">{pesquisa.resultados.totalItens}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Preço Médio:</span>
                    <p className="font-medium">R$ {pesquisa.resultados.precoMedio.toFixed(2)}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Economia:</span>
                    <p className="font-medium text-green-600">
                      R$ {pesquisa.resultados.economia.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {pesquisasFiltradas.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="mx-auto h-12 w-12 mb-4" />
              <p>Nenhuma pesquisa encontrada com os filtros aplicados.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
