
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

export default function SuspensePage() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden">
      {/* Background geometric elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/3 to-cyan-500/3 rounded-full blur-3xl"></div>
      </div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

      {/* Header */}
      <header className="relative z-10 flex justify-between items-center p-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="flex items-center space-x-3"
        >
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">IP</span>
          </div>
          <span className="text-white font-semibold text-xl">InovaProcess</span>
        </motion.div>
      </header>

      {/* Main content */}
      <main className="relative z-10 flex flex-col items-center justify-center min-h-[calc(100vh-120px)] px-6 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="max-w-4xl mx-auto space-y-8"
        >
          {/* Main title */}
          <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight">
            Uma Revolução
            <br />
            <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
              Silenciosa
            </span>
          </h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
          >
            Está transformando a gestão de organizações ao redor do mundo.
            <br />
            <span className="text-blue-400">Tecnologia que poucos conhecem.</span>
          </motion.p>

          {/* Video placeholder */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 1.2 }}
            className="relative mx-auto w-full max-w-2xl aspect-video bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-700/50 backdrop-blur-sm"
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30">
                  <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-gray-400 text-sm">Vídeo em breve</p>
              </div>
            </div>
          </motion.div>

          {/* Mystery text */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.6 }}
            className="space-y-4"
          >
            <p className="text-lg text-gray-400 max-w-2xl mx-auto">
              Descubra o que líderes visionários já sabem.
              <br />
              Uma ferramenta que redefine os limites do possível.
            </p>

            <div className="flex items-center justify-center space-x-2 text-cyan-400">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">Acesso Restrito</span>
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
            </div>
          </motion.div>

          {/* CTA Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2 }}
          >
            <button
              onClick={() => router.push('/solicitar-acesso')}
              className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-xl hover:from-blue-500 hover:to-cyan-500 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>Descobrir o Segredo</span>
                <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>

              {/* Button glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
            </button>
          </motion.div>
        </motion.div>

        {/* Bottom hint */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <div className="flex flex-col items-center space-y-2 text-gray-500">
            <span className="text-xs uppercase tracking-wider">Apenas para qualificados</span>
            <div className="w-px h-8 bg-gradient-to-b from-gray-500 to-transparent"></div>
          </div>
        </motion.div>
      </main>
    </div>
  );
}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🏗️ Obras e Infraestrutura</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Acompanhamento macro de projetos, cronogramas vs realidade, eficiência de contratos e impacto socioeconômico das obras.
                </p>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">25-40%</div>
                <div className="text-xs text-muted-foreground">do orçamento total</div>
              </div>

              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🏥 Saúde Pública</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Execução orçamentária em tempo real, indicadores de eficiência, integração DATASUS e análise preditiva de demandas.
                </p>
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">25-30%</div>
                <div className="text-xs text-muted-foreground">do orçamento total</div>
              </div>

              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🎓 Educação Pública</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Aplicação dos 25% constitucionais, indicadores educacionais, integração FNDE e projeções de matrículas futuras.
                </p>
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">25%</div>
                <div className="text-xs text-muted-foreground">mínimo constitucional</div>
              </div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold text-lg">
                <span className="mr-2">📊</span>
                Total: 70-90% do Orçamento Público
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 dark:hover:from-blue-900/20 dark:hover:to-blue-800/20">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">Dashboards Executivos</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Visão consolidada de todos os sistemas com KPIs estratégicos em tempo real.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-green-50 hover:to-green-100 dark:hover:from-green-900/20 dark:hover:to-green-800/20">
              <div className="h-10 w-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">Integração de Sistemas</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Conecta SIAFI, SICONV, DATASUS, FNDE e todos os sistemas locais em uma única plataforma.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-purple-50 hover:to-purple-100 dark:hover:from-purple-900/20 dark:hover:to-purple-800/20">
              <div className="h-10 w-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">Análise Preditiva</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                IA que antecipa problemas, prevê demandas e otimiza recursos antes que crises aconteçam.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-orange-50 hover:to-orange-100 dark:hover:from-orange-900/20 dark:hover:to-orange-800/20">
              <div className="h-10 w-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center text-orange-600 dark:text-orange-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">Alertas Inteligentes</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Monitoramento 24/7 com alertas automáticos para desvios orçamentários e riscos de compliance.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>
          </div>

          {/* Seção de Segmentos Atendidos */}
          <div className="bg-gradient-to-r from-blue-100/80 to-purple-100/80 dark:from-slate-700 dark:to-slate-800 rounded-2xl p-8 md:p-12 text-slate-700 dark:text-white mb-16 hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group border border-blue-200/60 dark:border-transparent shadow-lg">
            <div className="max-w-6xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 group-hover:scale-105 transition-transform duration-300">
                Gestão Inteligente para Organizações Públicas e Privadas
              </h2>
              <p className="text-lg md:text-xl mb-8 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                Solução completa que se adapta à complexidade de cada organização, desde governos estaduais
                até grandes corporações. Tecnologia que escala conforme suas necessidades.
              </p>

              {/* Setor Público */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold mb-6 text-blue-600 dark:text-blue-400">🏛️ Setor Público</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏛️</div>
                    <div className="font-semibold mb-1">Governos Estaduais</div>
                    <div className="text-sm opacity-80">Gestão macro de múltiplas secretarias</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏙️</div>
                    <div className="font-semibold mb-1">Prefeituras</div>
                    <div className="text-sm opacity-80">Grande e médio porte</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">⚖️</div>
                    <div className="font-semibold mb-1">Controle Externo</div>
                    <div className="text-sm opacity-80">Tribunais, Controladorias, Procuradorias</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏢</div>
                    <div className="font-semibold mb-1">Administração Direta</div>
                    <div className="text-sm opacity-80">Secretarias e órgãos centrais</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏛️</div>
                    <div className="font-semibold mb-1">Administração Indireta</div>
                    <div className="text-sm opacity-80">Autarquias, Empresas Públicas, Fundações</div>
                  </div>
                </div>
              </div>

              {/* Setor Privado */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold mb-6 text-purple-600 dark:text-purple-400">🏢 Setor Privado</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-purple-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏭</div>
                    <div className="font-semibold mb-1">Indústria</div>
                    <div className="text-sm opacity-80">Produção, Logística, Qualidade</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-purple-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏪</div>
                    <div className="font-semibold mb-1">Varejo</div>
                    <div className="text-sm opacity-80">Franquias, E-commerce, Redes</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-purple-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏗️</div>
                    <div className="font-semibold mb-1">Construtoras</div>
                    <div className="text-sm opacity-80">Obras, Projetos, Compliance</div>
                  </div>
                  <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-purple-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                    <div className="text-2xl mb-2">🏢</div>
                    <div className="font-semibold mb-1">Holdings</div>
                    <div className="text-sm opacity-80">Conglomerados, Consolidação</div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl mb-2">🏛️</div>
                  <div className="font-semibold mb-1">Governos Estaduais</div>
                  <div className="text-sm opacity-80">Gestão macro de múltiplas secretarias</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl mb-2">🏙️</div>
                  <div className="font-semibold mb-1">Prefeituras</div>
                  <div className="text-sm opacity-80">Grande e médio porte</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl mb-2">⚖️</div>
                  <div className="font-semibold mb-1">Controle Externo</div>
                  <div className="text-sm opacity-80">Tribunais, Controladorias, Procuradorias</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl mb-2">🏢</div>
                  <div className="font-semibold mb-1">Administração Direta</div>
                  <div className="text-sm opacity-80">Secretarias e órgãos centrais</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl mb-2">🏛️</div>
                  <div className="font-semibold mb-1">Administração Indireta</div>
                  <div className="text-sm opacity-80">Autarquias, Empresas Públicas, Fundações</div>
                </div>
              </div>

              {/* Indicadores Gerais */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-green-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-green-600 dark:text-white">70-90%</div>
                  <div className="text-sm opacity-80">Cobertura Orçamentária</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-blue-600 dark:text-white">100%</div>
                  <div className="text-sm opacity-80">Transparência Total</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-purple-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-purple-600 dark:text-white">24/7</div>
                  <div className="text-sm opacity-80">Monitoramento IA</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-orange-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-orange-600 dark:text-white">+80%</div>
                  <div className="text-sm opacity-80">Eficiência Operacional</div>
                </div>
              </div>

              <div className="flex justify-center">
                <Link
                  href="/login"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-8 rounded-lg transition-all duration-300 flex items-center hover:scale-105 hover:shadow-lg"
                >
                  Acessar Sistema
                  <ArrowRight size={18} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

