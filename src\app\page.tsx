
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <header className="bg-card shadow-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-6">
          <div className="flex items-center">
            <div className="bg-primary text-primary-foreground font-bold text-xl px-3 py-1 rounded mr-3">IP</div>
            <h1 className="text-2xl font-bold text-foreground">InovaProcess</h1>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Link
              href="/login"
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center"
            >
              Acessar Sistema
              <ArrowRight size={16} className="ml-2" />
            </Link>
          </div>
        </div>
      </header>

      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-extrabold text-foreground mb-6">
              Inteligência Estratégica para
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> Gestão Macro</span>
            </h2>
            <p className="max-w-4xl mx-auto text-xl text-muted-foreground mb-8">
              Plataforma que integra todos os sistemas, analisa dados com IA e fornece visão macro estratégica para tomada de decisões inteligentes. Monitore os maiores orçamentos, acompanhe KPIs em tempo real e antecipe problemas antes que aconteçam.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-medium">🧠 Inteligência Artificial</span>
              <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 rounded-full font-medium">🔗 Integração Total</span>
              <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-3 py-1 rounded-full font-medium">📊 KPIs Estratégicos</span>
              <span className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-3 py-1 rounded-full font-medium">👁️ Visão Macro</span>
            </div>
          </div>

          {/* Nova seção - Visão Macro Estratégica */}
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-800 dark:to-slate-700 rounded-2xl p-8 md:p-12 mb-16 border border-slate-200 dark:border-slate-600">
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                Gestão Macro dos Maiores Orçamentos
              </h3>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                Monitore e analise os 3 pilares que representam 70-90% de todo orçamento público com inteligência artificial e dashboards executivos em tempo real.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🏗️ Obras e Infraestrutura</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Acompanhamento macro de projetos, cronogramas vs realidade, eficiência de contratos e impacto socioeconômico das obras.
                </p>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">25-40%</div>
                <div className="text-xs text-muted-foreground">do orçamento total</div>
              </div>

              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🏥 Saúde Pública</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Execução orçamentária em tempo real, indicadores de eficiência, integração DATASUS e análise preditiva de demandas.
                </p>
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">25-30%</div>
                <div className="text-xs text-muted-foreground">do orçamento total</div>
              </div>

              <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-slate-200 dark:border-slate-600">
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-6 w-6">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-foreground mb-3">🎓 Educação Pública</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Aplicação dos 25% constitucionais, indicadores educacionais, integração FNDE e projeções de matrículas futuras.
                </p>
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">25%</div>
                <div className="text-xs text-muted-foreground">mínimo constitucional</div>
              </div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold text-lg">
                <span className="mr-2">📊</span>
                Total: 70-90% do Orçamento Público
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-blue-50 hover:to-blue-100 dark:hover:from-blue-900/20 dark:hover:to-blue-800/20">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">Dashboards Executivos</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Visão consolidada de todos os sistemas com KPIs estratégicos em tempo real.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-green-50 hover:to-green-100 dark:hover:from-green-900/20 dark:hover:to-green-800/20">
              <div className="h-10 w-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">Integração de Sistemas</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Conecta SIAFI, SICONV, DATASUS, FNDE e todos os sistemas locais em uma única plataforma.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-purple-50 hover:to-purple-100 dark:hover:from-purple-900/20 dark:hover:to-purple-800/20">
              <div className="h-10 w-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">Análise Preditiva</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                IA que antecipa problemas, prevê demandas e otimiza recursos antes que crises aconteçam.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-xl transition-all duration-500 border border-border group flex flex-col h-full hover:-translate-y-2 hover:bg-gradient-to-br hover:from-orange-50 hover:to-orange-100 dark:hover:from-orange-900/20 dark:hover:to-orange-800/20">
              <div className="h-10 w-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center text-orange-600 dark:text-orange-400 mb-3 group-hover:scale-125 group-hover:rotate-6 transition-all duration-500">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">Alertas Inteligentes</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow group-hover:text-foreground transition-colors duration-300">
                Monitoramento 24/7 com alertas automáticos para desvios orçamentários e riscos de compliance.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-2 transition-all duration-300 mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1 group-hover:animate-pulse" />
              </Link>
            </div>
          </div>

          {/* Seção de Apresentação de Alto Nível */}
          <div className="bg-gradient-to-r from-blue-100/80 to-purple-100/80 dark:from-slate-700 dark:to-slate-800 rounded-2xl p-8 md:p-12 text-slate-700 dark:text-white mb-16 hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group border border-blue-200/60 dark:border-transparent shadow-lg">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 group-hover:scale-105 transition-transform duration-300">
                Tecnologia de Ponta em Gestão de Processos
              </h2>
              <p className="text-lg md:text-xl mb-8 opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                Plataforma desenvolvida com as mais avançadas tecnologias de mercado, incluindo
                inteligência artificial, automação inteligente e preparação para blockchain.
                Ideal para organizações que buscam excelência operacional e inovação.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-blue-600 dark:text-white">+80%</div>
                  <div className="text-sm opacity-80">Eficiência Operacional</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-blue-600 dark:text-white">100%</div>
                  <div className="text-sm opacity-80">Transparência e Auditoria</div>
                </div>
                <div className="bg-white/70 dark:bg-white/10 backdrop-blur-sm rounded-lg p-4 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:-translate-y-1 border border-blue-200/40 dark:border-white/20 shadow-sm hover:shadow-md">
                  <div className="text-2xl font-bold mb-1 animate-pulse text-blue-600 dark:text-white">24/7</div>
                  <div className="text-sm opacity-80">Monitoramento Inteligente</div>
                </div>
              </div>

              <div className="flex justify-center">
                <Link
                  href="/login"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-8 rounded-lg transition-all duration-300 flex items-center hover:scale-105 hover:shadow-lg"
                >
                  Acessar Sistema
                  <ArrowRight size={18} className="ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

