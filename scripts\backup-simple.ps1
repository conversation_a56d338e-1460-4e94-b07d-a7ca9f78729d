# 🎯 BACKUP SIMPLES PARA INOVAPROCESS
param(
    [string]$Action = "pre-deploy",
    [string]$Description = "backup"
)

$Date = Get-Date -Format "yyyy-MM-dd-HH-mm"

Write-Host "🎯 BACKUP MANAGER - Ação: $Action" -ForegroundColor Cyan

# Criar estrutura base
if (!(Test-Path ".backups")) {
    New-Item -ItemType Directory -Path ".backups" -Force | Out-Null
    New-Item -ItemType Directory -Path ".backups\pre-deploy" -Force | Out-Null
    New-Item -ItemType Directory -Path ".backups\marcos" -Force | Out-Null
}

if ($Action -eq "pre-deploy") {
    Write-Host "🚀 CRIANDO BACKUP PRE-DEPLOY..." -ForegroundColor Green
    
    $PreDeployPath = ".backups\pre-deploy\atual"
    
    # Remove backup anterior
    if (Test-Path $PreDeployPath) {
        Remove-Item $PreDeployPath -Recurse -Force
        Write-Host "📁 Backup anterior removido" -ForegroundColor Yellow
    }
    
    # Cria novo backup
    New-Item -ItemType Directory -Path $PreDeployPath -Force | Out-Null
    
    # Copia arquivos importantes
    $ExcludeFolders = @("node_modules", ".next", ".vercel", ".backups", "backup-*")
    
    Get-ChildItem "." | Where-Object { $_.Name -notin $ExcludeFolders } | ForEach-Object {
        try {
            Copy-Item $_.FullName -Destination $PreDeployPath -Recurse -Force -ErrorAction Stop
        } catch {
            Write-Host "⚠️ Erro ao copiar: $($_.Name)" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ BACKUP PRE-DEPLOY CRIADO!" -ForegroundColor Green
}

if ($Action -eq "migrate") {
    Write-Host "🔄 MIGRANDO PRE-DEPLOY PARA MARCOS..." -ForegroundColor Green
    
    $PreDeployPath = ".backups\pre-deploy\atual"
    $MarcosPath = ".backups\marcos\$Date-deploy-$Description"
    
    if (Test-Path $PreDeployPath) {
        Move-Item $PreDeployPath $MarcosPath
        Write-Host "✅ MIGRADO PARA MARCOS: $MarcosPath" -ForegroundColor Green
    } else {
        Write-Host "❌ Nenhum backup pre-deploy encontrado" -ForegroundColor Red
    }
}

Write-Host "🎯 OPERAÇÃO CONCLUÍDA!" -ForegroundColor Cyan
