'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Clock,
  CheckCircle,
  DollarSign,
  FileText
} from 'lucide-react';

interface BannerConfig {
  id: string;
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
  badge?: {
    text: string;
    variant: string;
  };
  onClick?: () => void;
}

interface DraggableBannersProps {
  metricas: any;
  totalItems: number;
  isAdmin?: boolean;
}

export default function DraggableBanners({
  metricas,
  totalItems,
  isAdmin = false
}: DraggableBannersProps) {
  const [bannerOrder, setBannerOrder] = useState<string[]>([]);

  // Debug: verificar se os dados estão chegando
  console.log('🎯 DraggableBanners - Dados recebidos:', {
    metricas,
    totalItems,
    isAdmin
  });

  // Configuração padrão dos banners
  const defaultBanners: BannerConfig[] = [
    {
      id: 'total',
      title: 'Total',
      value: totalItems,
      subtitle: 'Processos',
      icon: <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />,
      color: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50'
    },
    {
      id: 'risco',
      title: 'Recursos Externos',
      value: metricas.recursosExternos?.total || 0,
      subtitle: `Alto: ${metricas.recursosExternos?.alto || 0} | Médio: ${metricas.recursosExternos?.medio || 0} | Baixo: ${metricas.recursosExternos?.baixo || 0}`,
      icon: <DollarSign className="h-8 w-8 text-red-600 dark:text-red-400" />,
      color: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50 cursor-pointer hover:shadow-lg transition-shadow',
      badge: (metricas.recursosExternos?.alto || 0) > 0 ? {
        text: 'RISCO ALTO',
        variant: 'destructive'
      } : (metricas.recursosExternos?.medio || 0) > 0 ? {
        text: 'RISCO MÉDIO',
        variant: 'warning'
      } : (metricas.recursosExternos?.baixo || 0) > 0 ? {
        text: 'RISCO BAIXO',
        variant: 'secondary'
      } : undefined,
      onClick: () => {
        if ((metricas.recursosExternos?.total || 0) > 0) {
          // Redirecionar para processos com filtro de recursos externos
          window.location.href = '/processos?fonte=externa';
        }
      }
    },
    {
      id: 'gargalos',
      title: 'Gargalos Críticos',
      value: (() => {
        const totalGargalos = metricas.gargalosSF + metricas.gargalosSAJ;
        if (totalGargalos === 0) return 'Sistema Fluindo';
        return totalGargalos;
      })(),
      subtitle: (() => {
        const totalGargalos = metricas.gargalosSF + metricas.gargalosSAJ;
        if (totalGargalos === 0) return 'Sem gargalos detectados';
        if (metricas.gargalosSF > metricas.gargalosSAJ) return `Maior gargalo: SF (${metricas.gargalosSF})`;
        if (metricas.gargalosSAJ > metricas.gargalosSF) return `Maior gargalo: SAJ (${metricas.gargalosSAJ})`;
        return `SF: ${metricas.gargalosSF} | SAJ: ${metricas.gargalosSAJ}`;
      })(),
      icon: <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />,
      color: 'border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50'
    },
    {
      id: 'tempo-clmp',
      title: 'Tempo Médio CLMP',
      value: metricas.tempoMedioCLMP || 0,
      subtitle: 'dias',
      icon: <Clock className="h-8 w-8 text-green-600 dark:text-green-400" />,
      color: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50'
    },
    {
      id: 'tempo-sf',
      title: 'Tempo Médio SF',
      value: metricas.tempoMedioSF || 0,
      subtitle: 'dias',
      icon: <Clock className="h-8 w-8 text-amber-600 dark:text-amber-400" />,
      color: 'border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50'
    },
    {
      id: 'tempo-saj',
      title: 'Tempo Médio SAJ',
      value: metricas.tempoMedioSAJ || 0,
      subtitle: 'dias',
      icon: <Clock className="h-8 w-8 text-purple-600 dark:text-purple-400" />,
      color: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50'
    }
  ];

  // Carregar ordem padrão
  useEffect(() => {
    setBannerOrder(defaultBanners.map(b => b.id));
  }, []);

  // Ordenar banners conforme a ordem salva
  const orderedBanners = bannerOrder
    .map(id => defaultBanners.find(b => b.id === id))
    .filter(Boolean) as BannerConfig[];

  return (
    <div className="space-y-4">
      {/* Grid de banners */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {orderedBanners.map((banner) => (
          <Card
            key={banner.id}
            className={banner.color}
            onClick={banner.onClick}
          >
            <CardContent className="p-4 min-h-[120px]">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium">{banner.title}</p>
                  <p className="text-2xl font-bold mt-1">{banner.value}</p>
                  {banner.subtitle && (
                    <p className="text-xs text-muted-foreground mt-1">{banner.subtitle}</p>
                  )}
                </div>
                <div className="ml-3">
                  {banner.icon}
                </div>
              </div>
              {banner.badge && (
                <Badge
                  variant={banner.badge.variant as any}
                  className="mt-3 text-xs"
                >
                  {banner.badge.text}
                </Badge>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
