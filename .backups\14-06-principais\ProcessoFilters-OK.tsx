'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X } from 'lucide-react';

interface FilterOptions {
  status: string[];
  modalidades: string[];
  responsaveis: string[];
  requisitantes: string[];
}

interface ProcessoFiltersProps {
  onFiltersChange: (filters: {
    search: string;
    status: string;
    modalidade: string;
    responsavel: string;
    requisitante: string;
  }) => void;
  loading?: boolean;
}

export default function ProcessoFilters({ onFiltersChange, loading = false }: ProcessoFiltersProps) {
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    modalidade: '',
    responsavel: '',
    requisitante: '',
  });

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    status: [],
    modalidades: [],
    responsaveis: [],
    requisitantes: [],
  });

  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Carregar opções de filtros
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const response = await fetch('/api/processos/stats');
        const data = await response.json();
        
        if (data.success) {
          setFilterOptions(data.data.filtros);
        }
      } catch (error) {
        console.error('Erro ao carregar opções de filtros:', error);
      }
    };

    loadFilterOptions();
  }, []);

  // Aplicar filtros com debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onFiltersChange(filters);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [filters, onFiltersChange]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      modalidade: '',
      responsavel: '',
      requisitante: '',
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
      {/* Busca principal */}
      <div className="flex flex-col sm:flex-row gap-4 mb-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="🔍 Busca inteligente: SSDAN, alimentos, CC 045/2024, medicamentos..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            <Filter size={16} />
            Filtros
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-2 px-4 py-2 bg-red-50 text-red-600 border border-red-200 rounded-md hover:bg-red-100 transition-colors"
              disabled={loading}
            >
              <X size={16} />
              Limpar
            </button>
          )}
        </div>
      </div>

      {/* Filtros específicos por campo */}
      {showAdvancedFilters && (
        <div className="space-y-4 pt-4 border-t">
          {/* Primeira linha - Campos principais */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nº Processo
              </label>
              <input
                type="text"
                placeholder="Ex: 9078/2024"
                value={filters.search.includes('/') ? filters.search : ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Objeto
              </label>
              <input
                type="text"
                placeholder="Ex: medicamentos, fraldas"
                value={filters.search.includes('medicamentos') || filters.search.includes('fraldas') ? filters.search : ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Secretaria
              </label>
              <select
                value={filters.requisitante}
                onChange={(e) => handleFilterChange('requisitante', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todas as secretarias</option>
                {filterOptions.requisitantes.map(requisitante => (
                  <option key={requisitante} value={requisitante}>
                    {requisitante}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nº Certame
              </label>
              <input
                type="text"
                placeholder="Ex: PE RP 006/2025"
                value={filters.search.includes('PE') || filters.search.includes('DE') ? filters.search : ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>
          </div>

          {/* Segunda linha - Campos adicionais */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nº Contrato
              </label>
              <input
                type="text"
                placeholder="Ex: Contrato 013/2025"
                value={filters.search.includes('Contrato') ? filters.search : ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prioridade
              </label>
              <select
                value={filters.modalidade}
                onChange={(e) => handleFilterChange('modalidade', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todas as prioridades</option>
                <option value="Alta">Alta</option>
                <option value="Média">Média</option>
                <option value="Baixa">Baixa</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Local
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todos os locais</option>
                <option value="CLMP">CLMP</option>
                <option value="SAJ">SAJ</option>
                <option value="SF">SF</option>
                <option value="SS">SS</option>
                <option value="SMA">SMA</option>
                <option value="SE">SE</option>
                <option value="SG">SG</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Responsável
              </label>
              <select
                value={filters.responsavel}
                onChange={(e) => handleFilterChange('responsavel', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todos os responsáveis</option>
                {filterOptions.responsaveis.map(responsavel => (
                  <option key={responsavel} value={responsavel}>
                    {responsavel}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
