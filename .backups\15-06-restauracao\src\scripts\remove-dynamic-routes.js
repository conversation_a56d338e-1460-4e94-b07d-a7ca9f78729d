const fs = require('fs');
const path = require('path');

// Função para remover rotas dinâmicas conflitantes
function removeDynamicRoutes() {
  console.log('Iniciando remoção de rotas dinâmicas conflitantes...');
  
  // Caminhos conhecidos de rotas dinâmicas problemáticas
  const routesToRemove = [
    'src/app/processos/[arquivo]',
    'src/app/processos/[numero]',
    'src/app/relatorios/[id]',
    'src/app/relatorios/[arquivo]',
    'src/app/dashboard/[id]',
    'src/app/dashboard/[arquivo]'
  ];
  
  let removedCount = 0;
  
  // Remove cada rota da lista
  routesToRemove.forEach(routePath => {
    const fullPath = path.join(process.cwd(), routePath);
    
    if (fs.existsSync(fullPath)) {
      console.log(`Removendo rota dinâmica: ${routePath}`);
      fs.rmSync(fullPath, { recursive: true, force: true });
      removedCount++;
    }
  });
  
  if (removedCount > 0) {
    console.log(`\n✅ Removidas ${removedCount} rotas dinâmicas conflitantes.`);
  } else {
    console.log('\nNenhuma rota dinâmica conflitante encontrada para remover.');
  }
}

// Executa a remoção
removeDynamicRoutes();

console.log('\n✅ Processo concluído. Tente executar "npm run dev" novamente.');