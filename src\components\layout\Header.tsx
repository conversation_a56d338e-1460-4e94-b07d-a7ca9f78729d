'use client';

import { useState, useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Bell, Search, HelpCircle, User, LogOut, Settings, ChevronDown } from 'lucide-react';
import { ThemeToggle } from '@/components/theme-toggle';
import { useAuth } from '@/contexts/AuthContext';

// Função para obter o título da página atual
const getPageTitle = (pathname: string): string => {
  if (pathname === '/dashboard') return 'Dashboard';
  if (pathname.startsWith('/processos')) return 'Processos';
  if (pathname.startsWith('/pesquisa-precos')) return 'Pesquisa de Preços';
  if (pathname.startsWith('/analise-editais')) return 'Análise de Editais';
  if (pathname.startsWith('/contratos')) return 'Contratos';
  if (pathname.startsWith('/relatorios')) return 'Relatórios';
  if (pathname.startsWith('/secretarias')) return 'Secretarias';
  if (pathname.startsWith('/usuarios')) return 'Usuários';
  if (pathname.startsWith('/configuracoes')) return 'Configurações';
  return 'InovaProcess';
};

export default function Header() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuth();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Se já estamos na página de processos, não redirecionar
      if (pathname === '/processos') {
        // Disparar evento customizado para a página de processos
        window.dispatchEvent(new CustomEvent('globalSearch', {
          detail: { query: searchQuery.trim() }
        }));
      } else {
        // Redirecionar para página de processos com filtro de busca
        window.location.href = `/processos?search=${encodeURIComponent(searchQuery.trim())}`;
      }
      setSearchQuery(''); // Limpar após busca
    }
  };

  // Sistema simplificado de notificações
  const notifications: any[] = [];

  // Fechar notificações ao clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }

    if (showNotifications || showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showNotifications, showUserMenu]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  return (
    <header className="bg-card border-b border-border py-4 px-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-6">
          <div className="flex flex-col">
            <h1 className="text-xl font-bold text-foreground">{getPageTitle(pathname)}</h1>
          </div>
        </div>

        {/* Campo de busca - oculto na página de receber-processo */}
        {pathname !== '/receber-processo' && (
          <div className="flex-1 max-w-xl mx-8">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={18} className="text-muted-foreground" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2.5 bg-background border border-input rounded-lg leading-5 placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all"
                  placeholder="Buscar processos, contratos, secretarias..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </form>
          </div>
        )}

        <div className="flex items-center space-x-3">
          <div className="relative" ref={notificationRef}>
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <span className="sr-only">Ver notificações</span>
              <div className="relative">
                <Bell size={18} />
                <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-destructive flex items-center justify-center text-xs text-destructive-foreground font-medium">
                  {notifications.length}
                </span>
              </div>
            </button>

            {showNotifications && (
              <div className="absolute right-0 top-12 w-80 bg-card border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <h3 className="font-semibold text-foreground">Notificações</h3>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="p-3 border-b border-border last:border-b-0 hover:bg-accent">
                      <div className="flex items-start space-x-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.type === 'warning' ? 'bg-yellow-500' :
                          notification.type === 'error' ? 'bg-red-500' : 'bg-green-500'
                        }`}></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">{notification.title}</p>
                          <p className="text-xs text-muted-foreground">{notification.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t border-border">
                  <Link
                    href="/processos"
                    className="text-sm text-primary hover:underline"
                    onClick={() => setShowNotifications(false)}
                  >
                    Ver todos os processos
                  </Link>
                </div>
              </div>
            )}
          </div>

          <ThemeToggle />

          <button
            onClick={() => alert('🎯 InovaProcess - Sistema Profissional de Gestão\n\n📋 MÓDULOS PRINCIPAIS:\n• Gestão Completa de Processos Licitatórios\n• Análise Automática de Editais com IA\n• Central de Relatórios Gerenciais\n• Controle de Usuários e Permissões\n• Gestão de Secretarias e Performance\n• Configurações Avançadas do Sistema\n\n🔍 BUSCA INTELIGENTE:\n• Digite qualquer termo na barra superior\n• Busca em processos, objetos, secretarias\n• Reconhece siglas (SSDAN, SEMAM, etc.)\n• Filtros avançados disponíveis\n• Resultados instantâneos\n\n⚡ FUNCIONALIDADES AVANÇADAS:\n• Demonstração de Eficiência CLMP\n• Rastreamento de Tempo e Local\n• Métricas de Performance\n• Alertas Automáticos\n• Compliance Lei 14.133/21\n• Análise de Conformidade\n\n🎯 DESENVOLVIDO PARA PREFEITURA DE MAUÁ\nSistema estratégico de defesa institucional')}
            className="p-2 rounded-lg text-muted-foreground hover:text-foreground hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <span className="sr-only">Ajuda</span>
            <HelpCircle size={18} />
          </button>

          <div className="border-l border-border h-6 mx-2"></div>

          {/* Menu do Usuário */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <div className="text-right">
                <p className="text-sm font-medium text-foreground">
                  {user?.displayName || user?.email?.split('@')[0] || 'Usuário'}
                </p>
                <p className="text-xs text-muted-foreground">
                  Gmail
                </p>
              </div>
              <div className="flex items-center space-x-1">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User size={16} className="text-primary" />
                </div>
                <ChevronDown size={14} className="text-muted-foreground" />
              </div>
            </button>

            {showUserMenu && (
              <div className="absolute right-0 top-12 w-64 bg-card border border-border rounded-lg shadow-lg z-50">
                <div className="p-4 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <User size={20} className="text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">
                        {user?.displayName || user?.email?.split('@')[0]}
                      </p>
                      <p className="text-xs text-muted-foreground">{user?.email}</p>
                      <div className="flex items-center space-x-1 mt-1">
                        <span className="text-xs px-2 py-0.5 bg-primary/10 text-primary rounded">
                          ADMIN
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Gmail
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-2">
                  <Link
                    href="/usuarios"
                    className="flex items-center space-x-2 w-full p-2 text-sm text-foreground hover:bg-accent rounded-md transition-colors"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <Settings size={16} />
                    <span>Configurações</span>
                  </Link>

                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 w-full p-2 text-sm text-foreground hover:bg-accent rounded-md transition-colors"
                  >
                    <LogOut size={16} />
                    <span>Sair</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}