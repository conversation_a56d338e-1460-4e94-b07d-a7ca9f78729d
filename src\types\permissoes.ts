/**
 * Sistema de Permissões e Habilitações do InovaProcess
 * Estrutura hierárquica de permissões para diferentes níveis de usuários
 */

// Tipos de permissões disponíveis no sistema
export type TipoPermissao = 
  // === PROCESSOS ===
  | 'processos.visualizar'           // Ver lista de processos
  | 'processos.visualizar.detalhes'  // Ver detalhes completos
  | 'processos.criar'                // Criar novos processos
  | 'processos.editar'               // Editar processos existentes
  | 'processos.tramitar'             // Tramitar processos
  | 'processos.excluir'              // Excluir processos
  | 'processos.corrigir'             // Corrigir dados de processos
  | 'processos.finalizar'            // Finalizar processos
  | 'processos.reabrir'              // Reabrir processos finalizados
  | 'processos.prioridade'           // Alterar prioridade
  | 'processos.anexos'               // Gerenciar anexos
  | 'processos.historico'            // Ver histórico completo
  
  // === ANÁLISES ===
  | 'analises.visualizar'            // Ver análises
  | 'analises.criar'                 // Criar análises
  | 'analises.editar'                // Editar análises
  | 'analises.aprovar'               // Aprovar análises
  | 'analises.rejeitar'              // Rejeitar análises
  | 'analises.ocr'                   // Usar OCR em documentos
  
  // === PESQUISA DE PREÇOS ===
  | 'precos.visualizar'              // Ver pesquisas de preços
  | 'precos.criar'                   // Criar pesquisas
  | 'precos.editar'                  // Editar pesquisas
  | 'precos.aprovar'                 // Aprovar pesquisas
  | 'precos.ratificar'               // Ratificar pesquisas
  | 'precos.graficos'                // Ver gráficos de preços
  | 'precos.relatorios.avancados'    // Relatórios avançados de preços
  | 'precos.exportar'                // Exportar dados de preços
  | 'precos.historico'               // Ver histórico completo de preços
  | 'precos.comparativo'             // Análises comparativas
  | 'precos.dashboard'               // Dashboard específico de preços
  
  // === CONTRATOS ===
  | 'contratos.visualizar'           // Ver contratos
  | 'contratos.criar'                // Criar contratos
  | 'contratos.editar'               // Editar contratos
  | 'contratos.gerenciar'            // Gerenciar contratos
  | 'contratos.finalizar'            // Finalizar contratos
  
  // === USUÁRIOS ===
  | 'usuarios.visualizar'            // Ver lista de usuários
  | 'usuarios.criar'                 // Criar usuários
  | 'usuarios.editar'                // Editar usuários
  | 'usuarios.excluir'               // Excluir usuários
  | 'usuarios.permissoes'            // Gerenciar permissões
  | 'usuarios.afastamentos'          // Gerenciar férias/afastamentos
  
  // === RELATÓRIOS ===
  | 'relatorios.basicos'             // Relatórios básicos
  | 'relatorios.avancados'           // Relatórios avançados
  | 'relatorios.exportar'            // Exportar relatórios
  | 'relatorios.dashboard'           // Ver dashboard completo
  | 'relatorios.metricas'            // Ver métricas detalhadas
  
  // === CONFIGURAÇÕES ===
  | 'config.sistema'                 // Configurações do sistema
  | 'config.backup'                  // Gerenciar backups
  | 'config.logs'                    // Ver logs do sistema
  | 'config.manutencao'              // Modo manutenção
  
  // === SECRETARIAS ===
  | 'secretarias.visualizar'         // Ver dados das secretarias
  | 'secretarias.editar'             // Editar dados das secretarias
  | 'secretarias.gerenciar'          // Gerenciar secretarias
  
  // === AUDITORIA ===
  | 'auditoria.visualizar'           // Ver logs de auditoria
  | 'auditoria.exportar'             // Exportar logs
  | 'auditoria.limpar'               // Limpar logs antigos;

// Perfis de usuário disponíveis
export type PerfilUsuario =
  | 'admin'                          // Administrador do sistema
  | 'coordenador'                    // Coordenador da CLMP
  | 'assessor'                       // Assessor da CLMP
  | 'analista'                       // Analista de processos
  | 'pesquisador'                    // Pesquisador de preços
  | 'secretaria'                     // Usuário de secretaria
  | 'consulta'                       // Apenas consulta
  | 'estagiario';                    // Estagiário

// Mapeamento de permissões por perfil
export const PERFIS_PERMISSOES: Record<PerfilUsuario, TipoPermissao[]> = {
  // === ADMINISTRADOR ===
  // Acesso total ao sistema
  admin: [
    // Processos - Acesso total
    'processos.visualizar', 'processos.visualizar.detalhes', 'processos.criar', 
    'processos.editar', 'processos.tramitar', 'processos.excluir', 'processos.corrigir',
    'processos.finalizar', 'processos.reabrir', 'processos.prioridade', 'processos.anexos',
    'processos.historico',
    
    // Análises - Acesso total
    'analises.visualizar', 'analises.criar', 'analises.editar', 'analises.aprovar',
    'analises.rejeitar', 'analises.ocr',
    
    // Preços - Acesso total
    'precos.visualizar', 'precos.criar', 'precos.editar', 'precos.aprovar', 'precos.ratificar',
    
    // Contratos - Acesso total
    'contratos.visualizar', 'contratos.criar', 'contratos.editar', 'contratos.gerenciar',
    'contratos.finalizar',
    
    // Usuários - Acesso total
    'usuarios.visualizar', 'usuarios.criar', 'usuarios.editar', 'usuarios.excluir',
    'usuarios.permissoes', 'usuarios.afastamentos',
    
    // Relatórios - Acesso total
    'relatorios.basicos', 'relatorios.avancados', 'relatorios.exportar',
    'relatorios.dashboard', 'relatorios.metricas',
    
    // Configurações - Acesso total
    'config.sistema', 'config.backup', 'config.logs', 'config.manutencao',
    
    // Secretarias - Acesso total
    'secretarias.visualizar', 'secretarias.editar', 'secretarias.gerenciar',
    
    // Auditoria - Acesso total
    'auditoria.visualizar', 'auditoria.exportar', 'auditoria.limpar'
  ],

  // === COORDENADOR ===
  // Coordenador da CLMP - Acesso amplo mas sem configurações críticas
  coordenador: [
    // Processos - Acesso quase total
    'processos.visualizar', 'processos.visualizar.detalhes', 'processos.criar',
    'processos.editar', 'processos.tramitar', 'processos.corrigir', 'processos.finalizar',
    'processos.reabrir', 'processos.prioridade', 'processos.anexos', 'processos.historico',
    
    // Análises - Acesso total
    'analises.visualizar', 'analises.criar', 'analises.editar', 'analises.aprovar',
    'analises.rejeitar', 'analises.ocr',
    
    // Preços - Acesso total
    'precos.visualizar', 'precos.criar', 'precos.editar', 'precos.aprovar', 'precos.ratificar',
    
    // Contratos - Acesso amplo
    'contratos.visualizar', 'contratos.criar', 'contratos.editar', 'contratos.gerenciar',
    'contratos.finalizar',
    
    // Usuários - Limitado
    'usuarios.visualizar', 'usuarios.afastamentos',
    
    // Relatórios - Acesso amplo
    'relatorios.basicos', 'relatorios.avancados', 'relatorios.exportar',
    'relatorios.dashboard', 'relatorios.metricas',
    
    // Secretarias - Visualização
    'secretarias.visualizar',
    
    // Auditoria - Visualização
    'auditoria.visualizar', 'auditoria.exportar'
  ],

  // === ASSESSOR ===
  // Assessor da CLMP - Foco em análises e processos
  assessor: [
    // Processos - Acesso amplo
    'processos.visualizar', 'processos.visualizar.detalhes', 'processos.criar',
    'processos.editar', 'processos.tramitar', 'processos.corrigir', 'processos.prioridade',
    'processos.anexos', 'processos.historico',
    
    // Análises - Acesso total
    'analises.visualizar', 'analises.criar', 'analises.editar', 'analises.aprovar',
    'analises.rejeitar', 'analises.ocr',
    
    // Preços - Acesso amplo
    'precos.visualizar', 'precos.criar', 'precos.editar', 'precos.aprovar',
    
    // Contratos - Visualização e edição
    'contratos.visualizar', 'contratos.editar',
    
    // Relatórios - Básicos e avançados
    'relatorios.basicos', 'relatorios.avancados', 'relatorios.exportar',
    'relatorios.dashboard',
    
    // Secretarias - Visualização
    'secretarias.visualizar'
  ],

  // === ANALISTA ===
  // Analista de processos - Foco operacional
  analista: [
    // Processos - Operacional
    'processos.visualizar', 'processos.visualizar.detalhes', 'processos.criar',
    'processos.editar', 'processos.tramitar', 'processos.anexos', 'processos.historico',
    
    // Análises - Criar e editar
    'analises.visualizar', 'analises.criar', 'analises.editar', 'analises.ocr',
    
    // Preços - Básico
    'precos.visualizar', 'precos.criar', 'precos.editar',
    
    // Contratos - Visualização
    'contratos.visualizar',
    
    // Relatórios - Básicos
    'relatorios.basicos', 'relatorios.dashboard',
    
    // Secretarias - Visualização
    'secretarias.visualizar'
  ],

  // === PESQUISADOR ===
  // Pesquisador de preços - Foco exclusivo em pesquisa de preços
  pesquisador: [
    // Preços - Acesso completo ao módulo
    'precos.visualizar', 'precos.criar', 'precos.editar',

    // Processos - Apenas visualização para contexto
    'processos.visualizar', 'processos.visualizar.detalhes',

    // Relatórios - Básicos relacionados a preços
    'relatorios.basicos'
  ],

  // === SECRETARIA ===
  // Usuário de secretaria - Foco em seus processos
  secretaria: [
    // Processos - Limitado aos seus
    'processos.visualizar', 'processos.visualizar.detalhes', 'processos.criar',
    'processos.anexos',
    
    // Análises - Visualização
    'analises.visualizar',
    
    // Preços - Visualização
    'precos.visualizar',
    
    // Contratos - Visualização
    'contratos.visualizar',
    
    // Relatórios - Básicos
    'relatorios.basicos'
  ],

  // === CONSULTA ===
  // Apenas visualização
  consulta: [
    'processos.visualizar',
    'analises.visualizar',
    'precos.visualizar',
    'contratos.visualizar',
    'relatorios.basicos'
  ],

  // === ESTAGIÁRIO ===
  // Acesso muito limitado
  estagiario: [
    'processos.visualizar',
    'analises.visualizar'
  ]
};

// Descrições dos perfis para interface (PERSONALIZÁVEIS)
export const DESCRICOES_PERFIS: Record<PerfilUsuario, string> = {
  admin: 'Administrador do sistema com acesso total',
  coordenador: 'Coordenador da CLMP com amplos poderes de gestão',
  assessor: 'Assessor da CLMP focado em análises e processos',
  analista: 'Analista de processos com foco operacional',
  pesquisador: 'Pesquisador de preços com acesso exclusivo ao módulo de pesquisa',
  secretaria: 'Usuário de secretaria com acesso aos seus processos',
  consulta: 'Acesso apenas para consulta e visualização',
  estagiario: 'Estagiário com acesso muito limitado'
};

// Nomes personalizáveis dos perfis (VOCÊ PODE ALTERAR ESTES!)
export const NOMES_PERFIS: Record<PerfilUsuario, string> = {
  admin: 'Administrador',
  coordenador: 'Coordenador CLMP',
  assessor: 'Assessor CLMP',
  analista: 'Analista',
  pesquisador: 'Pesquisador de Preços',
  secretaria: 'Secretaria',
  consulta: 'Consulta',
  estagiario: 'Estagiário'
};

// Função para obter nome personalizado do perfil
export function obterNomePerfil(perfil: PerfilUsuario): string {
  return NOMES_PERFIS[perfil] || perfil;
}

// Cores dos perfis para interface
export const CORES_PERFIS: Record<PerfilUsuario, string> = {
  admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  coordenador: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
  assessor: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
  analista: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
  pesquisador: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
  secretaria: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300',
  consulta: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
  estagiario: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300'
};

/**
 * Verifica se um usuário tem uma permissão específica
 */
export function temPermissao(perfil: PerfilUsuario, permissao: TipoPermissao): boolean {
  return PERFIS_PERMISSOES[perfil]?.includes(permissao) || false;
}

/**
 * Verifica se um usuário pode acessar uma funcionalidade
 */
export function podeAcessar(perfil: PerfilUsuario, permissoes: TipoPermissao[]): boolean {
  return permissoes.some(p => temPermissao(perfil, p));
}

/**
 * Obtém todas as permissões de um perfil
 */
export function obterPermissoesPerfil(perfil: PerfilUsuario): TipoPermissao[] {
  return PERFIS_PERMISSOES[perfil] || [];
}
