/**
 * 📝 SISTEMA DE DESPACHOS AUTOMÁTICOS - PESQUISA DE PREÇOS
 * Gera despachos padronizados do pesquisador para o Coordenador e SAJ
 * 🔧 COM FUNÇÃO DE EDIÇÃO ESPECIAL HABILITÁVEL
 */

export interface DadosPesquisa {
  numeroProcesso: string;
  objeto: string;
  pesquisador: string;
  tipoPesquisa: 'simples' | 'combinada';
  resultadosPNCP: {
    total: number;
    precoMedio: number;
    desvio: number;
    orgaosConsultados: number;
  };
  resultadosRegionais?: {
    total: number;
    fornecedores: string[];
    metodologiaAplicada: string;
  };
  dataFinalizacao: string;
  observacoes?: string;
  // 🔧 CONTROLE DE EDIÇÃO ESPECIAL
  edicaoEspecialHabilitada?: boolean;
  motivoEdicaoEspecial?: string;
  textoEditadoPesquisador?: string;
  dataHabilitacaoEdicao?: string;
  habilitadoPor?: string;
}

export interface DespachoGerado {
  tipo: 'pesquisador_para_coordenador' | 'coordenador_para_saj';
  conteudo: string;
  editavel: boolean;
  metadados: {
    processo: string;
    data: string;
    responsavel: string;
    tipoPesquisa: string;
    edicaoEspecial?: {
      habilitada: boolean;
      motivo?: string;
      habilitadoPor?: string;
      dataHabilitacao?: string;
    };
  };
}

/**
 * 🎯 GERADOR DE DESPACHOS DO PESQUISADOR
 */
export class GeradorDespachosPesquisa {
  
  /**
   * 🔧 HABILITAR EDIÇÃO ESPECIAL (Só o Coordenador pode fazer)
   */
  static habilitarEdicaoEspecial(
    numeroProcesso: string, 
    motivo: string, 
    coordenador: string
  ): boolean {
    // Simular salvamento no banco/localStorage
    const dadosEdicao = {
      processo: numeroProcesso,
      habilitada: true,
      motivo,
      habilitadoPor: coordenador,
      dataHabilitacao: new Date().toISOString(),
      utilizada: false
    };
    
    localStorage.setItem(`edicao_especial_${numeroProcesso}`, JSON.stringify(dadosEdicao));
    
    console.log(`🔧 EDIÇÃO ESPECIAL HABILITADA para processo ${numeroProcesso}`);
    console.log(`📝 Motivo: ${motivo}`);
    console.log(`👤 Por: ${coordenador}`);
    
    return true;
  }
  
  /**
   * 🔍 VERIFICAR SE EDIÇÃO ESPECIAL ESTÁ HABILITADA
   */
  static verificarEdicaoEspecial(numeroProcesso: string): boolean {
    const dados = localStorage.getItem(`edicao_especial_${numeroProcesso}`);
    if (!dados) return false;
    
    const edicao = JSON.parse(dados);
    return edicao.habilitada && !edicao.utilizada;
  }
  
  /**
   * 🔒 DESABILITAR EDIÇÃO ESPECIAL (Após uso)
   */
  static desabilitarEdicaoEspecial(numeroProcesso: string): void {
    const dados = localStorage.getItem(`edicao_especial_${numeroProcesso}`);
    if (dados) {
      const edicao = JSON.parse(dados);
      edicao.utilizada = true;
      edicao.dataUtilizacao = new Date().toISOString();
      localStorage.setItem(`edicao_especial_${numeroProcesso}`, JSON.stringify(edicao));
      
      console.log(`🔒 EDIÇÃO ESPECIAL DESABILITADA para processo ${numeroProcesso}`);
    }
  }
  
  /**
   * Gera despacho do pesquisador para o coordenador
   */
  static gerarDespachoPesquisador(dados: DadosPesquisa): DespachoGerado {
    const edicaoHabilitada = this.verificarEdicaoEspecial(dados.numeroProcesso);
    
    // Se tem texto editado pelo pesquisador, usa ele
    let conteudo: string;
    if (dados.textoEditadoPesquisador && edicaoHabilitada) {
      conteudo = dados.textoEditadoPesquisador;
      // Desabilita automaticamente após uso
      this.desabilitarEdicaoEspecial(dados.numeroProcesso);
    } else {
      // Usa template padrão
      conteudo = dados.tipoPesquisa === 'simples' 
        ? this.gerarDespachoSimples(dados)
        : this.gerarDespachoCombinado(dados);
    }
    
    const dadosEdicao = localStorage.getItem(`edicao_especial_${dados.numeroProcesso}`);
    const infoEdicao = dadosEdicao ? JSON.parse(dadosEdicao) : null;
    
    return {
      tipo: 'pesquisador_para_coordenador',
      conteudo,
      editavel: edicaoHabilitada,
      metadados: {
        processo: dados.numeroProcesso,
        data: dados.dataFinalizacao,
        responsavel: dados.pesquisador,
        tipoPesquisa: dados.tipoPesquisa,
        edicaoEspecial: infoEdicao ? {
          habilitada: infoEdicao.habilitada && !infoEdicao.utilizada,
          motivo: infoEdicao.motivo,
          habilitadoPor: infoEdicao.habilitadoPor,
          dataHabilitacao: infoEdicao.dataHabilitacao
        } : undefined
      }
    };
  }
  
  /**
   * Gera despacho do coordenador para SAJ
   */
  static gerarDespachoCoordenaorSAJ(dados: DadosPesquisa): DespachoGerado {
    const dataAtual = new Date().toLocaleDateString('pt-BR');
    
    const conteudo = `PREFEITURA MUNICIPAL DE MAUÁ
CLMP - Coordenadoria de Licitações, Materiais e Patrimônio

PARA: SAJ - Setor de Assessoria Jurídica
DE: Coordenador de Licitações, Materiais e Patrimônio
PROCESSO: ${dados.numeroProcesso}
DATA: ${dataAtual}
ASSUNTO: Encaminhamento para Parecer Jurídico

Senhor(a) Assessor(a) Jurídico(a),

Conforme solicitado pelo pesquisador responsável, encaminho o processo em epígrafe para emissão de parecer jurídico.

A pesquisa de preços foi concluída e aprovada por esta Coordenadoria, estando o processo apto ao prosseguimento do certame licitatório.

OBJETO: ${dados.objeto}

PESQUISA REALIZADA:
- Tipo: ${dados.tipoPesquisa === 'simples' ? 'Pesquisa PNCP' : 'Pesquisa Combinada PNCP + Regional'}
- Responsável: ${dados.pesquisador}
- Data de conclusão: ${dados.dataFinalizacao}

DOCUMENTOS ANEXOS:
- Mapa de preços consolidado
- Despacho fundamentado do pesquisador
- Planilhas de análise técnica
${dados.tipoPesquisa === 'combinada' ? '- Cotações de fornecedores regionais processadas' : ''}

O processo encontra-se em condições de prosseguir para a fase de elaboração do edital, após a emissão do competente parecer jurídico.

Atenciosamente,

[Nome do Coordenador]
Coordenador CLMP
Prefeitura Municipal de Mauá`;

    return {
      tipo: 'coordenador_para_saj',
      conteudo,
      editavel: false, // Coordenador sempre pode editar
      metadados: {
        processo: dados.numeroProcesso,
        data: dataAtual,
        responsavel: 'Coordenador CLMP',
        tipoPesquisa: dados.tipoPesquisa
      }
    };
  }
  
  /**
   * 🟢 DESPACHO PESQUISA SIMPLES (Só PNCP)
   */
  private static gerarDespachoSimples(dados: DadosPesquisa): string {
    return `PREFEITURA MUNICIPAL DE MAUÁ
CLMP - Coordenadoria de Licitações, Materiais e Patrimônio

PARA: Coordenador de Licitações, Materiais e Patrimônio
DE: ${dados.pesquisador}
PROCESSO: ${dados.numeroProcesso}
DATA: ${dados.dataFinalizacao}
ASSUNTO: Conclusão de Pesquisa de Preços - Solicitação de Encaminhamento à SAJ

Senhor Coordenador,

Informo a conclusão da pesquisa de preços do processo em epígrafe, realizada conforme metodologia estabelecida pela legislação vigente.

OBJETO: ${dados.objeto}

METODOLOGIA APLICADA:
Pesquisa exclusiva no Portal Nacional de Contratações Públicas (PNCP)

FUNDAMENTAÇÃO LEGAL:
Art. 23, §1º da Lei Federal 14.133/21

RESULTADOS OBTIDOS:
- Total de registros analisados: ${dados.resultadosPNCP.total}
- Órgãos consultados: ${dados.resultadosPNCP.orgaosConsultados}
- Preço médio apurado: R$ ${dados.resultadosPNCP.precoMedio.toFixed(2)}
- Desvio padrão: ${dados.resultadosPNCP.desvio.toFixed(2)}%
- Critério Decreto 9.337/24: Aplicado (exclusão de preços ±50% da média)

JUSTIFICATIVA DA METODOLOGIA:
A base de dados oficial do Portal Nacional de Contratações Públicas (PNCP) apresentou quantidade suficiente de registros para formação adequada de preços, dispensando a necessidade de pesquisa complementar junto a fornecedores regionais.

CONCLUSÃO:
A pesquisa atende integralmente aos requisitos legais e técnicos necessários para subsidiar o processo licitatório.

${dados.observacoes ? `OBSERVAÇÕES ADICIONAIS:\n${dados.observacoes}\n\n` : ''}Solicito o encaminhamento do processo à SAJ para emissão de parecer jurídico e prosseguimento do certame licitatório.

Atenciosamente,

${dados.pesquisador}
Pesquisador de Preços
CLMP - Prefeitura Municipal de Mauá`;
  }
  
  /**
   * 🟡 DESPACHO PESQUISA COMBINADA (PNCP + Regional)
   */
  private static gerarDespachoCombinado(dados: DadosPesquisa): string {
    const fornecedoresTexto = dados.resultadosRegionais?.fornecedores.join(', ') || '';
    
    return `PREFEITURA MUNICIPAL DE MAUÁ
CLMP - Coordenadoria de Licitações, Materiais e Patrimônio

PARA: Coordenador de Licitações, Materiais e Patrimônio
DE: ${dados.pesquisador}
PROCESSO: ${dados.numeroProcesso}
DATA: ${dados.dataFinalizacao}
ASSUNTO: Conclusão de Pesquisa de Preços Combinada - Solicitação de Encaminhamento à SAJ

Senhor Coordenador,

Informo a conclusão da pesquisa de preços do processo em epígrafe, realizada mediante metodologia combinada para maior robustez técnica e jurídica.

OBJETO: ${dados.objeto}

METODOLOGIA APLICADA:
Pesquisa combinada PNCP + Fornecedores Regionais

FUNDAMENTAÇÃO LEGAL:
Art. 23, §4º da Lei Federal 14.133/21 c/c Decreto 9.337/24

JUSTIFICATIVA DA METODOLOGIA COMBINADA:
A metodologia combinada, expressamente prevista no art. 23, §4º da Lei 14.133/21, proporciona maior segurança jurídica ao processo licitatório, demonstrando zelo na aplicação dos recursos públicos.

CONCLUSÃO:
A pesquisa combinada oferece fundamentação técnica e jurídica robusta, atendendo integralmente aos requisitos legais para formação de preços.

${dados.observacoes ? `OBSERVAÇÕES ADICIONAIS:\n${dados.observacoes}\n\n` : ''}Solicito o encaminhamento do processo à SAJ para emissão de parecer jurídico e prosseguimento do certame licitatório.

Atenciosamente,

${dados.pesquisador}
Pesquisador de Preços
CLMP - Prefeitura Municipal de Mauá`;
  }
}
