import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rotas que não precisam de autenticação
const PUBLIC_ROUTES = ['/', '/login'];

// Rotas que sempre redirecionam para dashboard se autenticado
const AUTH_ROUTES = ['/login'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Verificar se tem sessão (simulação - em produção seria JWT)
  const sessionCookie = request.cookies.get('inovaprocess_session');
  const isAuthenticated = sessionCookie?.value ? true : false;

  // Se está tentando acessar rota de auth e já está autenticado
  if (AUTH_ROUTES.includes(pathname) && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Se está tentando acessar rota protegida sem estar autenticado
  if (!PUBLIC_ROUTES.includes(pathname) && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
