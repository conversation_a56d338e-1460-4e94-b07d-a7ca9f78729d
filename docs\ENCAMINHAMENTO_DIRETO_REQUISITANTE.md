# 🔄 Sistema de Detecção de Encaminhamento Direto pelo Requisitante

## 📋 **PROBLEMA IDENTIFICADO**

Em alguns casos, o **requisitante** (secretaria) encaminha o processo diretamente para:
- **SF** (Secretaria de Finanças) - para impacto financeiro
- **SAJ** (Secretaria de Assuntos Jurídicos) - para parecer jurídico

**SEM PASSAR PELA CLMP** no fluxo normal.

## ⚠️ **IMPACTOS**

### **Problemas Causados:**
1. **Cronologia incompleta** - não registra que houve encaminhamento direto
2. **Tempos médios distorcidos** - SF e SAJ aparecem com tempo maior
3. **Falta de rastreabilidade** - não sabemos quando/por que foi direto
4. **Métricas incorretas** - CLMP pode aparecer como "lenta" incorretamente

### **Situação Específica - Processo 169/2025:**
- Requisitante: **SG** (Secretaria de Governo)
- Status: "Para prosseguimento após parecer jurídico"
- Local: **CLMP** (mas veio da SAJ)
- **Não temos**: data exata do encaminhamento direto nem tempo na SAJ

## 🔍 **DETECÇÃO AUTOMÁTICA**

### **Indicadores de Encaminhamento Direto:**

```typescript
// Status que indicam encaminhamento direto
const statusEncaminhamentoDireto = [
  'para prosseguimento após impacto financeiro',
  'encaminhado para manifestação - adequação ao pca e disponibilidade orçamentária',
  'para prosseguimento após parecer jurídico',
  'encaminhado para emissão de parecer jurídico'
];

// Local atual é SF ou SAJ
const localSFouSAJ = local.includes('sf') || local.includes('saj');

// Padrão temporal suspeito
const diasEntreCLMP = calcularDias(dataInicio, dataEntradaCLMP);
if (diasEntreCLMP > 30 && statusIndicaEncaminhamentoDireto) {
  // Possível encaminhamento direto
}
```

## 🚨 **SISTEMA DE ALERTAS**

### **Alerta Gerado:**
- **Tipo:** `ENCAMINHAMENTO_DIRETO_REQUISITANTE`
- **Urgência:** MÉDIA
- **Destinatário:** Responsável atual (ex: Nilva)
- **Ação:** Confirmar se houve encaminhamento direto

### **Informações do Alerta:**
```typescript
{
  titulo: "CONFIRMAR ENCAMINHAMENTO DIRETO - 169/2025",
  preview: "Requisitante pode ter encaminhado direto para análise externa",
  detalhes: {
    valor: "R$ 519.900,00",
    responsavel: "Qualquer usuário CLMP",
    local: "CLMP",
    motivo: "Status indica retorno de análise externa (SF/SAJ)",
    requisitante: "SG"
  }
}
```

## 📊 **REGISTRO NA CRONOLOGIA**

### **Evento Especial Adicionado:**
```
⚠️ ENCAMINHAMENTO DIRETO pelo requisitante para SAJ (data estimada - dias úteis)
Local: SG
Data: [estimada - 3 dias ÚTEIS após início]
Cor: Laranja (destaque especial)
```

### **Ajuste na Cronologia:**
- **Entrada na CLMP** vira → **"Retorno à CLMP após análise externa"**
- **Cor diferenciada** para eventos especiais
- **Aviso visual** para confirmar com responsável

## ⏱️ **IMPACTO NOS TEMPOS**

### **Cálculo Ajustado:**
```typescript
if (houveEncaminhamentoDireto) {
  // Tempo na secretaria: apenas 20% do período inicial
  tempoSecretaria = diasIniciais * 0.2;
  
  // Tempo em análise externa: 80% do período inicial
  tempoAnaliseExterna = diasIniciais * 0.8;
  
  // Tempo na CLMP: mais conservador (20% em vez de 30%)
  tempoCLMP = diasAtuais * 0.2;
}
```

### **Detalhamento Exibido:**
- ✅ **Secretaria (inicial):** X dias
- ✅ **CLMP:** Y dias  
- ⚠️ **Análise Externa (SAJ):** Z dias*
- 📝 **Nota:** *Tempo em análise externa por encaminhamento direto

## 🎯 **FLUXO DE CONFIRMAÇÃO À PROVA DE FALHAS**

### **1. Detecção Automática**
- Sistema identifica padrão suspeito
- Gera alerta para QUALQUER usuário da CLMP

### **2. Alerta Visual**
- Banner laranja na página do processo
- Destaque na cronologia
- Informações para confirmação

### **3. Múltiplas Oportunidades de Confirmação**
- **Tathi/Isabela:** Ao dar entrada e inserir status "após parecer/impacto"
- **Usuário seguinte:** Se escapar da entrada, ao receber o processo
- **Qualquer usuário:** Ao detectar a situação durante processamento
- **Sistema:** Não deixa escapar - sempre alerta quando detecta o padrão

### **4. Registro Permanente**
- Cronologia atualizada com evento especial
- Tempos ajustados para não penalizar métricas
- Rastreabilidade completa do fluxo
- **Data estimada em DIAS ÚTEIS** (não conta fins de semana)

## 📈 **BENEFÍCIOS**

### **Para a CLMP:**
- ✅ Métricas mais precisas
- ✅ Tempos médios corretos
- ✅ Rastreabilidade completa
- ✅ Transparência no fluxo

### **Para SF/SAJ:**
- ✅ Não penaliza tempo médio deles
- ✅ Identifica casos de encaminhamento direto
- ✅ Melhora planejamento de recursos

### **Para Gestão:**
- ✅ Visibilidade de fluxos alternativos
- ✅ Dados mais confiáveis para decisões
- ✅ Identificação de padrões por secretaria

## 🔧 **IMPLEMENTAÇÃO TÉCNICA**

### **Arquivos Modificados:**
- `src/lib/sistemaPrioridadesAlertas.ts` - Detecção e alertas
- `src/app/processos/[id]/page.tsx` - Interface e cronologia
- `docs/ENCAMINHAMENTO_DIRETO_REQUISITANTE.md` - Documentação

### **Funções Principais:**
- `verificarEncaminhamentoDiretoRequisitante()` - Detecção
- `detectarEncaminhamentoDireto()` - Verificação local
- `criarCronologiaDetalhada()` - Cronologia ajustada
- `calcularTemposSimples()` - Tempos corrigidos

## 🛡️ **SISTEMA À PROVA DE FALHAS**

### **Múltiplos Pontos de Detecção:**
1. **Na entrada:** Tathi/Isabela ao inserir status "após parecer/impacto"
2. **No processamento:** Qualquer usuário ao abrir o processo
3. **Na tramitação:** Sistema alerta automaticamente
4. **Na revisão:** Usuário seguinte sempre vê o alerta

### **Não Tem Como Escapar:**
- ✅ **Detecção automática** por padrão de status
- ✅ **Alerta visual** sempre presente
- ✅ **Múltiplos usuários** podem confirmar
- ✅ **Sistema persistente** - alerta até ser confirmado

### **Cálculo de Dias Úteis:**
```typescript
// Função que pula fins de semana
const calcularDataUtilEstimada = (dataInicio: Date, diasUteis: number) => {
  while (diasAdicionados < diasUteis) {
    data.setDate(data.getDate() + 1);
    // Só conta segunda a sexta (1-5)
    if (data.getDay() >= 1 && data.getDay() <= 5) {
      diasAdicionados++;
    }
  }
};
```

## 📝 **PRÓXIMOS PASSOS**

1. **Testar** com processo 169/2025
2. **Confirmar** detecção automática
3. **Validar** com equipe CLMP o fluxo de confirmação
4. **Expandir** para outros casos similares
5. **Criar relatório** de encaminhamentos diretos por secretaria
6. **Treinar usuários** sobre o novo sistema de alertas
