'use client';

import { Processo } from '@/types/processo';
import { temFonteNaoTesouro, temGargaloCritico, temRetrabalhoReal, getFontesRecursos } from '@/lib/processoUtils';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Clock, RefreshCw, DollarSign } from 'lucide-react';

interface AlertaPrioridadeProps {
  processo: Processo;
  showDetails?: boolean;
  compact?: boolean; // Nova prop para versão compacta
}

export default function AlertaPrioridade({ processo, showDetails = false, compact = false }: AlertaPrioridadeProps) {
  const fonteNaoTesouro = temFonteNaoTesouro(processo);
  const gargalo = temGargaloCritico(processo);
  const retrabalho = temRetrabalhoReal(processo);
  const fontes = getFontesRecursos(processo);
  
  // Se não há alertas, não renderiza nada
  if (!fonteNaoTesouro && !gargalo.tipo && !retrabalho) {
    return null;
  }

  // Versão compacta - apenas badges pequenos e discretos
  if (compact) {
    return (
      <div className="flex flex-wrap gap-1">
        {/* REMOVIDO: Badge "Externos" para evitar conflito com localização real do CSV */}
        {/* A prioridade alta por fonte externa é mantida, mas não mostra badge conflitante */}
        {gargalo.tipo && (
          <Badge variant="warning" className="text-xs px-2 py-0.5">
            ⏱️ {gargalo.tipo}
          </Badge>
        )}
        {retrabalho && (
          <Badge variant="outline" className="text-xs px-2 py-0.5">
            🔄 Retrabalho
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* REMOVIDO: Alerta de Prioridade Alta - Fontes não-Tesouro */}
      {/* A lógica de prioridade alta é mantida internamente, mas não exibe badge conflitante com localização */}
      
      {/* Alerta de Gargalo Crítico */}
      {gargalo.tipo && (
        <div className="flex items-center space-x-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <Clock className="h-5 w-5 text-orange-600 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="warning" className="text-xs font-bold">
                ⏱️ GARGALO CRÍTICO
              </Badge>
              <span className="text-sm font-medium text-orange-800">
                {gargalo.tipo === 'SF' ? 'SECRETARIA DE FINANÇAS' : 'SECRETARIA DE ASSUNTOS JURÍDICOS'}
              </span>
            </div>
            {showDetails && (
              <div className="mt-2">
                <p className="text-xs text-orange-700">
                  Status: {gargalo.status}
                </p>
                <p className="text-xs text-orange-700">
                  {gargalo.tipo === 'SF' 
                    ? 'Processo aguardando análise orçamentária - pode impactar no tempo total.'
                    : 'Processo aguardando parecer jurídico - pode impactar no tempo total.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Alerta de Retrabalho */}
      {retrabalho && (
        <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <RefreshCw className="h-5 w-5 text-yellow-600 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="warning" className="text-xs font-bold">
                🔄 RETRABALHO
              </Badge>
              <span className="text-sm font-medium text-yellow-800">
                PROCESSO COM RETRABALHO
              </span>
            </div>
            {showDetails && (
              <div className="mt-2">
                <p className="text-xs text-yellow-700">
                  Status: {processo.STATUS}
                </p>
                <p className="text-xs text-yellow-700">
                  Processo retornou para adequações ou foi encaminhado para a secretaria.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
