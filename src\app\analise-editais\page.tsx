'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  FileText,
  Upload,
  CheckCircle,
  XCircle,
  Send,
  RefreshCw,
  Download,
  Eye,
  Brain,
  Scale,
  AlertTriangle,
  TrendingUp,
  FileCheck,
  Zap,
  Search
} from 'lucide-react';
import DistribuirPesquisa from '@/components/DistribuirPesquisa';

interface VersaoDocumento {
  versao: number;
  arquivo: File;
  dataUpload: Date;
  observacoes: string;
  scoreAnterior?: number;
  analiseAlteracao?: AnaliseAlteracao;
}

interface ProblemaIdentificado {
  id: string;
  descricao: string;
  categoria: string;
  documentoAfetado: 'etp' | 'edital' | 'tr';
  primeiraDeteccao: number; // Número do retorno onde foi detectado pela primeira vez
  status: 'pendente' | 'corrigido' | 'nao_aplicavel';
}

interface AnaliseAlteracao {
  percentualAlteracao: number;
  tipoAlteracao: 'ajuste_normal' | 'revisao_substancial' | 'documento_novo';
  principaisAlteracoes: string[];
  impactoAnalise: 'continua_analise' | 'nova_analise_necessaria';
}

interface HistoricoProcesso {
  numeroProcesso: string;
  versoes: {
    etp: VersaoDocumento[];
    edital: VersaoDocumento[];
    tr: VersaoDocumento[];
  };
  retornos: {
    numero: number;
    data: Date;
    motivo: string;
    problemas: string[];
    scoreAnterior: number;
    scoreAtual?: number;
    problemasIdentificados: ProblemaIdentificado[]; // Problemas detectados neste retorno
    documentosAlterados: ('etp' | 'edital' | 'tr')[]; // Quais documentos foram alterados
    analisesAlteracao?: {[key: string]: AnaliseAlteracao}; // Análises de alteração por documento
  }[];
  problemasHistorico: ProblemaIdentificado[]; // Todos os problemas já identificados
  statusAtual: 'primeira_analise' | 'aguardando_ajustes' | 'reanalise' | 'aprovado' | 'reprovado_final';
}

export default function AnaliseEditaisPage() {
  const [loading, setLoading] = useState(false);
  const [documentosUpload, setDocumentosUpload] = useState<{[key: string]: File | null}>({
    etp: null,
    edital: null,
    tr: null
  });
  const [documentosRevisados, setDocumentosRevisados] = useState<{[key: string]: File | null}>({
    etp: null,
    edital: null,
    tr: null
  });
  const [analiseRealizada, setAnaliseRealizada] = useState(false);
  const [numeroProcesso, setNumeroProcesso] = useState('');
  const [historicoProcesso, setHistoricoProcesso] = useState<HistoricoProcesso | null>(null);
  const [modoReanalise, setModoReanalise] = useState(false);
  const [observacoesAjuste, setObservacoesAjuste] = useState('');
  const [mostrarDistribuicao, setMostrarDistribuicao] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileUpload = (tipo: string, file: File, isRevisado = false) => {
    if (isRevisado) {
      setDocumentosRevisados(prev => ({
        ...prev,
        [tipo]: file
      }));
    } else {
      setDocumentosUpload(prev => ({
        ...prev,
        [tipo]: file
      }));
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    handleMultipleFileUpload(files);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      handleMultipleFileUpload(files);
    }
  };

  const handleMultipleFileUpload = (files: File[]) => {
    // Detectar automaticamente o tipo de documento baseado no nome
    files.forEach(file => {
      const fileName = file.name.toLowerCase();
      let tipo = '';

      if (fileName.includes('etp') || fileName.includes('estudo')) {
        tipo = 'etp';
      } else if (fileName.includes('edital')) {
        tipo = 'edital';
      } else if (fileName.includes('tr') || fileName.includes('termo')) {
        tipo = 'tr';
      } else {
        // Se não conseguir detectar, usar o primeiro slot vazio
        if (!documentosUpload.etp) tipo = 'etp';
        else if (!documentosUpload.edital) tipo = 'edital';
        else if (!documentosUpload.tr) tipo = 'tr';
      }

      if (tipo) {
        handleFileUpload(tipo, file);
      }
    });
  };

  // Função para analisar alterações em documentos
  const analisarAlteracaoDocumento = async (novoArquivo: File, versaoAnterior: File): Promise<AnaliseAlteracao> => {
    // Simular análise de alteração (em produção, usar OCR + diff de texto)
    const simulacaoAlteracao = Math.random() * 100;

    let tipoAlteracao: 'ajuste_normal' | 'revisao_substancial' | 'documento_novo';
    let principaisAlteracoes: string[] = [];
    let impactoAnalise: 'continua_analise' | 'nova_analise_necessaria';

    if (simulacaoAlteracao >= 70) {
      tipoAlteracao = 'documento_novo';
      impactoAnalise = 'nova_analise_necessaria';
      principaisAlteracoes = [
        'Objeto principal completamente alterado',
        'Especificações técnicas totalmente diferentes',
        'Valores e quantitativos substancialmente modificados',
        'Estrutura do documento reformulada'
      ];
    } else if (simulacaoAlteracao >= 30) {
      tipoAlteracao = 'revisao_substancial';
      impactoAnalise = 'continua_analise';
      principaisAlteracoes = [
        'Especificações técnicas significativamente alteradas',
        'Alguns valores e quantitativos modificados',
        'Ajustes importantes no texto'
      ];
    } else {
      tipoAlteracao = 'ajuste_normal';
      impactoAnalise = 'continua_analise';
      principaisAlteracoes = [
        'Correções pontuais no texto',
        'Pequenos ajustes de formatação',
        'Alterações menores nas especificações'
      ];
    }

    return {
      percentualAlteracao: Math.round(simulacaoAlteracao),
      tipoAlteracao,
      principaisAlteracoes,
      impactoAnalise
    };
  };

  const buscarHistoricoProcesso = async (numero: string) => {
    // Simular busca no backend
    if (numero.includes('CC 045/2024')) {
      const historico: HistoricoProcesso = {
        numeroProcesso: numero,
        versoes: {
          etp: [
            {
              versao: 1,
              arquivo: new File([''], 'ETP_v1.pdf'),
              dataUpload: new Date('2024-12-15'),
              observacoes: 'Versão inicial',
              scoreAnterior: 65
            }
          ],
          edital: [
            {
              versao: 1,
              arquivo: new File([''], 'Edital_v1.pdf'),
              dataUpload: new Date('2024-12-15'),
              observacoes: 'Versão inicial',
              scoreAnterior: 70
            }
          ],
          tr: []
        },
        retornos: [
          {
            numero: 1,
            data: new Date('2024-12-16'),
            motivo: 'Conflito de objetos detectado - Análise inicial',
            problemas: [
              'ETP menciona medicamentos mas Edital refere-se a ração animal',
              'Especificações técnicas inadequadas',
              'Falta de detalhamento no TR'
            ],
            scoreAnterior: 65,
            problemasIdentificados: [
              {
                id: 'prob_001',
                descricao: 'ETP menciona medicamentos mas Edital refere-se a ração animal',
                categoria: 'Conflito de Objeto',
                documentoAfetado: 'etp',
                primeiraDeteccao: 1,
                status: 'pendente'
              },
              {
                id: 'prob_002',
                descricao: 'Especificações técnicas inadequadas',
                categoria: 'Especificação Técnica',
                documentoAfetado: 'tr',
                primeiraDeteccao: 1,
                status: 'pendente'
              }
            ],
            documentosAlterados: []
          },
          {
            numero: 2,
            data: new Date('2024-12-20'),
            motivo: 'Problemas anteriores persistem - Documentos não foram alterados adequadamente',
            problemas: [
              'ETP ainda menciona medicamentos (problema já identificado no retorno #1)',
              'Especificações técnicas ainda inadequadas (problema já identificado no retorno #1)'
            ],
            scoreAnterior: 72,
            problemasIdentificados: [], // Nenhum problema NOVO identificado
            documentosAlterados: ['etp'], // Apenas ETP foi alterado, mas inadequadamente
            analisesAlteracao: {
              'etp': {
                percentualAlteracao: 25,
                tipoAlteracao: 'ajuste_normal',
                principaisAlteracoes: ['Correções pontuais no texto', 'Pequenos ajustes de formatação'],
                impactoAnalise: 'continua_analise'
              }
            }
          }
        ],
        problemasHistorico: [
          {
            id: 'prob_001',
            descricao: 'ETP menciona medicamentos mas Edital refere-se a ração animal',
            categoria: 'Conflito de Objeto',
            documentoAfetado: 'etp',
            primeiraDeteccao: 1,
            status: 'pendente'
          },
          {
            id: 'prob_002',
            descricao: 'Especificações técnicas inadequadas',
            categoria: 'Especificação Técnica',
            documentoAfetado: 'tr',
            primeiraDeteccao: 1,
            status: 'pendente'
          }
        ],
        statusAtual: 'aguardando_ajustes'
      };
      setHistoricoProcesso(historico);
      setModoReanalise(true);
    } else {
      setHistoricoProcesso(null);
      setModoReanalise(false);
    }
  };

  const handleAnalise = async () => {
    setLoading(true);
    try {
      // Simular análise
      await new Promise(resolve => setTimeout(resolve, 3000));
      setAnaliseRealizada(true);

      // Se é reanalise, adicionar ao histórico
      if (modoReanalise && historicoProcesso) {
        // Verificar quais documentos foram alterados e analisar o grau de alteração
        const documentosAlterados: ('etp' | 'edital' | 'tr')[] = [];
        const analisesAlteracao: {[key: string]: AnaliseAlteracao} = {};
        let temDocumentoNovo = false;
        let motivoDocumentoNovo: string[] = [];

        // Analisar cada documento revisado
        for (const tipo of ['etp', 'edital', 'tr'] as const) {
          if (documentosRevisados[tipo]) {
            documentosAlterados.push(tipo);

            // Buscar versão anterior para comparação
            const versaoAnterior = historicoProcesso.versoes[tipo][historicoProcesso.versoes[tipo].length - 1];
            if (versaoAnterior) {
              const analise = await analisarAlteracaoDocumento(documentosRevisados[tipo]!, versaoAnterior.arquivo);
              analisesAlteracao[tipo] = analise;

              if (analise.tipoAlteracao === 'documento_novo') {
                temDocumentoNovo = true;
                motivoDocumentoNovo.push(`${tipo.toUpperCase()}: ${analise.percentualAlteracao}% alterado - DOCUMENTO NOVO`);
              }
            }
          }
        }

        // Determinar o motivo e status baseado na análise
        let motivo: string;
        let problemas: string[];
        let scoreAtual: number;
        let statusAtual: 'primeira_analise' | 'aguardando_ajustes' | 'reanalise' | 'aprovado' | 'reprovado_final';

        if (temDocumentoNovo) {
          motivo = '🆕 DOCUMENTO(S) NOVO(S) DETECTADO(S) - Nova análise necessária';
          problemas = [
            '🚨 ATENÇÃO: Alterações substanciais detectadas (≥70%)',
            '📋 Requer nova pesquisa de preços',
            '🔄 Processo deve ser reiniciado',
            ...motivoDocumentoNovo
          ];
          scoreAtual = 0; // Reset para nova análise
          statusAtual = 'primeira_analise';
        } else {
          const problemasCorrigidos = documentosAlterados.length > 0;
          motivo = problemasCorrigidos
            ? 'Documentos corrigidos adequadamente - Problemas resolvidos'
            : 'Documentos ainda não foram alterados ou alterações insuficientes';
          problemas = problemasCorrigidos
            ? ['✅ Todos os problemas anteriores foram corrigidos']
            : ['❌ Problemas anteriores persistem - documentos não foram alterados adequadamente'];
          scoreAtual = problemasCorrigidos ? 92 : 78;
          statusAtual = problemasCorrigidos ? 'aprovado' : 'aguardando_ajustes';
        }

        const novoRetorno = {
          numero: historicoProcesso.retornos.length + 1,
          data: new Date(),
          motivo,
          problemas,
          scoreAnterior: 85,
          scoreAtual,
          problemasIdentificados: [], // IMPORTANTE: Não identificamos problemas NOVOS em reanalise
          documentosAlterados,
          analisesAlteracao // Nova propriedade para armazenar análises
        };

        setHistoricoProcesso(prev => prev ? {
          ...prev,
          retornos: [...prev.retornos, novoRetorno],
          statusAtual
        } : null);
      }
    } catch (error) {
      console.error('Erro na análise:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground">Análise de Editais</h1>
        <p className="text-muted-foreground mt-2">Sistema automatizado de checagem de conformidade</p>

        <div className="flex flex-wrap items-center justify-center gap-2 mt-4">
          <Badge variant="outline" className="text-xs">
            <Brain className="mr-1 h-3 w-3" />
            IA + Machine Learning
          </Badge>
          <Badge variant="outline" className="text-xs">
            <Scale className="mr-1 h-3 w-3" />
            Lei 14.133/21
          </Badge>
          <Badge variant="outline" className="text-xs">
            <FileCheck className="mr-1 h-3 w-3" />
            Decreto 9337/2024
          </Badge>
          <Badge variant="outline" className="text-xs">
            <FileCheck className="mr-1 h-3 w-3" />
            Padrão Mauá
          </Badge>
        </div>
      </div>

      {/* Legenda de Status */}
      <Card className="border-gray-200 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-900/50">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Aprovado</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Aguardando Ajustes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Em Análise</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span>Documentos Revisados</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-orange-500"></div>
              <span>Reanalise</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Busca de Processo para Reanalise */}
      <Card className="border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Search className="mr-2 h-5 w-5" />
            Buscar Processo para Reanalise
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-3">
            <Input
              placeholder="Digite o número do processo (ex: CC 045/2024)"
              value={numeroProcesso}
              onChange={(e) => setNumeroProcesso(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={() => buscarHistoricoProcesso(numeroProcesso)}
              disabled={!numeroProcesso.trim()}
            >
              <Search className="mr-2 h-4 w-4" />
              Buscar
            </Button>
          </div>

          {historicoProcesso && (
            <div className="mt-4 p-4 bg-white dark:bg-gray-900 rounded-lg border">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold">📋 {historicoProcesso.numeroProcesso}</h4>
                <Badge variant={
                  historicoProcesso.statusAtual === 'aprovado' ? 'default' :
                  historicoProcesso.statusAtual === 'aguardando_ajustes' ? 'destructive' : 'secondary'
                }>
                  {historicoProcesso.statusAtual.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  <strong>Retornos para ajuste:</strong> {historicoProcesso.retornos.length}
                </p>

                {historicoProcesso.retornos.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Histórico de Retornos:</p>
                    {historicoProcesso.retornos.map((retorno, index) => (
                      <div key={index} className="text-xs p-2 bg-red-50 dark:bg-red-950/30 rounded border border-red-200">
                        <div className="flex justify-between items-start mb-1">
                          <span className="font-medium">Retorno #{retorno.numero}</span>
                          <span className="text-muted-foreground">{retorno.data.toLocaleDateString('pt-BR')}</span>
                        </div>
                        <p className="text-red-700 dark:text-red-300 mb-1">{retorno.motivo}</p>

                        {/* Mostrar problemas com indicação de quando foram detectados */}
                        <div className="space-y-1">
                          {retorno.problemas.map((problema, i) => (
                            <p key={i} className="text-red-600 dark:text-red-400">• {problema}</p>
                          ))}
                        </div>

                        {/* Indicar documentos alterados */}
                        {retorno.documentosAlterados && retorno.documentosAlterados.length > 0 && (
                          <div className="mt-2 p-1 bg-blue-100 dark:bg-blue-900/30 rounded">
                            <p className="text-blue-700 dark:text-blue-300 text-xs">
                              📄 Documentos alterados: {retorno.documentosAlterados.join(', ').toUpperCase()}
                            </p>
                          </div>
                        )}

                        {/* Análises de Alteração */}
                        {retorno.analisesAlteracao && Object.keys(retorno.analisesAlteracao).length > 0 && (
                          <div className="mt-2 space-y-1">
                            {Object.entries(retorno.analisesAlteracao).map(([tipo, analise]) => (
                              <div key={tipo} className={`p-2 rounded text-xs ${
                                analise.tipoAlteracao === 'documento_novo'
                                  ? 'bg-red-100 dark:bg-red-900/30 border border-red-300'
                                  : analise.tipoAlteracao === 'revisao_substancial'
                                  ? 'bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300'
                                  : 'bg-green-100 dark:bg-green-900/30 border border-green-300'
                              }`}>
                                <div className="flex justify-between items-center mb-1">
                                  <span className="font-medium">{tipo.toUpperCase()}</span>
                                  <span className={`font-bold ${
                                    analise.tipoAlteracao === 'documento_novo' ? 'text-red-700' :
                                    analise.tipoAlteracao === 'revisao_substancial' ? 'text-yellow-700' : 'text-green-700'
                                  }`}>
                                    {analise.percentualAlteracao}% alterado
                                  </span>
                                </div>

                                <div className={`text-xs ${
                                  analise.tipoAlteracao === 'documento_novo' ? 'text-red-600' :
                                  analise.tipoAlteracao === 'revisao_substancial' ? 'text-yellow-600' : 'text-green-600'
                                }`}>
                                  {analise.tipoAlteracao === 'documento_novo' && '🆕 DOCUMENTO NOVO'}
                                  {analise.tipoAlteracao === 'revisao_substancial' && '⚠️ REVISÃO SUBSTANCIAL'}
                                  {analise.tipoAlteracao === 'ajuste_normal' && '✅ AJUSTE NORMAL'}
                                </div>

                                {analise.tipoAlteracao === 'documento_novo' && (
                                  <div className="mt-1 p-1 bg-red-200 dark:bg-red-800/50 rounded">
                                    <p className="text-red-800 dark:text-red-200 text-xs font-medium">
                                      🚨 NOVA PESQUISA DE PREÇOS NECESSÁRIA
                                    </p>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Mostrar se foram identificados problemas novos */}
                        {retorno.problemasIdentificados && retorno.problemasIdentificados.length > 0 ? (
                          <div className="mt-2 p-1 bg-orange-100 dark:bg-orange-900/30 rounded">
                            <p className="text-orange-700 dark:text-orange-300 text-xs">
                              🔍 {retorno.problemasIdentificados.length} problema(s) NOVO(S) identificado(s)
                            </p>
                          </div>
                        ) : retorno.numero > 1 && (
                          <div className="mt-2 p-1 bg-green-100 dark:bg-green-900/30 rounded">
                            <p className="text-green-700 dark:text-green-300 text-xs">
                              ✅ Nenhum problema NOVO - Apenas problemas já identificados anteriormente
                            </p>
                          </div>
                        )}

                        <p className="text-xs text-muted-foreground mt-1">
                          Score anterior: {retorno.scoreAnterior}%
                          {retorno.scoreAtual && ` → ${retorno.scoreAtual}%`}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload das Peças Originais - Drag & Drop */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Upload className="mr-2 h-5 w-5" />
            Upload de Documentos Originais
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Área de Drag & Drop */}
          <div
            className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors mb-6 ${
              isDragging
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.odt,.ods,.odp"
              onChange={handleFileSelect}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={loading}
            />

            <div className="space-y-4">
              <div className="flex justify-center">
                <Upload className={`h-12 w-12 ${isDragging ? 'text-blue-500' : 'text-gray-400'}`} />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">
                  {isDragging ? 'Solte os documentos aqui' : 'Arraste seus documentos aqui'}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Ou clique para selecionar arquivos
                </p>

                <div className="flex justify-center gap-4 text-xs text-muted-foreground">
                  <span>📄 ETPs</span>
                  <span>📋 Editais</span>
                  <span>📝 TRs</span>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <p>📄 PDF RECOMENDADO (melhor performance)</p>
                <p>Formatos aceitos: PDF, DOC, DOCX, ODT, ODS, ODP</p>
                <p>Tamanho máximo: 50MB por arquivo</p>
              </div>

              {loading && (
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <Upload className="h-4 w-4 animate-pulse" />
                  <span>Processando arquivos...</span>
                </div>
              )}
            </div>
          </div>

          {/* Documentos Carregados */}
          {(documentosUpload.etp || documentosUpload.edital || documentosUpload.tr) && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {(['etp', 'edital', 'tr'] as const).map((tipo) => (
                <div key={tipo} className="space-y-2">
                  <h4 className="font-medium text-sm">{tipo.toUpperCase()}</h4>
                  {documentosUpload[tipo] ? (
                    <div className="text-xs text-green-600 dark:text-green-400 p-2 bg-green-50 dark:bg-green-950/30 rounded border">
                      ✅ {documentosUpload[tipo]?.name}
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground p-2 bg-gray-50 dark:bg-gray-900/30 rounded border border-dashed">
                      Aguardando documento...
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Alerta de Consistência de Análise */}
      {modoReanalise && historicoProcesso && (
        <Card className="border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/50">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                  🔒 Sistema de Consistência de Análise Ativo
                </h4>
                <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <p>• <strong>Garantia de credibilidade:</strong> Não apontaremos erros novos em documentos não alterados</p>
                  <p>• <strong>Rastreamento:</strong> Todos os problemas são registrados na primeira detecção</p>
                  <p>• <strong>Transparência:</strong> Indicamos claramente quais documentos foram alterados</p>
                  <p>• <strong>Proteção CLMP:</strong> Evita questionamentos sobre "erros que apareceram depois"</p>
                  <p>• <strong>Detecção de Documento Novo:</strong> ≥70% alteração = Nova pesquisa de preços necessária</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerta de Documento Novo Detectado */}
      {modoReanalise && historicoProcesso && historicoProcesso.retornos.some(r =>
        r.analisesAlteracao && Object.values(r.analisesAlteracao).some(a => a.tipoAlteracao === 'documento_novo')
      ) && (
        <Card className="border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-red-800 dark:text-red-200 mb-2">
                  🆕 DOCUMENTO(S) NOVO(S) DETECTADO(S)
                </h4>
                <div className="text-sm text-red-700 dark:text-red-300 space-y-1">
                  <p>• <strong>Alteração substancial:</strong> ≥70% do documento foi modificado</p>
                  <p>• <strong>Nova pesquisa de preços:</strong> Necessária devido às alterações significativas</p>
                  <p>• <strong>Processo reiniciado:</strong> Análise será feita como novo edital</p>
                  <p>• <strong>Histórico preservado:</strong> Todas as versões anteriores mantidas para auditoria</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload de Documentos Revisados - Só aparece se há histórico */}
      {modoReanalise && historicoProcesso && (
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <RefreshCw className="mr-2 h-5 w-5" />
              Upload de Documentos Revisados
              <Badge variant="outline" className="ml-2">
                Retorno #{historicoProcesso.retornos.length + 1}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Observações sobre os ajustes realizados:
                </label>
                <Textarea
                  placeholder="Descreva as correções e ajustes realizados nos documentos..."
                  value={observacoesAjuste}
                  onChange={(e) => setObservacoesAjuste(e.target.value)}
                  className="min-h-[80px]"
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {(['etp', 'edital', 'tr'] as const).map((tipo) => (
                  <Card key={`revisado-${tipo}`} className="border-blue-300">
                    <CardHeader>
                      <CardTitle className="flex items-center text-lg">
                        <Upload className="mr-2 h-5 w-5" />
                        {tipo.toUpperCase()} Revisado
                        {historicoProcesso.versoes[tipo].length > 0 && (
                          <Badge variant="secondary" className="ml-2 text-xs">
                            v{historicoProcesso.versoes[tipo].length + 1}
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <input
                          type="file"
                          id={`upload-revisado-${tipo}`}
                          accept=".pdf,.doc,.docx,.odt,.ods,.odp"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload(tipo, file, true);
                          }}
                        />
                        <Button
                          variant="outline"
                          className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                          disabled={loading}
                          onClick={() => document.getElementById(`upload-revisado-${tipo}`)?.click()}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload Revisado
                        </Button>

                        {/* Recomendação de formato para revisados */}
                        <div className="text-center space-y-1">
                          <p className="text-xs text-green-600 dark:text-green-400 font-medium">
                            📄 PDF RECOMENDADO (melhor performance)
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Aceitos: PDF, DOC, DOCX, ODT, ODS, ODP
                          </p>
                        </div>

                        {documentosRevisados[tipo] && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 p-2 bg-blue-50 dark:bg-blue-950/30 rounded border">
                            ✅ {documentosRevisados[tipo]?.name}
                          </div>
                        )}

                        {/* Mostrar versões anteriores */}
                        {historicoProcesso.versoes[tipo].length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            <p className="font-medium mb-1">Versões anteriores:</p>
                            {historicoProcesso.versoes[tipo].map((versao, index) => (
                              <div key={index} className="flex justify-between items-center p-1">
                                <span>v{versao.versao} - {versao.dataUpload.toLocaleDateString('pt-BR')}</span>
                                {versao.scoreAnterior && (
                                  <span className="text-red-600">Score: {versao.scoreAnterior}%</span>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Botão de Análise */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
        <CardContent className="p-6 text-center space-y-4">
          <div className="flex items-center justify-center space-x-2 text-blue-700 dark:text-blue-300">
            <Brain className="h-5 w-5" />
            <Scale className="h-5 w-5" />
            <Zap className="h-5 w-5" />
          </div>
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
            {modoReanalise ? 'Reanalise Inteligente' : 'Análise Inteligente Completa'}
          </h3>
          <Button
            size="lg"
            className="w-full max-w-md"
            disabled={loading}
            onClick={handleAnalise}
          >
            {loading ? (
              <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
            ) : (
              <Brain className="mr-2 h-5 w-5" />
            )}
            {loading
              ? (modoReanalise ? 'Reanalisando...' : 'Analisando...')
              : (modoReanalise ? 'Realizar Reanalise' : 'Realizar Análise Inteligente')
            }
          </Button>
        </CardContent>
      </Card>

      {/* Evolução de Scores - Só aparece em reanalise */}
      {modoReanalise && historicoProcesso && historicoProcesso.retornos.length > 0 && (
        <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50 mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="mr-2 h-5 w-5" />
              Evolução dos Scores por Retorno
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {historicoProcesso.retornos.map((retorno, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded border">
                  <div>
                    <span className="font-medium">Retorno #{retorno.numero}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      {retorno.data.toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Score</div>
                      <div className={`font-bold ${
                        retorno.scoreAnterior >= 80 ? 'text-green-600' :
                        retorno.scoreAnterior >= 60 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {retorno.scoreAnterior}%
                        {retorno.scoreAtual && (
                          <span className="text-green-600 ml-2">→ {retorno.scoreAtual}%</span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Problemas</div>
                      <div className="font-bold text-red-600">{retorno.problemas.length}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}







      {/* Modal de Distribuição de Pesquisa */}
      {mostrarDistribuicao && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <DistribuirPesquisa
              processoId={numeroProcesso || 'CC 045/2024'}
              processoObjeto="Aquisição de materiais de limpeza e higiene"
              onDistribuir={(pesquisadorId) => {
                alert(`✅ Pesquisa distribuída para: ${pesquisadorId}`);
                setMostrarDistribuicao(false);
              }}
              onCancelar={() => setMostrarDistribuicao(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
