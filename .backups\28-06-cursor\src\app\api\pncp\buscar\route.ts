import { NextRequest, NextResponse } from 'next/server';

// 🌐 CONFIGURAÇÃO DA API PNCP
const PNCP_BASE_URL = 'https://pncp.gov.br/api/consulta/v1';
const TIMEOUT = 10000; // 10 segundos

interface PNCPSearchParams {
  termo: string;
  unidade?: string;
  valorMinimo?: number;
  valorMaximo?: number;
  orgaoTipo?: string;
}

interface PNCPItem {
  id: string;
  descricao: string;
  unidade: string;
  preco: number;
  fornecedor: string;
  cnpj: string;
  dataContrato: string;
  orgao: string;
  numeroContrato: string;
  especificacao: string;
  relevancia: number;
}

export async function POST(request: NextRequest) {
  try {
    const params: PNCPSearchParams = await request.json();
    console.log('🔍 Busca PNCP no backend:', params);

    // Construir URL da API PNCP
    const url = new URL(`${PNCP_BASE_URL}/contratos`);
    
    // Adicionar parâmetros de busca
    if (params.termo) {
      url.searchParams.append('objeto', params.termo);
    }
    
    console.log('🌐 URL PNCP:', url.toString());

    // Fazer requisição com timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TIMEOUT);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'InovaProcess/1.0'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`API PNCP retornou: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Dados recebidos da API PNCP:', {
        total: data.data?.length || 0,
        estrutura: Object.keys(data)
      });

      // Transformar dados para nosso formato
      const itens = transformarDadosPNCP(data.data || data.contratos || []);

      return NextResponse.json({
        sucesso: true,
        itens,
        total: itens.length,
        fonte: 'PNCP API Real'
      });

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if ((fetchError as any).name === 'AbortError') {
        console.error('⏰ Timeout na API PNCP');
        throw new Error('Timeout na consulta PNCP');
      }
      
      throw fetchError;
    }

  } catch (error) {
    console.error('❌ Erro na API PNCP:', error);

    return NextResponse.json({
      sucesso: false,
      erro: error instanceof Error ? error.message : 'Erro desconhecido',
      itens: [],
      total: 0,
      fonte: 'Erro'
    }, { status: 500 });
  }
}

/**
 * Transformar dados da API PNCP para nosso formato
 */
function transformarDadosPNCP(dadosPNCP: any[]): PNCPItem[] {
  if (!Array.isArray(dadosPNCP)) {
    console.warn('⚠️ Dados PNCP não são array:', dadosPNCP);
    return [];
  }

  return dadosPNCP.map((item, index) => {
    const pncpItem: PNCPItem = {
      id: item.numeroControlePNCP || item.id || `pncp_${index}`,
      descricao: item.objetoContrato || item.objeto || item.descricao || '',
      unidade: item.unidadeMedida || item.unidade || 'UN',
      preco: extrairValor(item.valorTotalEstimado || item.valor || item.preco || 0),
      fornecedor: item.razaoSocialFornecedor || item.fornecedor || item.empresa || '',
      cnpj: item.cnpjFornecedor || item.cnpj || '',
      dataContrato: formatarData(item.dataAssinatura || item.dataContrato || item.data),
      orgao: item.nomeOrgaoEntidade || item.orgao || item.entidade || '',
      numeroContrato: item.numeroContrato || item.numero || '',
      especificacao: item.especificacaoTecnica || item.especificacao || item.objetoContrato || '',
      relevancia: 0
    };

    return pncpItem;
  }).filter(item => item.descricao && item.preco > 0);
}

/**
 * Extrair valor numérico
 */
function extrairValor(valor: any): number {
  if (typeof valor === 'number') return valor;
  if (typeof valor === 'string') {
    const numero = parseFloat(valor.replace(/[^\d.,]/g, '').replace(',', '.'));
    return isNaN(numero) ? 0 : numero;
  }
  return 0;
}

/**
 * Formatar data
 */
function formatarData(data: any): string {
  if (!data) return new Date().toISOString().split('T')[0];

  try {
    const dataObj = new Date(data);
    return dataObj.toISOString().split('T')[0];
  } catch {
    return new Date().toISOString().split('T')[0];
  }
}


