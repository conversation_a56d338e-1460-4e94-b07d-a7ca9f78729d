/**
 * 📊 COMPONENTE DE STATUS DO FIRESTORE
 * Mostra informações sobre o estado da inicialização do Firestore
 */

'use client';

import React from 'react';
import { useFirestoreStatus } from '@/hooks/useFirestore';

interface FirestoreStatusProps {
  showDetails?: boolean;
  className?: string;
}

export const FirestoreStatus: React.FC<FirestoreStatusProps> = ({ 
  showDetails = false, 
  className = '' 
}) => {
  const { isAvailable, isInitialized, statusInfo, refresh } = useFirestoreStatus();

  if (!showDetails && isAvailable && isInitialized) {
    // Se tudo está OK e não quer mostrar detalhes, não renderiza nada
    return null;
  }

  const getStatusColor = () => {
    if (!isAvailable) return 'text-red-500 bg-red-50 border-red-200';
    if (!isInitialized) return 'text-yellow-500 bg-yellow-50 border-yellow-200';
    return 'text-green-500 bg-green-50 border-green-200';
  };

  const getStatusIcon = () => {
    if (!isAvailable) return '❌';
    if (!isInitialized) return '⚠️';
    return '✅';
  };

  const getStatusText = () => {
    if (!isAvailable) return 'Firestore Indisponível';
    if (!isInitialized) return 'Firestore Não Inicializado';
    return 'Firestore Ativo';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleString('pt-BR');
    } catch {
      return 'Data inválida';
    }
  };

  return (
    <div className={`border rounded-lg p-4 ${getStatusColor()} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getStatusIcon()}</span>
          <span className="font-medium">{getStatusText()}</span>
        </div>
        
        {showDetails && (
          <button
            onClick={refresh}
            className="text-sm px-2 py-1 rounded border border-current opacity-70 hover:opacity-100 transition-opacity"
            title="Atualizar status"
          >
            🔄 Atualizar
          </button>
        )}
      </div>

      {showDetails && (
        <div className="mt-3 space-y-1 text-sm opacity-80">
          <div>
            <strong>Disponível:</strong> {isAvailable ? 'Sim' : 'Não'}
          </div>
          <div>
            <strong>Inicializado:</strong> {isInitialized ? 'Sim' : 'Não'}
          </div>
          {statusInfo.version && (
            <div>
              <strong>Versão:</strong> {statusInfo.version}
            </div>
          )}
          {statusInfo.initializedBy && (
            <div>
              <strong>Inicializado por:</strong> {statusInfo.initializedBy}
            </div>
          )}
          {statusInfo.initDate && (
            <div>
              <strong>Data de inicialização:</strong> {formatDate(statusInfo.initDate)}
            </div>
          )}
        </div>
      )}

      {!isAvailable && (
        <div className="mt-2 text-sm">
          <p>O Firestore não está configurado. Verifique as variáveis de ambiente.</p>
        </div>
      )}

      {isAvailable && !isInitialized && (
        <div className="mt-2 text-sm">
          <p>O Firestore será inicializado automaticamente no próximo login.</p>
        </div>
      )}
    </div>
  );
};

// Componente compacto para header/navbar
export const FirestoreStatusIndicator: React.FC = () => {
  const { isAvailable, isInitialized } = useFirestoreStatus();

  if (isAvailable && isInitialized) {
    return (
      <div className="flex items-center space-x-1 text-green-600" title="Firestore ativo">
        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
        <span className="text-xs font-medium">DB</span>
      </div>
    );
  }

  if (isAvailable && !isInitialized) {
    return (
      <div className="flex items-center space-x-1 text-yellow-600" title="Firestore não inicializado">
        <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
        <span className="text-xs font-medium">DB</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-1 text-red-600" title="Firestore indisponível">
      <span className="w-2 h-2 bg-red-500 rounded-full"></span>
      <span className="text-xs font-medium">DB</span>
    </div>
  );
};

export default FirestoreStatus;
