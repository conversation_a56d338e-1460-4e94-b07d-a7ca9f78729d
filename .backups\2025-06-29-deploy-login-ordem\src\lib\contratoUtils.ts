import { Contrato, ContratoStats } from '@/types/contrato';

/**
 * Utilitários para gestão de contratos
 */

/**
 * Calcula dias restantes até o vencimento
 */
export function calcularDiasRestantes(dataVencimento: string): number {
  try {
    const hoje = new Date();
    const vencimento = new Date(dataVencimento);
    
    if (isNaN(vencimento.getTime())) return 0;
    
    const diffTime = vencimento.getTime() - hoje.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  } catch {
    return 0;
  }
}

/**
 * Determina o status do contrato baseado nas datas
 */
export function determinarStatusContrato(dataVencimento: string, statusAtual?: string): Contrato['status'] {
  if (statusAtual === 'SUSPENSO' || statusAtual === 'ENCERRADO') {
    return statusAtual;
  }
  
  const diasRestantes = calcularDiasRestantes(dataVencimento);
  
  if (diasRestantes < 0) {
    return 'VENCIDO';
  }
  
  return 'VIGENTE';
}

/**
 * Verifica se o contrato está próximo do vencimento
 */
export function contratoProximoVencimento(contrato: Contrato, diasLimite: number = 30): boolean {
  return contrato.status === 'VIGENTE' && contrato.diasRestantes <= diasLimite && contrato.diasRestantes > 0;
}

/**
 * Calcula o percentual de execução do contrato
 */
export function calcularPercentualExecucao(valorContrato: number, valorExecutado: number = 0): number {
  if (valorContrato <= 0) return 0;
  return Math.min((valorExecutado / valorContrato) * 100, 100);
}

/**
 * Formata valor monetário para exibição
 */
export function formatarValorContrato(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2
  }).format(valor);
}

/**
 * Formata data para padrão brasileiro
 */
export function formatarDataContrato(data: string): string {
  try {
    const dataObj = new Date(data);
    if (isNaN(dataObj.getTime())) return data;
    
    return dataObj.toLocaleDateString('pt-BR');
  } catch {
    return data;
  }
}

/**
 * Calcula estatísticas dos contratos
 */
export function calcularEstatisticasContratos(contratos: Contrato[]): ContratoStats {
  const total = contratos.length;
  const vigentes = contratos.filter(c => c.status === 'VIGENTE').length;
  const vencidos = contratos.filter(c => c.status === 'VENCIDO').length;
  const suspensos = contratos.filter(c => c.status === 'SUSPENSO').length;
  const encerrados = contratos.filter(c => c.status === 'ENCERRADO').length;
  const vencendoEm30Dias = contratos.filter(c => contratoProximoVencimento(c, 30)).length;
  
  const valorTotal = contratos.reduce((sum, c) => sum + c.valor, 0);
  const valorVigentes = contratos
    .filter(c => c.status === 'VIGENTE')
    .reduce((sum, c) => sum + c.valor, 0);
  
  const valorExecutado = contratos.reduce((sum, c) => sum + (c.valorExecutado || 0), 0);
  
  const percentualMedioExecucao = contratos.length > 0
    ? contratos.reduce((sum, c) => sum + calcularPercentualExecucao(c.valor, c.valorExecutado), 0) / contratos.length
    : 0;

  return {
    total,
    vigentes,
    vencidos,
    suspensos,
    encerrados,
    vencendoEm30Dias,
    valorTotal,
    valorVigentes,
    valorExecutado,
    percentualMedioExecucao
  };
}

/**
 * Filtra contratos baseado nos critérios
 */
export function filtrarContratos(contratos: Contrato[], filtros: any): Contrato[] {
  return contratos.filter(contrato => {
    // Filtro por texto
    if (filtros.search) {
      const searchLower = filtros.search.toLowerCase();
      const matchesSearch = 
        contrato.numero.toLowerCase().includes(searchLower) ||
        contrato.objeto.toLowerCase().includes(searchLower) ||
        contrato.contratada.toLowerCase().includes(searchLower) ||
        contrato.processoOrigem.toLowerCase().includes(searchLower);
      
      if (!matchesSearch) return false;
    }

    // Filtro por status
    if (filtros.status && contrato.status !== filtros.status) {
      return false;
    }

    // Filtro por secretaria
    if (filtros.secretaria && !contrato.secretaria.toLowerCase().includes(filtros.secretaria.toLowerCase())) {
      return false;
    }

    // Filtro por modalidade
    if (filtros.modalidade && !contrato.modalidade.toLowerCase().includes(filtros.modalidade.toLowerCase())) {
      return false;
    }

    // Filtro por valor
    if (filtros.valorMin && contrato.valor < filtros.valorMin) {
      return false;
    }

    if (filtros.valorMax && contrato.valor > filtros.valorMax) {
      return false;
    }

    return true;
  });
}

/**
 * Ordena contratos por critério
 */
export function ordenarContratos(contratos: Contrato[], criterio: string, ordem: 'asc' | 'desc' = 'asc'): Contrato[] {
  return [...contratos].sort((a, b) => {
    let valorA: any;
    let valorB: any;

    switch (criterio) {
      case 'numero':
        valorA = a.numero;
        valorB = b.numero;
        break;
      case 'valor':
        valorA = a.valor;
        valorB = b.valor;
        break;
      case 'dataVencimento':
        valorA = new Date(a.dataVencimento);
        valorB = new Date(b.dataVencimento);
        break;
      case 'diasRestantes':
        valorA = a.diasRestantes;
        valorB = b.diasRestantes;
        break;
      case 'contratada':
        valorA = a.contratada;
        valorB = b.contratada;
        break;
      default:
        valorA = a.numero;
        valorB = b.numero;
    }

    if (valorA < valorB) return ordem === 'asc' ? -1 : 1;
    if (valorA > valorB) return ordem === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Gera relatório de contratos
 */
export function gerarRelatorioContratos(contratos: Contrato[]) {
  const stats = calcularEstatisticasContratos(contratos);
  const proximosVencimento = contratos.filter(c => contratoProximoVencimento(c, 30));
  const vencidos = contratos.filter(c => c.status === 'VENCIDO');
  
  return {
    resumo: stats,
    alertas: {
      proximosVencimento,
      vencidos,
      totalAlertas: proximosVencimento.length + vencidos.length
    },
    distribuicaoPorSecretaria: contratos.reduce((acc, contrato) => {
      acc[contrato.secretaria] = (acc[contrato.secretaria] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    distribuicaoPorModalidade: contratos.reduce((acc, contrato) => {
      acc[contrato.modalidade] = (acc[contrato.modalidade] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };
}
