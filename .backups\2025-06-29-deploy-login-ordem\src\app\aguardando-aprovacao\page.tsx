'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from 'next-themes';
import {
  Clock,
  Building,
  Shield,
  LogOut,
  Sun,
  Moon,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

export default function AguardandoAprovacaoPage() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { user, logout, loading } = useAuth();

  useEffect(() => {
    // Se não há usuário logado, redirecionar para login
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-900 dark:to-black flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-slate-600 dark:text-slate-300">
            Carregando...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Será redirecionado pelo useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-black flex items-center justify-center p-4 relative">
      {/* Botão Dark Mode */}
      <div className="absolute top-4 right-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          className="bg-white/80 border-slate-200 hover:bg-white text-slate-700 dark:bg-slate-800/80 dark:border-slate-600 dark:hover:bg-slate-700 dark:text-white backdrop-blur-sm"
        >
          {theme === 'dark' ? (
            <Sun className="h-4 w-4" />
          ) : (
            <Moon className="h-4 w-4" />
          )}
        </Button>
      </div>

      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-3 bg-white rounded-xl shadow-lg dark:bg-slate-800">
              <Building className="h-10 w-10 text-slate-700 dark:text-slate-300" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-slate-800 dark:text-white">InovaProcess</h1>
              <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
                Sistema de Gestão de Processos
              </p>
            </div>
          </div>
        </div>

        {/* Card de Aguardando Aprovação */}
        <Card className="shadow-xl bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
          <CardHeader className="space-y-1 bg-yellow-600 dark:bg-yellow-700 text-white rounded-t-lg">
            <CardTitle className="text-xl text-center flex items-center justify-center">
              <Clock className="mr-2 h-5 w-5" />
              Aguardando Aprovação
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Informações do usuário */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-gray-900 dark:text-white">
                    <p className="font-medium mb-1">
                      👋 Olá, {user.displayName || user.email?.split('@')[0] || 'Usuário'}!
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      Seu cadastro foi recebido e está sendo analisado pelo administrador do sistema.
                    </p>
                  </div>
                </div>
              </div>

              {/* Status */}
              <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800">
                <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <AlertDescription className="text-yellow-800 dark:text-yellow-200">
                  <strong>Status:</strong> Pendente de aprovação
                </AlertDescription>
              </Alert>

              {/* Informações importantes */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  📋 O que acontece agora?
                </h3>
                <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                  <li>• Seu cadastro será analisado pelo administrador</li>
                  <li>• Você receberá uma notificação quando for aprovado</li>
                  <li>• Após aprovação, você poderá acessar o sistema normalmente</li>
                  <li>• O processo pode levar algumas horas</li>
                </ul>
              </div>

              {/* Botão de logout */}
              <Button
                onClick={handleLogout}
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                <LogOut className="mr-2 h-4 w-4" />
                Sair do Sistema
              </Button>

              {/* Dicas */}
              <div className="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                <p className="font-medium">💡 Dica:</p>
                <p>
                  Você pode tentar fazer login novamente mais tarde para verificar se foi aprovado.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <div className="bg-white/80 dark:bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200 dark:border-white/20">
            <p className="text-slate-700 dark:text-white font-medium">InovaProcess © 2025</p>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Sistema de Gestão de Processos Administrativos
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 