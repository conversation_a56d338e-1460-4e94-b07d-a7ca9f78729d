import { Usuario, PeriodoAfastamento, SugestaoRedistribuicao } from '@/types/usuario';
import { Processo } from '@/types/processo';

/**
 * Utilitários para análise e redistribuição de processos durante afastamentos
 */

export interface AnaliseRedistribuicao {
  usuarioAfastado: Usuario;
  periodo: PeriodoAfastamento;
  processosAfetados: Processo[];
  sugestoes: SugestaoRedistribuicao[];
  alertas: AlertaRedistribuicao[];
  estatisticas: EstatisticasRedistribuicao;
}

export interface AlertaRedistribuicao {
  tipo: 'sobrecarga' | 'competencia' | 'prazo' | 'prioridade';
  mensagem: string;
  severidade: 'baixa' | 'media' | 'alta' | 'critica';
  usuarioAfetado?: string;
}

export interface EstatisticasRedistribuicao {
  totalProcessos: number;
  processosPorPrioridade: {
    alta: number;
    media: number;
    baixa: number;
  };
  processosPorStatus: Record<string, number>;
  cargaTrabalhoEquipe: Record<string, number>;
  diasAfastamento: number;
}

/**
 * Analisa processos de um usuário e sugere redistribuição
 */
export function analisarRedistribuicao(
  usuarioAfastado: Usuario,
  periodo: PeriodoAfastamento,
  todosProcessos: Processo[],
  todosUsuarios: Usuario[]
): AnaliseRedistribuicao {
  
  // Filtrar processos do usuário afastado
  const processosAfetados = todosProcessos.filter(processo => 
    processo.RESPONSÁVEL === usuarioAfastado.nome && 
    !isProcessoFinalizado(processo)
  );

  // Calcular carga de trabalho atual da equipe
  const cargaTrabalhoEquipe = calcularCargaTrabalhoEquipe(todosProcessos, todosUsuarios);

  // Gerar sugestões de redistribuição
  const sugestoes = gerarSugestoesRedistribuicao(
    processosAfetados,
    todosUsuarios.filter(u => u.id !== usuarioAfastado.id && u.status === 'ativo'),
    cargaTrabalhoEquipe
  );

  // Gerar alertas
  const alertas = gerarAlertasRedistribuicao(sugestoes, cargaTrabalhoEquipe, periodo);

  // Calcular estatísticas
  const estatisticas = calcularEstatisticasRedistribuicao(
    processosAfetados,
    cargaTrabalhoEquipe,
    periodo
  );

  return {
    usuarioAfastado,
    periodo,
    processosAfetados,
    sugestoes,
    alertas,
    estatisticas
  };
}

/**
 * Verifica se processo está finalizado
 */
function isProcessoFinalizado(processo: Processo): boolean {
  const statusFinalizados = [
    'FINALIZADO',
    'CONCLUÍDO',
    'ARQUIVADO',
    'CANCELADO',
    'HOMOLOGADO',
    'CONTRATADO'
  ];
  
  return statusFinalizados.some(status => 
    processo.STATUS?.toUpperCase().includes(status)
  );
}

/**
 * Calcula carga de trabalho atual de cada usuário
 */
function calcularCargaTrabalhoEquipe(
  processos: Processo[],
  usuarios: Usuario[]
): Record<string, number> {
  const cargaTrabalho: Record<string, number> = {};
  
  // Inicializar com zero para todos os usuários ativos
  usuarios
    .filter(u => u.status === 'ativo')
    .forEach(usuario => {
      cargaTrabalho[usuario.nome] = 0;
    });

  // Contar processos por responsável
  processos
    .filter(p => !isProcessoFinalizado(p))
    .forEach(processo => {
      const responsavel = processo.RESPONSÁVEL;
      if (responsavel && cargaTrabalho.hasOwnProperty(responsavel)) {
        cargaTrabalho[responsavel]++;
      }
    });

  return cargaTrabalho;
}

/**
 * Gera sugestões inteligentes de redistribuição
 */
function gerarSugestoesRedistribuicao(
  processos: Processo[],
  usuariosDisponiveis: Usuario[],
  cargaTrabalhoAtual: Record<string, number>
): SugestaoRedistribuicao[] {
  const sugestoes: SugestaoRedistribuicao[] = [];

  processos.forEach(processo => {
    const melhorUsuario = encontrarMelhorUsuario(
      processo,
      usuariosDisponiveis,
      cargaTrabalhoAtual
    );

    if (melhorUsuario) {
      sugestoes.push({
        processoId: processo.PROCESSO || '',
        processoNumero: processo.PROCESSO || '',
        usuarioAtual: processo.RESPONSÁVEL || '',
        usuarioSugerido: melhorUsuario.nome,
        motivo: gerarMotivoRedistribuicao(processo, melhorUsuario, cargaTrabalhoAtual),
        prioridade: determinarPrioridadeRedistribuicao(processo),
        cargaTrabalho: cargaTrabalhoAtual[melhorUsuario.nome] || 0
      });
    }
  });

  // Ordenar por prioridade
  return sugestoes.sort((a, b) => {
    const prioridadeOrder = { 'alta': 3, 'media': 2, 'baixa': 1 };
    return prioridadeOrder[b.prioridade] - prioridadeOrder[a.prioridade];
  });
}

/**
 * Encontra o melhor usuário para receber um processo
 */
function encontrarMelhorUsuario(
  processo: Processo,
  usuariosDisponiveis: Usuario[],
  cargaTrabalhoAtual: Record<string, number>
): Usuario | null {
  
  // Filtrar usuários elegíveis
  const usuariosElegiveis = usuariosDisponiveis.filter(usuario => {
    // Verificar se não está em afastamento no período
    const estaAfastado = verificarAfastamentoAtivo(usuario);
    if (estaAfastado) return false;

    // Verificar competência (mesma secretaria ou CLMP)
    const temCompetencia = usuario.secretaria === 'CLMP' || 
                          usuario.secretaria === processo.REQUISITANTE;
    
    return temCompetencia;
  });

  if (usuariosElegiveis.length === 0) {
    // Se nenhum usuário elegível, pegar qualquer um da CLMP
    return usuariosDisponiveis
      .filter(u => u.secretaria === 'CLMP')
      .sort((a, b) => (cargaTrabalhoAtual[a.nome] || 0) - (cargaTrabalhoAtual[b.nome] || 0))[0] || null;
  }

  // Ordenar por menor carga de trabalho
  return usuariosElegiveis.sort((a, b) => 
    (cargaTrabalhoAtual[a.nome] || 0) - (cargaTrabalhoAtual[b.nome] || 0)
  )[0];
}

/**
 * Verifica se usuário está em afastamento ativo
 */
function verificarAfastamentoAtivo(usuario: Usuario): boolean {
  if (!usuario.periodos) return false;
  
  const hoje = new Date();
  return usuario.periodos.some(periodo => {
    if (periodo.status !== 'ativo' && periodo.status !== 'agendado') return false;
    
    const inicio = new Date(periodo.dataInicio);
    const fim = new Date(periodo.dataFim);
    
    return hoje >= inicio && hoje <= fim;
  });
}

/**
 * Gera motivo da redistribuição
 */
function gerarMotivoRedistribuicao(
  processo: Processo,
  usuarioSugerido: Usuario,
  cargaTrabalho: Record<string, number>
): string {
  const carga = cargaTrabalho[usuarioSugerido.nome] || 0;
  
  if (usuarioSugerido.secretaria === processo.REQUISITANTE) {
    return `Competência específica (${processo.REQUISITANTE}) - Carga atual: ${carga} processos`;
  }
  
  if (carga === 0) {
    return `Usuário disponível sem processos ativos`;
  }
  
  return `Menor carga de trabalho (${carga} processos)`;
}

/**
 * Determina prioridade da redistribuição
 */
function determinarPrioridadeRedistribuicao(processo: Processo): 'alta' | 'media' | 'baixa' {
  const status = processo.STATUS?.toUpperCase() || '';
  const objeto = processo.OBJETO?.toUpperCase() || '';
  
  // Alta prioridade
  if (status.includes('URGENTE') || 
      status.includes('EMERGÊNCIA') || 
      objeto.includes('EMERGÊNCIA') ||
      objeto.includes('URGENTE')) {
    return 'alta';
  }
  
  // Média prioridade
  if (status.includes('PUBLICAÇÃO') || 
      status.includes('ABERTURA') ||
      status.includes('ANÁLISE')) {
    return 'media';
  }
  
  return 'baixa';
}

/**
 * Gera alertas sobre a redistribuição
 */
function gerarAlertasRedistribuicao(
  sugestoes: SugestaoRedistribuicao[],
  cargaTrabalho: Record<string, number>,
  periodo: PeriodoAfastamento
): AlertaRedistribuicao[] {
  const alertas: AlertaRedistribuicao[] = [];
  
  // Verificar sobrecarga
  sugestoes.forEach(sugestao => {
    const cargaFinal = sugestao.cargaTrabalho + 1;
    
    if (cargaFinal > 10) {
      alertas.push({
        tipo: 'sobrecarga',
        mensagem: `${sugestao.usuarioSugerido} ficará com ${cargaFinal} processos (sobrecarga crítica)`,
        severidade: 'critica',
        usuarioAfetado: sugestao.usuarioSugerido
      });
    } else if (cargaFinal > 7) {
      alertas.push({
        tipo: 'sobrecarga',
        mensagem: `${sugestao.usuarioSugerido} ficará com ${cargaFinal} processos (carga alta)`,
        severidade: 'alta',
        usuarioAfetado: sugestao.usuarioSugerido
      });
    }
  });
  
  // Verificar processos de alta prioridade
  const processosAltaPrioridade = sugestoes.filter(s => s.prioridade === 'alta');
  if (processosAltaPrioridade.length > 0) {
    alertas.push({
      tipo: 'prioridade',
      mensagem: `${processosAltaPrioridade.length} processos de alta prioridade precisam ser redistribuídos`,
      severidade: 'alta'
    });
  }
  
  return alertas;
}

/**
 * Calcula estatísticas da redistribuição
 */
function calcularEstatisticasRedistribuicao(
  processos: Processo[],
  cargaTrabalho: Record<string, number>,
  periodo: PeriodoAfastamento
): EstatisticasRedistribuicao {
  const inicio = new Date(periodo.dataInicio);
  const fim = new Date(periodo.dataFim);
  const diasAfastamento = Math.ceil((fim.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24));
  
  const processosPorPrioridade = {
    alta: 0,
    media: 0,
    baixa: 0
  };
  
  const processosPorStatus: Record<string, number> = {};
  
  processos.forEach(processo => {
    // Contar por prioridade
    const prioridade = determinarPrioridadeRedistribuicao(processo);
    processosPorPrioridade[prioridade]++;
    
    // Contar por status
    const status = processo.STATUS || 'Não informado';
    processosPorStatus[status] = (processosPorStatus[status] || 0) + 1;
  });
  
  return {
    totalProcessos: processos.length,
    processosPorPrioridade,
    processosPorStatus,
    cargaTrabalhoEquipe: { ...cargaTrabalho },
    diasAfastamento
  };
}

/**
 * Calcula dias úteis entre duas datas
 */
export function calcularDiasUteis(dataInicio: string, dataFim: string): number {
  const inicio = new Date(dataInicio);
  const fim = new Date(dataFim);
  let diasUteis = 0;
  
  const atual = new Date(inicio);
  while (atual <= fim) {
    const diaSemana = atual.getDay();
    if (diaSemana >= 1 && diaSemana <= 5) { // Segunda a sexta
      diasUteis++;
    }
    atual.setDate(atual.getDate() + 1);
  }
  
  return diasUteis;
}
