const fs = require('fs');
const path = require('path');

// Diretório para armazenar o cache
const CACHE_DIR = path.join(process.cwd(), '.cache');

// Garantir que o diretório de cache exista
if (!fs.existsSync(CACHE_DIR)) {
  fs.mkdirSync(CACHE_DIR, { recursive: true });
}

module.exports = class CacheHandler {
  constructor(options) {
    this.options = options;
    console.log('Cache handler inicializado com proteção contra resets');
  }

  async get(key) {
    try {
      const cacheFile = path.join(CACHE_DIR, `${key}.json`);
      if (fs.existsSync(cacheFile)) {
        const data = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
        return data.value;
      }
    } catch (error) {
      console.error('Erro ao ler cache:', error);
    }
    return null;
  }

  async set(key, data, ctx) {
    try {
      const cacheFile = path.join(CACHE_DIR, `${key}.json`);
      fs.writeFileSync(
        cacheFile,
        JSON.stringify({
          value: data,
          lastModified: Date.now(),
          tags: ctx.tags || [],
        })
      );
    } catch (error) {
      console.error('Erro ao escrever cache:', error);
    }
  }

  async revalidateTag(tags) {
    tags = [tags].flat();
    
    try {
      const files = fs.readdirSync(CACHE_DIR);
      for (const file of files) {
        const cacheFile = path.join(CACHE_DIR, file);
        try {
          const data = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
          if (data.tags && data.tags.some(tag => tags.includes(tag))) {
            fs.unlinkSync(cacheFile);
          }
        } catch (e) {
          // Ignora arquivos que não podem ser analisados
        }
      }
    } catch (error) {
      console.error('Erro ao revalidar tags:', error);
    }
  }

  resetRequestCache() {
    // Implementação opcional para cache temporário
  }
};