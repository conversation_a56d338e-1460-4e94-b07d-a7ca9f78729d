// Analisador de conformidade com a Lei 14.133/21

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface ViolacaoLei {
  artigo: string;
  descricao: string;
  gravidade: 'BAIXA' | 'MEDIA' | 'ALTA' | 'CRITICA';
  evidencia: string;
  sugestaoCorrecao: string;
}

interface AnaliseConformidade {
  conformeGeral: boolean;
  scoreConformidade: number;
  violacoes: ViolacaoLei[];
  violacoesGraves: ViolacaoLei[];
  recomendacoes: any[];
  artigosVerificados: string[];
}

export async function analisarConformidadeLei14133(documentos: DocumentoAnalise[]): Promise<AnaliseConformidade> {
  const violacoes: ViolacaoLei[] = [];
  const artigosVerificados: string[] = [];

  // Verificações por tipo de documento
  for (const doc of documentos) {
    switch (doc.tipo) {
      case 'etp':
        violacoes.push(...verificarETP(doc));
        break;
      case 'edital':
        violacoes.push(...verificarEdital(doc));
        break;
      case 'tr':
        violacoes.push(...verificarTR(doc));
        break;
    }
  }

  // Verificações gerais do processo
  violacoes.push(...verificarCoerenciaGeral(documentos));

  const violacoesGraves = violacoes.filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA');
  const scoreConformidade = calcularScoreConformidade(violacoes);
  const conformeGeral = violacoesGraves.length === 0 && scoreConformidade >= 80;

  return {
    conformeGeral,
    scoreConformidade,
    violacoes,
    violacoesGraves,
    recomendacoes: gerarRecomendacoesLegais(violacoes),
    artigosVerificados: [
      'Art. 18 - ETP',
      'Art. 40 - Edital',
      'Art. 23 - Modalidades',
      'Art. 26 - Julgamento',
      'Art. 59 - Habilitação'
    ]
  };
}

function verificarETP(doc: DocumentoAnalise): ViolacaoLei[] {
  const violacoes: ViolacaoLei[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // 1. CONFORMIDADE LEGAL E PRINCÍPIOS DA LEI 14.133/21

  // Art. 6º, XX - Fundamentação do interesse público
  if (!conteudo.includes('interesse público') && !conteudo.includes('interesse publico')) {
    violacoes.push({
      artigo: 'Art. 6º, XX',
      descricao: 'Ausência de fundamentação do interesse público',
      gravidade: 'CRITICA',
      evidencia: 'Interesse público não caracterizado',
      sugestaoCorrecao: 'Incluir fundamentação clara do interesse público conforme Art. 6º, XX'
    });
  }

  // Art. 5º - Princípios da licitação
  const principios = [
    'legalidade', 'impessoalidade', 'moralidade', 'publicidade', 'eficiência',
    'probidade administrativa', 'igualdade', 'planejamento', 'transparência',
    'motivação', 'vinculação', 'segurança jurídica'
  ];

  const principiosEncontrados = principios.filter(p => conteudo.includes(p.replace(' ', '')));
  if (principiosEncontrados.length < 3) {
    violacoes.push({
      artigo: 'Art. 5º',
      descricao: 'Observância insuficiente dos princípios da licitação',
      gravidade: 'ALTA',
      evidencia: `Apenas ${principiosEncontrados.length} princípios identificados`,
      sugestaoCorrecao: 'Demonstrar observância aos princípios do Art. 5º da Lei 14.133/21'
    });
  }

  // Art. 18, I - Justificativa da necessidade
  if (!conteudo.includes('justificativa')) {
    violacoes.push({
      artigo: 'Art. 18, I',
      descricao: 'Ausência de justificativa da necessidade da contratação',
      gravidade: 'CRITICA',
      evidencia: 'Justificativa não encontrada no documento',
      sugestaoCorrecao: 'Incluir justificativa detalhada conforme Art. 18, I'
    });
  }

  // Art. 18, II - Descrição do objeto
  if (!conteudo.includes('objeto') && !conteudo.includes('descrição')) {
    violacoes.push({
      artigo: 'Art. 18, II',
      descricao: 'Descrição inadequada do objeto',
      gravidade: 'ALTA',
      evidencia: 'Objeto não claramente definido',
      sugestaoCorrecao: 'Incluir descrição detalhada e específica do objeto'
    });
  }

  // Pesquisa de soluções de mercado
  if (!conteudo.includes('pesquisa') && !conteudo.includes('mercado') && !conteudo.includes('solução')) {
    violacoes.push({
      artigo: 'Art. 18, III',
      descricao: 'Ausência de pesquisa de soluções de mercado',
      gravidade: 'ALTA',
      evidencia: 'Pesquisa de mercado não identificada',
      sugestaoCorrecao: 'Incluir pesquisa e análise das soluções de mercado disponíveis'
    });
  }

  // Art. 18, V - Análise de riscos
  if (!conteudo.includes('risco') && !conteudo.includes('análise')) {
    violacoes.push({
      artigo: 'Art. 18, V',
      descricao: 'Ausência de análise de riscos',
      gravidade: 'CRITICA',
      evidencia: 'Análise de riscos não identificada',
      sugestaoCorrecao: 'Incluir levantamento, análise e mitigação de riscos'
    });
  }

  // Mapa de riscos específico
  if (!conteudo.includes('mapa') && !conteudo.includes('matriz')) {
    violacoes.push({
      artigo: 'Metodologia Mauá',
      descricao: 'Ausência de mapa de riscos conforme metodologia',
      gravidade: 'MEDIA',
      evidencia: 'Mapa de riscos não identificado',
      sugestaoCorrecao: 'Incluir mapa de riscos conforme metodologia da Prefeitura'
    });
  }

  // Art. 18, VII - Cronograma
  if (!conteudo.includes('cronograma') && !conteudo.includes('prazo')) {
    violacoes.push({
      artigo: 'Art. 18, VII',
      descricao: 'Ausência de cronograma de execução',
      gravidade: 'MEDIA',
      evidencia: 'Cronograma não especificado',
      sugestaoCorrecao: 'Incluir cronograma detalhado com marcos e prazos'
    });
  }

  // 2. FORMATO E PADRONIZAÇÃO (PREFEITURA DE MAUÁ)

  // Estrutura padrão
  const estruturaPadrao = ['identificação', 'problema', 'alternativas', 'custos', 'benefícios'];
  const estruturaEncontrada = estruturaPadrao.filter(item => conteudo.includes(item));

  if (estruturaEncontrada.length < 3) {
    violacoes.push({
      artigo: 'Padrão Mauá',
      descricao: 'Estrutura não conforme ao padrão da Prefeitura',
      gravidade: 'MEDIA',
      evidencia: `Estrutura incompleta: ${estruturaEncontrada.length}/5 itens`,
      sugestaoCorrecao: 'Adequar estrutura ao formato padrão da Prefeitura de Mauá'
    });
  }

  // 3. CONTEÚDO ESPECÍFICO

  // Identificação do problema
  if (!conteudo.includes('problema') && !conteudo.includes('necessidade')) {
    violacoes.push({
      artigo: 'Conteúdo Específico',
      descricao: 'Identificação inadequada do problema',
      gravidade: 'ALTA',
      evidencia: 'Problema não claramente identificado',
      sugestaoCorrecao: 'Identificar claramente o problema a ser resolvido'
    });
  }

  // Análise de alternativas
  if (!conteudo.includes('alternativa') && !conteudo.includes('opção')) {
    violacoes.push({
      artigo: 'Conteúdo Específico',
      descricao: 'Ausência de análise de alternativas',
      gravidade: 'MEDIA',
      evidencia: 'Alternativas não consideradas',
      sugestaoCorrecao: 'Incluir descrição das alternativas consideradas e critérios de escolha'
    });
  }

  // Análise de custos e benefícios
  if (!conteudo.includes('custo') && !conteudo.includes('benefício')) {
    violacoes.push({
      artigo: 'Conteúdo Específico',
      descricao: 'Ausência de análise de custos e benefícios',
      gravidade: 'ALTA',
      evidencia: 'Análise econômica não identificada',
      sugestaoCorrecao: 'Incluir análise preliminar dos custos e benefícios'
    });
  }

  return violacoes;
}

function verificarEdital(doc: DocumentoAnalise): ViolacaoLei[] {
  const violacoes: ViolacaoLei[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // 1. CONFORMIDADE LEGAL E PRINCÍPIOS DA LEI 14.133/21

  // Art. 5º - Princípios da licitação
  const principiosEdital = [
    'legalidade', 'publicidade', 'transparência', 'julgamento objetivo',
    'segurança jurídica', 'razoabilidade', 'competitividade', 'celeridade', 'economicidade'
  ];

  const principiosEncontrados = principiosEdital.filter(p =>
    conteudo.includes(p.replace(' ', '')) || conteudo.includes(p)
  );

  if (principiosEncontrados.length < 4) {
    violacoes.push({
      artigo: 'Art. 5º',
      descricao: 'Observância insuficiente dos princípios da licitação',
      gravidade: 'ALTA',
      evidencia: `Apenas ${principiosEncontrados.length} princípios identificados`,
      sugestaoCorrecao: 'Demonstrar observância aos princípios do Art. 5º'
    });
  }

  // Art. 40, I - Preâmbulo
  if (!conteudo.includes('preâmbulo') && !conteudo.includes('preambulo')) {
    violacoes.push({
      artigo: 'Art. 40, I',
      descricao: 'Ausência de preâmbulo adequado',
      gravidade: 'MEDIA',
      evidencia: 'Preâmbulo não identificado',
      sugestaoCorrecao: 'Incluir preâmbulo com identificação do órgão e dados da licitação'
    });
  }

  // Art. 40, II - Objeto claramente definido
  if (!conteudo.includes('objeto')) {
    violacoes.push({
      artigo: 'Art. 40, II',
      descricao: 'Objeto da licitação não claramente definido',
      gravidade: 'CRITICA',
      evidencia: 'Objeto ausente ou inadequado',
      sugestaoCorrecao: 'Definir claramente o objeto da licitação'
    });
  }

  // Art. 40, VII - Critérios de julgamento
  if (!conteudo.includes('critério') && !conteudo.includes('julgamento')) {
    violacoes.push({
      artigo: 'Art. 40, VII',
      descricao: 'Critérios de julgamento não especificados',
      gravidade: 'CRITICA',
      evidencia: 'Critérios de julgamento ausentes',
      sugestaoCorrecao: 'Definir claramente os critérios de julgamento conforme Art. 26'
    });
  }

  // Art. 40, VIII - Documentação de habilitação
  const tiposHabilitacao = ['técnica', 'jurídica', 'fiscal', 'econômica'];
  const habilitacaoEncontrada = tiposHabilitacao.filter(tipo => conteudo.includes(tipo));

  if (!conteudo.includes('habilitação') && !conteudo.includes('documentação')) {
    violacoes.push({
      artigo: 'Art. 40, VIII',
      descricao: 'Documentação de habilitação não especificada',
      gravidade: 'CRITICA',
      evidencia: 'Requisitos de habilitação ausentes',
      sugestaoCorrecao: 'Listar documentação necessária para habilitação técnica, jurídica, fiscal e econômica'
    });
  } else if (habilitacaoEncontrada.length < 3) {
    violacoes.push({
      artigo: 'Art. 40, VIII',
      descricao: 'Habilitação incompleta',
      gravidade: 'ALTA',
      evidencia: `Apenas ${habilitacaoEncontrada.length} tipos de habilitação identificados`,
      sugestaoCorrecao: 'Incluir todos os tipos de habilitação: técnica, jurídica, fiscal e econômica'
    });
  }

  // Procedimentos de julgamento e recursos
  if (!conteudo.includes('recurso') && !conteudo.includes('impugnação')) {
    violacoes.push({
      artigo: 'Art. 40',
      descricao: 'Procedimentos de recursos não detalhados',
      gravidade: 'MEDIA',
      evidencia: 'Procedimentos de recursos ausentes',
      sugestaoCorrecao: 'Detalhar procedimentos para apresentação de recursos'
    });
  }

  // Prazos compatíveis
  if (!conteudo.includes('prazo')) {
    violacoes.push({
      artigo: 'Art. 40',
      descricao: 'Prazos não especificados',
      gravidade: 'ALTA',
      evidencia: 'Prazos ausentes',
      sugestaoCorrecao: 'Incluir prazos compatíveis com a complexidade da licitação'
    });
  }

  // Remover verificação de sustentabilidade - não há lei específica

  // 2. FORMATO E PADRONIZAÇÃO (PREFEITURA DE MAUÁ)

  const estruturaEdital = ['capa', 'sumário', 'objeto', 'condições', 'critérios', 'prazos', 'recursos', 'penalidades', 'anexos'];
  const estruturaEncontrada = estruturaEdital.filter(item => conteudo.includes(item));

  if (estruturaEncontrada.length < 6) {
    violacoes.push({
      artigo: 'Padrão Mauá',
      descricao: 'Estrutura não conforme ao modelo padrão',
      gravidade: 'MEDIA',
      evidencia: `Estrutura incompleta: ${estruturaEncontrada.length}/9 itens`,
      sugestaoCorrecao: 'Adequar ao modelo padrão da Prefeitura de Mauá'
    });
  }

  // Verificar modalidade adequada
  if (conteudo.includes('convite') || conteudo.includes('tomada de preço')) {
    violacoes.push({
      artigo: 'Art. 28',
      descricao: 'Modalidade incompatível com a Lei 14.133/21',
      gravidade: 'CRITICA',
      evidencia: 'Modalidade revogada identificada',
      sugestaoCorrecao: 'Utilizar modalidades previstas na Lei 14.133/21'
    });
  }

  // Vinculação ao edital
  if (!conteudo.includes('vinculação') && !conteudo.includes('vinculante')) {
    violacoes.push({
      artigo: 'Art. 5º - Vinculação',
      descricao: 'Princípio da vinculação ao edital não observado',
      gravidade: 'MEDIA',
      evidencia: 'Vinculação não expressa',
      sugestaoCorrecao: 'Expressar que todas as regras são vinculantes'
    });
  }

  // Clareza e objetividade
  const termosVagos = ['aproximadamente', 'cerca de', 'similar', 'parecido', 'mais ou menos'];
  const vagosEncontrados = termosVagos.filter(termo => conteudo.includes(termo));

  if (vagosEncontrados.length > 2) {
    violacoes.push({
      artigo: 'Art. 5º - Objetividade',
      descricao: 'Linguagem vaga prejudica clareza do edital',
      gravidade: 'MEDIA',
      evidencia: `Termos vagos encontrados: ${vagosEncontrados.join(', ')}`,
      sugestaoCorrecao: 'Utilizar linguagem clara, objetiva e impessoal'
    });
  }

  return violacoes;
}

function verificarTR(doc: DocumentoAnalise): ViolacaoLei[] {
  const violacoes: ViolacaoLei[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // 1. CONFORMIDADE LEGAL E PRINCÍPIOS DA LEI 14.133/21

  // Conformidade com ETP
  if (!conteudo.includes('etp') && !conteudo.includes('estudo técnico')) {
    violacoes.push({
      artigo: 'Conformidade ETP',
      descricao: 'TR não demonstra conformidade com ETP aprovado',
      gravidade: 'ALTA',
      evidencia: 'Referência ao ETP não identificada',
      sugestaoCorrecao: 'Demonstrar conformidade com o ETP aprovado'
    });
  }

  // Princípios aplicáveis ao TR
  const principiosTR = ['legalidade', 'eficiência', 'planejamento', 'motivação', 'segurança jurídica'];
  const principiosEncontrados = principiosTR.filter(p => conteudo.includes(p));

  if (principiosEncontrados.length < 2) {
    violacoes.push({
      artigo: 'Art. 5º',
      descricao: 'Observância insuficiente dos princípios aplicáveis',
      gravidade: 'MEDIA',
      evidencia: `Apenas ${principiosEncontrados.length} princípios identificados`,
      sugestaoCorrecao: 'Demonstrar observância aos princípios da Lei 14.133/21'
    });
  }

  // Art. 40, VI - Especificações técnicas detalhadas
  if (!conteudo.includes('especificação') && !conteudo.includes('técnica')) {
    violacoes.push({
      artigo: 'Art. 40, VI',
      descricao: 'Especificações técnicas inadequadas',
      gravidade: 'CRITICA',
      evidencia: 'Especificações técnicas não detalhadas',
      sugestaoCorrecao: 'Detalhar especificações técnicas do objeto'
    });
  }

  // Quantitativos e estimativas
  if (!conteudo.includes('quantidade') && !conteudo.includes('unidade')) {
    violacoes.push({
      artigo: 'Art. 40, VI',
      descricao: 'Quantitativos não especificados',
      gravidade: 'ALTA',
      evidencia: 'Quantidades e unidades ausentes',
      sugestaoCorrecao: 'Especificar quantidades e unidades de medida'
    });
  }

  // Compatibilidade com necessidade real
  if (!conteudo.includes('necessidade') && !conteudo.includes('demanda')) {
    violacoes.push({
      artigo: 'Adequação',
      descricao: 'Quantitativos não demonstram compatibilidade com necessidade',
      gravidade: 'MEDIA',
      evidencia: 'Justificativa de quantitativos ausente',
      sugestaoCorrecao: 'Demonstrar compatibilidade dos quantitativos com a necessidade real'
    });
  }

  // Análise de riscos específica
  if (!conteudo.includes('risco') && !conteudo.includes('mapa')) {
    violacoes.push({
      artigo: 'Análise de Riscos',
      descricao: 'Ausência de análise de riscos específica para execução',
      gravidade: 'ALTA',
      evidencia: 'Análise de riscos não identificada',
      sugestaoCorrecao: 'Incluir análise de riscos específica para execução do objeto'
    });
  }

  // 2. FORMATO E PADRONIZAÇÃO (PREFEITURA DE MAUÁ)

  const estruturaTR = ['objeto', 'especificações', 'quantitativos', 'prazos', 'fiscalização'];
  const estruturaEncontrada = estruturaTR.filter(item => conteudo.includes(item));

  if (estruturaEncontrada.length < 4) {
    violacoes.push({
      artigo: 'Padrão Mauá',
      descricao: 'Estrutura não conforme ao padrão da Prefeitura',
      gravidade: 'MEDIA',
      evidencia: `Estrutura incompleta: ${estruturaEncontrada.length}/5 itens`,
      sugestaoCorrecao: 'Adequar estrutura ao padrão da Prefeitura de Mauá'
    });
  }

  // Linguagem técnica adequada
  if (!conteudo.includes('norma') && !conteudo.includes('padrão') && !conteudo.includes('abnt')) {
    violacoes.push({
      artigo: 'Padrão Técnico',
      descricao: 'Ausência de referências normativas técnicas',
      gravidade: 'MEDIA',
      evidencia: 'Normas técnicas não referenciadas',
      sugestaoCorrecao: 'Incluir referências a normas técnicas aplicáveis (ABNT, etc.)'
    });
  }

  // 3. CONTEÚDO ESPECÍFICO

  // Definição precisa do objeto
  if (!conteudo.includes('objeto')) {
    violacoes.push({
      artigo: 'Definição do Objeto',
      descricao: 'Definição imprecisa do objeto',
      gravidade: 'CRITICA',
      evidencia: 'Objeto não claramente definido',
      sugestaoCorrecao: 'Definir precisamente o objeto com especificações detalhadas'
    });
  }

  // Critérios de avaliação e aceitação
  if (!conteudo.includes('aceitação') && !conteudo.includes('critério') && !conteudo.includes('avaliação')) {
    violacoes.push({
      artigo: 'Critérios de Aceitação',
      descricao: 'Ausência de critérios para avaliação da execução',
      gravidade: 'ALTA',
      evidencia: 'Critérios de aceitação não definidos',
      sugestaoCorrecao: 'Definir critérios claros para avaliação da execução e aceitação'
    });
  }

  // Condições de fiscalização
  if (!conteudo.includes('fiscalização') && !conteudo.includes('acompanhamento')) {
    violacoes.push({
      artigo: 'Fiscalização',
      descricao: 'Condições de fiscalização não especificadas',
      gravidade: 'ALTA',
      evidencia: 'Fiscalização não definida',
      sugestaoCorrecao: 'Especificar condições para fiscalização e acompanhamento'
    });
  }

  // Gestão contratual
  if (!conteudo.includes('gestão') && !conteudo.includes('gestor')) {
    violacoes.push({
      artigo: 'Gestão Contratual',
      descricao: 'Gestão contratual não definida',
      gravidade: 'MEDIA',
      evidencia: 'Responsabilidades de gestão ausentes',
      sugestaoCorrecao: 'Definir responsabilidades para gestão contratual'
    });
  }

  // Obrigações das partes
  const obrigacoes = ['contratado', 'contratante'];
  const obrigacoesEncontradas = obrigacoes.filter(obr => conteudo.includes(obr));

  if (obrigacoesEncontradas.length < 2) {
    violacoes.push({
      artigo: 'Obrigações',
      descricao: 'Obrigações das partes não claramente definidas',
      gravidade: 'ALTA',
      evidencia: 'Obrigações incompletas',
      sugestaoCorrecao: 'Definir claramente obrigações do contratado e contratante'
    });
  }

  // Prazos de execução
  if (!conteudo.includes('prazo') && !conteudo.includes('cronograma')) {
    violacoes.push({
      artigo: 'Prazos',
      descricao: 'Prazos de execução não especificados',
      gravidade: 'ALTA',
      evidencia: 'Prazos ausentes',
      sugestaoCorrecao: 'Especificar prazos detalhados para execução'
    });
  }

  // Penalidades e sanções
  if (!conteudo.includes('penalidade') && !conteudo.includes('sanção') && !conteudo.includes('multa')) {
    violacoes.push({
      artigo: 'Penalidades',
      descricao: 'Penalidades não especificadas',
      gravidade: 'MEDIA',
      evidencia: 'Sistema de penalidades ausente',
      sugestaoCorrecao: 'Incluir sistema de penalidades e sanções'
    });
  }

  return violacoes;
}

function verificarCoerenciaGeral(documentos: DocumentoAnalise[]): ViolacaoLei[] {
  const violacoes: ViolacaoLei[] = [];

  // Verificar coerência de objetos entre documentos (evitar medicamento vs ração animal)
  const objetosExtraidos = documentos.map(doc => ({
    tipo: doc.tipo,
    objeto: extrairObjeto(doc.conteudo),
    palavrasChave: extrairPalavrasChave(doc.conteudo)
  }));

  // Verificar se há conflitos óbvios entre objetos
  const conflitosDetectados = detectarConflitosObjeto(objetosExtraidos);

  if (conflitosDetectados.length > 0) {
    violacoes.push({
      artigo: 'Coerência de Objeto',
      descricao: 'CONFLITO CRÍTICO: Objetos completamente diferentes entre documentos',
      gravidade: 'CRITICA',
      evidencia: `Conflitos detectados: ${conflitosDetectados.join(', ')}`,
      sugestaoCorrecao: 'URGENTE: Verificar se os documentos se referem ao mesmo objeto de contratação'
    });
  }

  // Verificar similaridade geral dos objetos
  const objetosLimpos = objetosExtraidos.map(obj => obj.objeto).filter(obj => obj);
  const similaridade = calcularSimilaridadeObjetos(objetosLimpos);

  if (similaridade < 0.5 && objetosLimpos.length > 1) {
    violacoes.push({
      artigo: 'Coerência Geral',
      descricao: 'Baixa similaridade entre objetos dos documentos',
      gravidade: 'ALTA',
      evidencia: `Similaridade: ${Math.round(similaridade * 100)}% (mínimo: 50%)`,
      sugestaoCorrecao: 'Alinhar descrição do objeto em todos os documentos'
    });
  }

  return violacoes;
}

function extrairObjeto(conteudo: string): string | null {
  // Extrair objeto com múltiplos padrões
  const padroes = [
    /objeto[:\s]+([^.\n]+)/i,
    /contratação\s+de\s+([^.\n]+)/i,
    /aquisição\s+de\s+([^.\n]+)/i,
    /prestação\s+de\s+serviços?\s+de\s+([^.\n]+)/i
  ];

  for (const padrao of padroes) {
    const match = conteudo.match(padrao);
    if (match) {
      return match[1].trim().toLowerCase();
    }
  }

  return null;
}

function extrairPalavrasChave(conteudo: string): string[] {
  const conteudoLimpo = conteudo.toLowerCase();

  // Categorias de objetos comuns
  const categorias = {
    medicamentos: ['medicamento', 'remédio', 'fármaco', 'droga', 'antibiótico', 'analgésico'],
    alimentos: ['ração', 'alimento', 'comida', 'nutrição', 'alimentação'],
    materiais: ['material', 'equipamento', 'ferramenta', 'utensílio'],
    serviços: ['serviço', 'prestação', 'manutenção', 'limpeza', 'consultoria'],
    obras: ['obra', 'construção', 'reforma', 'pavimentação'],
    veículos: ['veículo', 'carro', 'caminhão', 'ônibus', 'ambulância'],
    informática: ['computador', 'software', 'sistema', 'tecnologia', 'informática']
  };

  const palavrasEncontradas = [];

  for (const [categoria, palavras] of Object.entries(categorias)) {
    for (const palavra of palavras) {
      if (conteudoLimpo.includes(palavra)) {
        palavrasEncontradas.push(categoria);
        break;
      }
    }
  }

  return palavrasEncontradas;
}

function detectarConflitosObjeto(objetosExtraidos: any[]): string[] {
  const conflitos = [];

  // Verificar se há categorias conflitantes
  const todasCategorias = objetosExtraidos.flatMap(obj => obj.palavrasChave);
  const categoriasUnicas = [...new Set(todasCategorias)];

  // Conflitos óbvios
  const conflitosGraves = [
    ['medicamentos', 'alimentos'],
    ['medicamentos', 'ração'],
    ['obras', 'medicamentos'],
    ['veículos', 'medicamentos'],
    ['informática', 'alimentos']
  ];

  for (const [cat1, cat2] of conflitosGraves) {
    if (categoriasUnicas.includes(cat1) && categoriasUnicas.includes(cat2)) {
      conflitos.push(`${cat1} vs ${cat2}`);
    }
  }

  return conflitos;
}

function calcularSimilaridadeObjetos(objetos: string[]): number {
  if (objetos.length < 2) return 1;

  const objeto1 = objetos[0];
  const objeto2 = objetos[1];

  if (!objeto1 || !objeto2) return 0;

  // Calcular similaridade simples baseada em palavras comuns
  const palavras1 = objeto1.split(/\s+/);
  const palavras2 = objeto2.split(/\s+/);

  const palavrasComuns = palavras1.filter(p =>
    palavras2.some(p2 => p2.includes(p) || p.includes(p2))
  );

  const totalPalavras = Math.max(palavras1.length, palavras2.length);

  return palavrasComuns.length / totalPalavras;
}

function calcularScoreConformidade(violacoes: ViolacaoLei[]): number {
  let penalizacao = 0;
  
  violacoes.forEach(violacao => {
    switch (violacao.gravidade) {
      case 'CRITICA': penalizacao += 25; break;
      case 'ALTA': penalizacao += 15; break;
      case 'MEDIA': penalizacao += 8; break;
      case 'BAIXA': penalizacao += 3; break;
    }
  });

  return Math.max(0, 100 - penalizacao);
}

function gerarRecomendacoesLegais(violacoes: ViolacaoLei[]): any[] {
  return violacoes
    .filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA')
    .map(violacao => ({
      prioridade: violacao.gravidade === 'CRITICA' ? 'ALTA' : 'MEDIA',
      titulo: `Correção necessária - ${violacao.artigo}`,
      descricao: violacao.descricao,
      acoes: [violacao.sugestaoCorrecao],
      artigo: violacao.artigo
    }));
}
