{"name": "inovaprocess-new", "version": "0.1.0", "private": true, "scripts": {"predev": "node src/scripts/prevent-reset.js --backup && node src/scripts/monitor-resets.js", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backup": "node src/scripts/prevent-reset.js --backup", "clean-cache": "node src/scripts/prevent-reset.js --clean-cache", "restore-backup": "node src/scripts/prevent-reset.js --restore", "fix": "node src/scripts/prevent-reset.js --clean-cache", "prebuild": "node src/scripts/monitor-resets.js", "check-protection": "node src/scripts/monitor-resets.js", "restore-checkpoint": "node src/scripts/prevent-reset.js --restore-checkpoint", "checkpoint": "node src/scripts/checkpoint-system.js create", "checkpoint:list": "node src/scripts/checkpoint-system.js list", "checkpoint:restore": "node src/scripts/checkpoint-system.js restore"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.13.2", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "lottie-react": "^2.4.0", "lucide-react": "^0.400.0", "next": "15.3.3", "next-themes": "^0.2.1", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "20.19.0", "@types/react": "18.3.23", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "5.8.3"}}