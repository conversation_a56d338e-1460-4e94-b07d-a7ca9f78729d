{"git.countBadge": "off", "git.decorations.enabled": true, "git.ignoreLimitWarning": true, "files.exclude": {"**/node_modules": true, "**/.next": true, "**/backups": true, "**/backup-*": true, "**/.env": true, "**/.env.local": true, "**/.env.production": true, "**/checkpoints": true, "**/*.log": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/backups": true, "**/backup-*": true}}