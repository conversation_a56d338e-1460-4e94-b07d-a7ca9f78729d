const fs = require('fs');
const path = require('path');

// Script para processar TODOS os arquivos CSV de TODAS as pastas
// NUNCA MAIS ESQUECER: databackup-junho-v1, databackup-maio-v1, databackup-29 e 30-04

function processAllCSV() {
  console.log('🔥 PROCESSANDO TODOS OS ARQUIVOS CSV DE TODAS AS PASTAS!');
  
  const dataDir = path.join(__dirname, '../../data');
  
  // TODAS AS PASTAS QUE PRECISAM SER PROCESSADAS
  const folders = [
    'databackup-29 e 30-04',
    'databackup-maio-v1', 
    'databackup-junho-v1'
  ];
  
  let totalProcessed = 0;
  
  folders.forEach(folder => {
    console.log(`\n📁 PROCESSANDO PASTA: ${folder}`);
    
    const folderPath = path.join(dataDir, folder);
    
    if (!fs.existsSync(folderPath)) {
      console.log(`❌ Pasta não encontrada: ${folderPath}`);
      return;
    }
    
    // Listar todos os arquivos CSV na pasta
    const files = fs.readdirSync(folderPath);
    const csvFiles = files.filter(file => file.endsWith('.csv') && !file.includes('-processado'));
    
    console.log(`📄 Arquivos CSV encontrados: ${csvFiles.length}`);
    
    csvFiles.forEach(file => {
      console.log(`\n🔄 Processando: ${file}`);
      
      const inputPath = path.join(folderPath, file);
      const outputFile = file.replace('.csv', '-processado.csv');
      const outputPath = path.join(folderPath, outputFile);
      
      try {
        // Ler arquivo original
        const content = fs.readFileSync(inputPath, 'utf-8');
        
        // Dividir em linhas
        const lines = content.split('\n');
        console.log(`   📊 Total de linhas: ${lines.length}`);
        
        // Processar cada linha
        const processedLines = [];
        
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();
          
          // Pular linhas vazias
          if (!line || line === ',,,,,,,,,,,,,,,,,,,,,,,') {
            continue;
          }
          
          // Processar linha
          let processedLine = line;
          
          // Substituir campos vazios por aspas vazias
          processedLine = processedLine.replace(/,-,/g, ',,');
          processedLine = processedLine.replace(/^-,/, ',');
          processedLine = processedLine.replace(/,-$/, ',');
          
          // Adicionar aspas duplas em todos os campos se não tiver
          if (i === 0) {
            // Header - adicionar aspas
            const fields = processedLine.split(',');
            const quotedFields = fields.map(field => {
              field = field.trim();
              if (!field.startsWith('"')) {
                return `"${field}"`;
              }
              return field;
            });
            processedLine = quotedFields.join(',');
          } else {
            // Dados - processar campos
            const fields = [];
            let currentField = '';
            let inQuotes = false;
            
            for (let j = 0; j < processedLine.length; j++) {
              const char = processedLine[j];
              
              if (char === '"') {
                inQuotes = !inQuotes;
                currentField += char;
              } else if (char === ',' && !inQuotes) {
                // Fim do campo
                let field = currentField.trim();
                
                // Se campo não tem aspas, adicionar
                if (!field.startsWith('"') && field !== '') {
                  field = `"${field}"`;
                } else if (field === '' || field === '-') {
                  field = '';
                }
                
                fields.push(field);
                currentField = '';
              } else {
                currentField += char;
              }
            }
            
            // Último campo
            let field = currentField.trim();
            if (!field.startsWith('"') && field !== '') {
              field = `"${field}"`;
            } else if (field === '' || field === '-') {
              field = '';
            }
            fields.push(field);
            
            processedLine = fields.join(',');
          }
          
          processedLines.push(processedLine);
        }
        
        console.log(`   ✅ Linhas processadas: ${processedLines.length}`);
        
        // Escrever arquivo processado
        const finalContent = processedLines.join('\n');
        fs.writeFileSync(outputPath, finalContent, 'utf-8');
        
        console.log(`   ✅ Salvo: ${outputFile}`);
        console.log(`   📊 Registros válidos: ${processedLines.length - 1}`);
        
        totalProcessed++;
        
      } catch (error) {
        console.error(`   ❌ Erro ao processar ${file}:`, error.message);
      }
    });
  });
  
  console.log(`\n🎯 PROCESSAMENTO CONCLUÍDO!`);
  console.log(`📊 Total de arquivos processados: ${totalProcessed}`);
}

// Executar se chamado diretamente
if (require.main === module) {
  processAllCSV();
}

module.exports = { processAllCSV };
