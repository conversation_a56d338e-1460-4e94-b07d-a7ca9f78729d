const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configurações
const CHECKPOINT_DIR = path.join(process.cwd(), 'checkpoints');
const BACKUP_DIR = path.join(process.cwd(), 'backups');
const APP_DIR = path.join(process.cwd(), 'src', 'app');
const LOG_FILE = path.join(process.cwd(), 'checkpoint.log');

// Garantir que os diretórios existam
[CHECKPOINT_DIR, BACKUP_DIR].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Função para registrar logs
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  fs.appendFileSync(LOG_FILE, logMessage);
}

// Criar um checkpoint manual
function createCheckpoint(name) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const checkpointName = name ? `${name}-${timestamp}` : timestamp;
  const checkpointDir = path.join(CHECKPOINT_DIR, checkpointName);
  
  log(`Criando checkpoint: ${checkpointName}`);
  
  // Criar diretório do checkpoint
  fs.mkdirSync(checkpointDir, { recursive: true });
  
  // Copiar a pasta src/app para o checkpoint
  copyDirectory(APP_DIR, path.join(checkpointDir, 'app'));
  
  // Criar arquivo de metadados
  const metadata = {
    name: checkpointName,
    timestamp: new Date().toISOString(),
    description: name || 'Checkpoint manual',
    files: getDirectoryStats(APP_DIR)
  };
  
  fs.writeFileSync(
    path.join(checkpointDir, 'metadata.json'),
    JSON.stringify(metadata, null, 2)
  );
  
  // Atualizar o arquivo de checkpoint atual
  fs.writeFileSync(
    path.join(process.cwd(), '.checkpoint'),
    checkpointName
  );
  
  log(`✅ Checkpoint "${checkpointName}" criado com sucesso!`);
  return checkpointName;
}

// Listar todos os checkpoints disponíveis
function listCheckpoints() {
  if (!fs.existsSync(CHECKPOINT_DIR)) {
    log('Nenhum checkpoint encontrado.');
    return [];
  }
  
  const checkpoints = fs.readdirSync(CHECKPOINT_DIR)
    .filter(file => {
      const stats = fs.statSync(path.join(CHECKPOINT_DIR, file));
      return stats.isDirectory();
    })
    .map(dir => {
      try {
        const metadataPath = path.join(CHECKPOINT_DIR, dir, 'metadata.json');
        if (fs.existsSync(metadataPath)) {
          return JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        }
        return { name: dir, timestamp: null, description: 'Sem metadados' };
      } catch (e) {
        return { name: dir, timestamp: null, description: 'Erro ao ler metadados' };
      }
    })
    .sort((a, b) => {
      if (!a.timestamp) return 1;
      if (!b.timestamp) return -1;
      return new Date(b.timestamp) - new Date(a.timestamp);
    });
  
  log(`${checkpoints.length} checkpoints encontrados.`);
  checkpoints.forEach((cp, index) => {
    log(`${index + 1}. ${cp.name} - ${cp.description}`);
  });
  
  return checkpoints;
}

// Restaurar um checkpoint
function restoreCheckpoint(checkpointName) {
  const checkpointDir = path.join(CHECKPOINT_DIR, checkpointName);
  
  if (!fs.existsSync(checkpointDir)) {
    log(`❌ Erro: Checkpoint "${checkpointName}" não encontrado.`);
    return false;
  }
  
  log(`Restaurando checkpoint: ${checkpointName}`);
  
  // Criar backup do estado atual antes de restaurar
  const backupName = createBackup('pre-restore');
  log(`Backup criado: ${backupName}`);
  
  // Limpar a pasta app atual
  if (fs.existsSync(APP_DIR)) {
    fs.rmSync(APP_DIR, { recursive: true, force: true });
    fs.mkdirSync(APP_DIR, { recursive: true });
  }
  
  // Copiar o checkpoint para a pasta app
  copyDirectory(path.join(checkpointDir, 'app'), APP_DIR);
  
  // Atualizar o arquivo de checkpoint atual
  fs.writeFileSync(
    path.join(process.cwd(), '.checkpoint'),
    checkpointName
  );
  
  log(`✅ Checkpoint "${checkpointName}" restaurado com sucesso!`);
  log(`Se necessário, o estado anterior pode ser recuperado do backup: ${backupName}`);
  
  return true;
}

// Criar um backup (usado internamente)
function createBackup(reason) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupName = reason ? `${reason}-${timestamp}` : timestamp;
  const backupDir = path.join(BACKUP_DIR, backupName);
  
  // Criar diretório do backup
  fs.mkdirSync(backupDir, { recursive: true });
  
  // Copiar a pasta src/app para o backup
  if (fs.existsSync(APP_DIR)) {
    copyDirectory(APP_DIR, path.join(backupDir, 'app'));
  }
  
  return backupName;
}

// Função para copiar diretórios recursivamente
function copyDirectory(source, destination) {
  if (!fs.existsSync(source)) {
    log(`Aviso: Diretório de origem não existe: ${source}`);
    return;
  }
  
  // Cria o diretório de destino se não existir
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }
  
  // Lê todos os arquivos/diretórios na pasta de origem
  const entries = fs.readdirSync(source, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(source, entry.name);
    const destPath = path.join(destination, entry.name);
    
    if (entry.isDirectory()) {
      // Recursivamente copia subdiretórios
      copyDirectory(srcPath, destPath);
    } else {
      // Copia arquivos
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Obter estatísticas do diretório (usado para metadados)
function getDirectoryStats(dir) {
  let stats = {
    totalFiles: 0,
    totalDirectories: 0,
    fileTypes: {}
  };
  
  function processDir(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        stats.totalDirectories++;
        processDir(fullPath);
      } else {
        stats.totalFiles++;
        const ext = path.extname(entry.name).toLowerCase();
        stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1;
      }
    }
  }
  
  if (fs.existsSync(dir)) {
    processDir(dir);
  }
  
  return stats;
}

// Exportar funções para uso em scripts
module.exports = {
  createCheckpoint,
  listCheckpoints,
  restoreCheckpoint,
  createBackup,
  log
};

// Executar como script independente
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'create':
      createCheckpoint(args[1]);
      break;
    case 'list':
      listCheckpoints();
      break;
    case 'restore':
      if (!args[1]) {
        log('❌ Erro: Especifique o nome do checkpoint a ser restaurado.');
        log('Uso: node checkpoint-system.js restore NOME_DO_CHECKPOINT');
        process.exit(1);
      }
      restoreCheckpoint(args[1]);
      break;
    default:
      log('Comandos disponíveis:');
      log('  create [nome] - Cria um novo checkpoint');
      log('  list - Lista todos os checkpoints disponíveis');
      log('  restore NOME - Restaura um checkpoint específico');
  }
}