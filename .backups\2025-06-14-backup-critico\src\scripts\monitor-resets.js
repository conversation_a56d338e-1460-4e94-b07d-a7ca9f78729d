const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Arquivos que nunca devem ser modificados para evitar resets
const protectedFiles = [
  'src/scripts/fix-routes.js',
  'src/scripts/reset-app.js',
  'src/scripts/clean-routes.js',
  'src/scripts/find-dynamic-routes.js',
  'src/scripts/manual-clean.js'
];

// Verifica se algum arquivo protegido foi modificado
function checkProtectedFiles() {
  let modified = false;
  
  for (const file of protectedFiles) {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Verifica se o arquivo não contém o aviso de proteção
      if (!content.includes('SCRIPT PROTEGIDO CONTRA RESETS ACIDENTAIS')) {
        console.log(`⚠️ ALERTA: Arquivo ${file} foi modificado e pode causar resets!`);
        
        // Restaura a versão segura do arquivo
        execSync(`node src/scripts/prevent-reset.js`);
        modified = true;
      }
    }
  }
  
  return modified;
}

// Cria um arquivo de bloqueio para evitar execuções simultâneas
const lockFile = path.join(process.cwd(), '.monitor-lock');
if (fs.existsSync(lockFile)) {
  console.log('Monitor já está em execução.');
  process.exit(0);
}

// Cria o arquivo de bloqueio
fs.writeFileSync(lockFile, new Date().toISOString());

// Executa a verificação inicial
console.log('Iniciando monitor de proteção contra resets...');
const modified = checkProtectedFiles();

if (modified) {
  console.log('🛡️ Proteções restauradas com sucesso!');
} else {
  console.log('✅ Todos os arquivos estão protegidos.');
}

// Remove o arquivo de bloqueio
fs.unlinkSync(lockFile);