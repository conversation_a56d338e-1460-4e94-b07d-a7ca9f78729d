'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import CurrencyInput from '@/components/ui/currency-input';
import {
  Plus,
  Save,
  ArrowLeft,
  Calendar,
  Building,
  FileText,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';

export default function NovoProcessoPage() {
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    numeroProcesso: '',
    objeto: '',
    modalidade: '',
    secretaria: '',
    responsavel: '',
    valorEstimado: '',
    dataInicio: '',
    dataEntradaCLMP: '',
    prioridade: 'Normal',
    status: '',
    local: '',
    observacoes: '',
    // Fontes de recursos
    fonteTesouro: '',
    fonteEstadual: '',
    fonteFundo: '',
    fonteFederal: '',
    fonteFinisa: '',
    // Datas de vencimento das fontes não-tesouro
    dataVencimentoEstadual: '',
    dataVencimentoFundo: '',
    dataVencimentoFederal: '',
    dataVencimentoFinisa: ''
  });

  // Palavras-chave que indicam prioridade alta
  const palavrasChavePrioridadeAlta = [
    'medicamento', 'medicamentos', 'farmácia', 'farmacêutico',
    'judicial', 'liminar', 'mandado', 'decisão judicial',
    'urgente', 'emergência', 'emergencial',
    'saúde pública', 'epidemia', 'pandemia',
    'merenda escolar', 'alimentação escolar',
    'convênio', 'federal', 'estadual', 'fundo'
  ];

  const detectarPrioridadeAlta = (objeto: string): boolean => {
    const objetoLower = objeto.toLowerCase();
    return palavrasChavePrioridadeAlta.some(palavra =>
      objetoLower.includes(palavra.toLowerCase())
    );
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Se o campo é objeto, verificar se deve definir prioridade alta
      if (field === 'objeto' && value) {
        const temPrioridadeAlta = detectarPrioridadeAlta(value);
        if (temPrioridadeAlta) {
          newData.prioridade = 'Alta';
        }
      }

      return newData;
    });
  };

  const handleSave = async () => {
    setSaving(true);

    try {
      // Criar objeto do processo com todos os campos
      const novoProcesso = {
        PROCESSO: formData.numeroProcesso,
        OBJETO: formData.objeto,
        MODALIDADE: formData.modalidade,
        REQUISITANTE: formData.secretaria,
        RESPONSÁVEL: formData.responsavel,
        'VALOR ESTIMADO': formData.valorEstimado,
        'DATA DE INÍCIO DO PROCESSO': formData.dataInicio,
        'DATA ENTRADA NA CLMP': formData.dataEntradaCLMP,
        PRIORIDADE: formData.prioridade,
        STATUS: formData.status,
        LOCAL: formData.local,
        // Fontes de recursos
        'Fonte 0001 (TESOURO)': formData.fonteTesouro || 'R$ 0,00',
        'Fonte 0002 (ESTADUAL)': formData.fonteEstadual || 'R$ 0,00',
        'Fonte 0003 (FUNDO)': formData.fonteFundo || 'R$ 0,00',
        'Fonte 0005 (FEDERAL)': formData.fonteFederal || 'R$ 0,00',
        'Fonte 0007 (FINISA)': formData.fonteFinisa || 'R$ 0,00',
        // Datas de vencimento das fontes não-tesouro
        'Data Vencimento Estadual': formData.dataVencimentoEstadual || '',
        'Data Vencimento Fundo': formData.dataVencimentoFundo || '',
        'Data Vencimento Federal': formData.dataVencimentoFederal || '',
        'Data Vencimento Finisa': formData.dataVencimentoFinisa || '',
        // Observações
        OBSERVACOES: formData.observacoes
      };

      // Verificar se precisa gerar alerta para recursos sem data
      const fontesComProblema = [];
      if (formData.fonteEstadual && !formData.dataVencimentoEstadual) fontesComProblema.push('Estadual');
      if (formData.fonteFundo && !formData.dataVencimentoFundo) fontesComProblema.push('Fundo');
      if (formData.fonteFederal && !formData.dataVencimentoFederal) fontesComProblema.push('Federal');
      if (formData.fonteFinisa && !formData.dataVencimentoFinisa) fontesComProblema.push('Finisa');

      if (fontesComProblema.length > 0) {
        console.log(`⚠️ ALERTA: Processo ${formData.numeroProcesso} tem recursos externos sem datas de vencimento:`, fontesComProblema);
        // Aqui seria enviado alerta para Isabela e Tath
      }

      // Simular salvamento
      console.log('💾 Salvando processo:', novoProcesso);

      setTimeout(() => {
        setSaving(false);
        alert(`✅ Processo ${formData.numeroProcesso} salvo com sucesso!${fontesComProblema.length > 0 ? '\n\n⚠️ Alerta gerado para Isabela e Tath: Recursos externos sem datas de vencimento.' : ''}`);
        // Aqui redirecionaria para a lista de processos
      }, 2000);

    } catch (error) {
      setSaving(false);
      alert('❌ Erro ao salvar processo. Tente novamente.');
    }
  };

  // Validação do formulário - campos obrigatórios
  const temFonteRecurso = formData.fonteTesouro || formData.fonteEstadual || formData.fonteFundo || formData.fonteFederal || formData.fonteFinisa;

  const isFormValid = formData.numeroProcesso &&
                     formData.objeto &&
                     formData.modalidade &&
                     formData.secretaria &&
                     formData.status &&
                     temFonteRecurso &&
                     formData.dataInicio &&
                     formData.dataEntradaCLMP;

  return (
    <div className="space-y-6 processos-page">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/processos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Novo Processo</h1>
            <p className="text-muted-foreground mt-2">
              Cadastre um novo processo para acompanhamento
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <Plus className="mr-1 h-3 w-3" />
            Novo Cadastro
          </Badge>
        </div>
      </div>

      {/* Layout Horizontal - Formulário + Resumo Fixo */}
      <div className="flex gap-6">
        {/* Formulário Principal - Rolável */}
        <div className="flex-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Informações Básicas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Número do Processo *
                  </label>
                  <Input
                    placeholder="Ex: 2024.001.001"
                    value={formData.numeroProcesso}
                    onChange={(e) => handleInputChange('numeroProcesso', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Tab') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="modalidade"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="numeroProcesso"
                    tabIndex={1}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Modalidade *
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent [&>option]:bg-background [&>option]:text-foreground"
                    value={formData.modalidade}
                    onChange={(e) => handleInputChange('modalidade', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="objeto"]') as HTMLElement;
                        nextElement?.focus();
                      }
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        handleInputChange('modalidade', '');
                      }
                    }}
                    name="modalidade"
                    tabIndex={2}
                  >
                    <option value="">Selecione...</option>
                    <option value="Pregão Eletrônico">Pregão Eletrônico</option>
                    <option value="Pregão Presencial">Pregão Presencial</option>
                    <option value="Concorrência">Concorrência</option>
                    <option value="Dispensa">Dispensa</option>
                    <option value="Inexigibilidade">Inexigibilidade</option>
                    <option value="Chamamento Público">Chamamento Público</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Objeto *
                  <span className="text-xs text-muted-foreground ml-2">
                    ({formData.objeto.length}/150 caracteres)
                  </span>
                </label>
                <textarea
                  className="w-full p-2 border border-input rounded-md bg-background min-h-[80px]"
                  placeholder="DESCREVA RESUMIDAMENTE O OBJETO DA LICITAÇÃO..."
                  value={formData.objeto}
                  maxLength={150}
                  onChange={(e) => {
                    if (e.target.value.length <= 150) {
                      handleInputChange('objeto', e.target.value);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const nextElement = document.querySelector('[name="valorEstimado"]') as HTMLElement;
                      nextElement?.focus();
                    }
                  }}
                  name="objeto"
                  tabIndex={3}
                />
                {formData.objeto.length >= 140 && (
                  <p className="text-xs text-amber-600 mt-1">
                    ⚠️ Atenção: Você está próximo do limite de caracteres. Seja mais conciso.
                  </p>
                )}
                {formData.objeto.length >= 150 && (
                  <p className="text-xs text-red-600 mt-1">
                    🚫 Limite de caracteres atingido! Texto muito longo pode quebrar o layout.
                  </p>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Valor Estimado
                  </label>
                  <CurrencyInput
                    value={formData.valorEstimado}
                    onChange={(value) => handleInputChange('valorEstimado', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="status"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="valorEstimado"
                    tabIndex={4}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Status *
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent [&>option]:bg-background [&>option]:text-foreground"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="secretaria"]') as HTMLElement;
                        nextElement?.focus();
                      }
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        handleInputChange('status', '');
                      }
                    }}
                    name="status"
                    tabIndex={5}
                  >
                    <option value="">Selecione...</option>
                    <option value="Em Instrução">Em Instrução</option>
                    <option value="Para Pesquisa de Preços">Para Pesquisa de Preços</option>
                    <option value="Aguardando Documento do Fornecedor">Aguardando Documento do Fornecedor</option>
                    <option value="Para Análise Técnica">Para Análise Técnica</option>
                    <option value="Para Análise Orçamentária">Para Análise Orçamentária</option>
                    <option value="Retornou Após Análise Orçamentária">Retornou Após Análise Orçamentária</option>
                    <option value="Para Análise Jurídica">Para Análise Jurídica</option>
                    <option value="Retornou Após Análise Jurídica">Retornou Após Análise Jurídica</option>
                    <option value="Para Adequações">Para Adequações</option>
                    <option value="Encaminhado Para a Secretaria">Encaminhado Para a Secretaria</option>
                    <option value="Para Publicação de Edital">Para Publicação de Edital</option>
                    <option value="Aguardando Abertura da Licitação">Aguardando Abertura da Licitação</option>
                    <option value="Licitação em Andamento">Licitação em Andamento</option>
                    <option value="Para Elaboração de Contrato/Ata">Para Elaboração de Contrato/Ata</option>
                    <option value="Para Homologação e AUDESP">Para Homologação e AUDESP</option>
                    <option value="Para Assinatura da Homologação">Para Assinatura da Homologação</option>
                    <option value="Finalizado">Finalizado</option>
                    <option value="Para Abertura de Processos de Gerenciamento">Para Abertura de Processos de Gerenciamento</option>
                    <option value="Para Análise e Distribuição">Para Análise e Distribuição</option>
                    <option value="Para Ratificação da Pesquisa de Preços">Para Ratificação da Pesquisa de Preços</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5" />
                Responsabilidade
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Secretaria *
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent [&>option]:bg-background [&>option]:text-foreground"
                    value={formData.secretaria}
                    onChange={(e) => handleInputChange('secretaria', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="responsavel"]') as HTMLElement;
                        nextElement?.focus();
                      }
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        handleInputChange('secretaria', '');
                      }
                    }}
                    name="secretaria"
                    tabIndex={6}
                  >
                    <option value="">Selecione...</option>
                    <option value="SAS">SAS - SECRETARIA DE ASSISTENCIA SOCIAL</option>
                    <option value="SAJ">SAJ - SECRETARIA DE ASSUNTOS JURIDICOS</option>
                    <option value="SECOM">SECOM - SECRETARIA DE COMUNICACAO</option>
                    <option value="CGM">CGM - CONTROLADORIA GERAL DO MUNICIPIO</option>
                    <option value="SC">SC - SECRETARIA DE CULTURA</option>
                    <option value="SDE">SDE - SECRETARIA DE DESENVOLVIMENTO ECONOMICO</option>
                    <option value="SEL">SEL - SECRETARIA DE ESPORTE E LAZER</option>
                    <option value="SE">SE - SECRETARIA DE EDUCAÇAO</option>
                    <option value="SF">SF - SECRETARIA DE FINANÇAS</option>
                    <option value="GP">GP - GABINETE DO PREFEITO</option>
                    <option value="SG">SG - SECRETARIA DE GOVERNO</option>
                    <option value="SH">SH - SECRETARIA DE HABITAÇÃO</option>
                    <option value="SMA">SMA - SECRETARIA DE MEIO AMBIENTE</option>
                    <option value="SMU">SMU - SECRETARIA DE MOBILIDADE URBANA</option>
                    <option value="SO">SO - SECRETARIA DE OBRAS</option>
                    <option value="SPU">SPU - SECRETARIA DE PLANEJAMENTO URBANO</option>
                    <option value="SPPM">SPPM - SECRETARIA DE POLITICAS PUBLICAS PARA MULHERES</option>
                    <option value="SPDC">SPDC - SECRETARIA DE PROTECAO E DEFESA CIVIL</option>
                    <option value="SPDPD">SPDPD - SECRETARIA DE PROTECAO E DEFESA DAS PESSOAS COM DEFICIENCIA</option>
                    <option value="SRI">SRI - SECRETARIA DE RELACOES INSTITUCIONAIS</option>
                    <option value="SS">SS - SECRETARIA DE SAÚDE</option>
                    <option value="SSAN">SSAN - SECRETARIA DE SEGURANCA ALIMENTAR E NUTRICIONAL</option>
                    <option value="SSP">SSP - SECRETARIA DE SEGURANCA PUBLICA</option>
                    <option value="SSU">SSU - SECRETARIA DE SERVIÇOS URBANOS</option>
                    <option value="STRE">STRE - SECRETARIA DE TRABALHO RENDA E EMPREENDEDORISMO</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Responsável
                  </label>
                  <Input
                    placeholder="Nome do responsável"
                    value={formData.responsavel}
                    onChange={(e) => handleInputChange('responsavel', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="fonteTesouro"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="responsavel"
                    tabIndex={7}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="mr-2 h-5 w-5" />
                Fontes de Recursos *
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0001 (TESOURO)
                  </label>
                  <CurrencyInput
                    value={formData.fonteTesouro}
                    onChange={(value) => handleInputChange('fonteTesouro', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="fonteEstadual"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="fonteTesouro"
                    tabIndex={8}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0002 (ESTADUAL)
                  </label>
                  <CurrencyInput
                    value={formData.fonteEstadual}
                    onChange={(value) => handleInputChange('fonteEstadual', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="fonteFundo"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="fonteEstadual"
                    tabIndex={9}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0003 (FUNDO)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFundo}
                    onChange={(value) => handleInputChange('fonteFundo', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="fonteFederal"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="fonteFundo"
                    tabIndex={10}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0005 (FEDERAL)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFederal}
                    onChange={(value) => handleInputChange('fonteFederal', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="fonteFinisa"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="fonteFederal"
                    tabIndex={11}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0007 (FINISA)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFinisa}
                    onChange={(value) => handleInputChange('fonteFinisa', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="dataInicio"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="fonteFinisa"
                    tabIndex={12}
                  />
                </div>
              </div>

              {/* Datas de Vencimento das Fontes Não-Tesouro */}
              {(formData.fonteEstadual || formData.fonteFundo || formData.fonteFederal || formData.fonteFinisa) && (
                <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Datas de Vencimento dos Recursos Externos
                  </h4>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-4">
                    Informe as datas de vencimento para controle de risco de perda dos recursos.
                    <strong>Não é obrigatório</strong> - você pode cadastrar o processo sem essas datas.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.fonteEstadual && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Estadual
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoEstadual}
                          onChange={(e) => handleInputChange('dataVencimentoEstadual', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFundo && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Fundo
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFundo}
                          onChange={(e) => handleInputChange('dataVencimentoFundo', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFederal && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Federal
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFederal}
                          onChange={(e) => handleInputChange('dataVencimentoFederal', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFinisa && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Finisa
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFinisa}
                          onChange={(e) => handleInputChange('dataVencimentoFinisa', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Cronograma e Localização
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Data de Início do Processo *
                  </label>
                  <Input
                    type="date"
                    value={formData.dataInicio}
                    onChange={(e) => handleInputChange('dataInicio', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="dataEntradaCLMP"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="dataInicio"
                    tabIndex={13}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Data Entrada na CLMP *
                  </label>
                  <Input
                    type="date"
                    value={formData.dataEntradaCLMP}
                    onChange={(e) => handleInputChange('dataEntradaCLMP', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="prioridade"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="dataEntradaCLMP"
                    tabIndex={14}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Prioridade
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent [&>option]:bg-background [&>option]:text-foreground"
                    value={formData.prioridade}
                    onChange={(e) => handleInputChange('prioridade', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="local"]') as HTMLElement;
                        nextElement?.focus();
                      }
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        handleInputChange('prioridade', 'Normal');
                      }
                    }}
                    name="prioridade"
                    tabIndex={15}
                  >
                    <option value="Normal">Normal</option>
                    <option value="Alta">Alta</option>
                    <option value="Urgente">Urgente</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Local Atual
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent [&>option]:bg-background [&>option]:text-foreground"
                    value={formData.local}
                    onChange={(e) => handleInputChange('local', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="observacoes"]') as HTMLElement;
                        nextElement?.focus();
                      }
                      if (e.key === 'Escape') {
                        e.preventDefault();
                        handleInputChange('local', '');
                      }
                    }}
                    name="local"
                    tabIndex={16}
                  >
                    <option value="">Selecione...</option>
                    <option value="CLMP">CLMP</option>
                    <option value="SAS">SAS</option>
                    <option value="SAJ">SAJ</option>
                    <option value="SECOM">SECOM</option>
                    <option value="CGM">CGM</option>
                    <option value="SC">SC</option>
                    <option value="SDE">SDE</option>
                    <option value="SEL">SEL</option>
                    <option value="SE">SE</option>
                    <option value="SF">SF</option>
                    <option value="GP">GP</option>
                    <option value="SG">SG</option>
                    <option value="SH">SH</option>
                    <option value="SMA">SMA</option>
                    <option value="SMU">SMU</option>
                    <option value="SO">SO</option>
                    <option value="SPU">SPU</option>
                    <option value="SPPM">SPPM</option>
                    <option value="SPDC">SPDC</option>
                    <option value="SPDPD">SPDPD</option>
                    <option value="SRI">SRI</option>
                    <option value="SS">SS</option>
                    <option value="SSAN">SSAN</option>
                    <option value="SSP">SSP</option>
                    <option value="SSU">SSU</option>
                    <option value="STRE">STRE</option>
                    <option value="AUDESP">AUDESP</option>
                    <option value="FORNECEDOR">FORNECEDOR</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Observações
                </label>
                <textarea
                  className="w-full p-2 border border-input rounded-md bg-background min-h-[80px]"
                  placeholder="Observações adicionais..."
                  value={formData.observacoes}
                  onChange={(e) => handleInputChange('observacoes', e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                      e.preventDefault();
                      // Ctrl+Enter para salvar
                      if (isFormValid && !saving) {
                        handleSave();
                      }
                    }
                  }}
                  name="observacoes"
                  tabIndex={17}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar com Resumo - FIXO NA TELA */}
        <div className="w-80 sticky top-6 h-fit space-y-6">
          <Card className="shadow-lg">
            <CardHeader className="bg-primary/5">
              <CardTitle className="text-lg flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Resumo do Processo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status do Formulário</span>
                  {isFormValid ? (
                    <Badge variant="success" className="text-xs">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Válido
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">
                      <AlertCircle className="mr-1 h-3 w-3" />
                      Incompleto
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Campos obrigatórios:</span>
                    <span className="font-medium">
                      {[
                        formData.numeroProcesso,
                        formData.objeto,
                        formData.modalidade,
                        formData.secretaria,
                        formData.status,
                        temFonteRecurso,
                        formData.dataInicio,
                        formData.dataEntradaCLMP
                      ].filter(Boolean).length}/8
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Campos opcionais:</span>
                    <span className="font-medium">
                      {[
                        formData.valorEstimado,
                        formData.responsavel,
                        formData.prioridade,
                        formData.local,
                        formData.observacoes
                      ].filter(Boolean).length}/5
                    </span>
                  </div>
                </div>
              </div>

              {/* Informações do Processo */}
              {(formData.numeroProcesso || formData.modalidade || formData.secretaria) && (
                <div className="pt-4 border-t border-border space-y-2">
                  <h4 className="text-sm font-medium text-foreground">Dados do Processo:</h4>
                  {formData.numeroProcesso && (
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Número:</span>
                      <span className="font-medium">{formData.numeroProcesso}</span>
                    </div>
                  )}
                  {formData.modalidade && (
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Modalidade:</span>
                      <span className="font-medium">{formData.modalidade}</span>
                    </div>
                  )}
                  {formData.secretaria && (
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Secretaria:</span>
                      <span className="font-medium">{formData.secretaria}</span>
                    </div>
                  )}
                  {formData.status && (
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Status:</span>
                      <span className="font-medium">{formData.status}</span>
                    </div>
                  )}
                  {formData.valorEstimado && (
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Valor:</span>
                      <span className="font-medium">{formData.valorEstimado}</span>
                    </div>
                  )}
                </div>
              )}

              <div className="pt-4 border-t border-border">
                <Button
                  onClick={handleSave}
                  disabled={!isFormValid || saving}
                  className="w-full bg-primary hover:bg-primary/90 text-white"
                  size="lg"
                >
                  {saving ? (
                    <>
                      <Save className="mr-2 h-4 w-4 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Salvar Processo
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Dicas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  Preencha todos os campos obrigatórios marcados com *
                </p>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  O número do processo deve ser único no sistema
                </p>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  Confira todos os dados antes de salvar o processo
                </p>
              </div>

              {/* Botão de Salvar - Sempre Visível */}
              <div className="pt-4 border-t">
                <Button
                  onClick={handleSave}
                  disabled={!isFormValid || saving}
                  className="w-full bg-primary hover:bg-primary/90 text-white"
                  size="lg"
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Salvando Processo...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Salvar Processo
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
