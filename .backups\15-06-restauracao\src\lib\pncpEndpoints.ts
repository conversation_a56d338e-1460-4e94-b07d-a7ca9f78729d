/**
 * 🌐 ENDPOINTS E CONFIGURAÇÕES DA API PNCP
 * Documentação oficial: https://pncp.gov.br/api/consulta
 */

export const PNCP_CONFIG = {
  BASE_URL: 'https://pncp.gov.br/api/consulta/v1',
  TIMEOUT: 30000, // 30 segundos
  MAX_RETRIES: 3,
  RATE_LIMIT: 100 // requests por minuto
};

export const PNCP_ENDPOINTS = {
  // Contratos
  CONTRATOS: '/contratos',
  CONTRATO_DETALHES: '/contratos/{id}',
  
  // Licitações
  LICITACOES: '/licitacoes',
  LICITACAO_DETALHES: '/licitacoes/{id}',
  
  // Órgãos
  ORGAOS: '/orgaos',
  ORGAO_DETALHES: '/orgaos/{id}',
  
  // Fornecedores
  FORNECEDORES: '/fornecedores',
  FORNECEDOR_DETALHES: '/fornecedores/{cnpj}'
};

export interface PNCPRequestParams {
  // Parâmetros comuns
  termo?: string;
  pagina?: number;
  tamanhoPagina?: number;
  
  // Filtros de data
  dataInicio?: string;
  dataFim?: string;
  
  // Filtros específicos
  orgaoTipo?: 'municipal' | 'estadual' | 'federal';
  modalidade?: string;
  situacao?: string;
  
  // Filtros de valor
  valorMinimo?: number;
  valorMaximo?: number;
  
  // Localização
  uf?: string;
  municipio?: string;
}

/**
 * 🔧 UTILITÁRIOS PARA API PNCP
 */
export class PNCPUtils {
  
  /**
   * Construir URL com parâmetros
   */
  static buildUrl(endpoint: string, params: PNCPRequestParams = {}): string {
    const url = new URL(endpoint, PNCP_CONFIG.BASE_URL);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        url.searchParams.append(key, value.toString());
      }
    });
    
    return url.toString();
  }
  
  /**
   * Headers padrão para requisições
   */
  static getHeaders(): HeadersInit {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'User-Agent': 'InovaProcess/1.0 (Prefeitura Municipal de Mauá)',
      'Cache-Control': 'no-cache'
    };
  }
  
  /**
   * Validar resposta da API
   */
  static validateResponse(response: Response): void {
    if (!response.ok) {
      throw new Error(`PNCP API Error: ${response.status} - ${response.statusText}`);
    }
    
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error('Resposta da API PNCP não é JSON válido');
    }
  }
  
  /**
   * Retry com backoff exponencial
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = PNCP_CONFIG.MAX_RETRIES
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Backoff exponencial: 1s, 2s, 4s...
        const delay = Math.pow(2, attempt - 1) * 1000;
        console.warn(`⚠️ Tentativa ${attempt} falhou, tentando novamente em ${delay}ms:`, error);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
  
  /**
   * Rate limiting simples
   */
  private static lastRequest = 0;
  private static requestCount = 0;
  
  static async rateLimit(): Promise<void> {
    const now = Date.now();
    const oneMinute = 60 * 1000;
    
    // Reset contador a cada minuto
    if (now - this.lastRequest > oneMinute) {
      this.requestCount = 0;
      this.lastRequest = now;
    }
    
    // Verificar limite
    if (this.requestCount >= PNCP_CONFIG.RATE_LIMIT) {
      const waitTime = oneMinute - (now - this.lastRequest);
      console.warn(`⏳ Rate limit atingido, aguardando ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
    }
    
    this.requestCount++;
  }
}

/**
 * 📊 MAPEAMENTO DE CAMPOS DA API PNCP
 */
export const PNCP_FIELD_MAPPING = {
  // Contratos
  contrato: {
    id: 'numeroControlePNCP',
    numero: 'numeroContrato',
    objeto: 'objetoContrato',
    valor: 'valorTotalEstimado',
    dataAssinatura: 'dataAssinatura',
    dataVigenciaInicio: 'dataVigenciaInicio',
    dataVigenciaFim: 'dataVigenciaFim',
    fornecedor: 'razaoSocialFornecedor',
    cnpjFornecedor: 'cnpjFornecedor',
    orgao: 'nomeOrgaoEntidade',
    cnpjOrgao: 'cnpjOrgaoEntidade'
  },
  
  // Licitações
  licitacao: {
    id: 'numeroControlePNCP',
    numero: 'numeroLicitacao',
    objeto: 'objetoLicitacao',
    modalidade: 'modalidadeLicitacao',
    situacao: 'situacaoLicitacao',
    dataAbertura: 'dataAberturaProposta',
    valorEstimado: 'valorEstimadoLicitacao',
    orgao: 'nomeOrgaoEntidade'
  }
};

/**
 * 🎯 FILTROS INTELIGENTES PARA PESQUISA
 */
export class PNCPSmartFilters {
  
  /**
   * Detectar tipo de busca baseado no termo
   */
  static detectSearchType(termo: string): {
    tipo: 'produto' | 'servico' | 'obra' | 'geral';
    sugestoes: PNCPRequestParams;
  } {
    const termoLower = termo.toLowerCase();
    
    // Produtos
    if (termoLower.includes('álcool') || termoLower.includes('papel') || 
        termoLower.includes('material') || termoLower.includes('equipamento')) {
      return {
        tipo: 'produto',
        sugestoes: {
          modalidade: 'Pregão Eletrônico',
          tamanhoPagina: 50
        }
      };
    }
    
    // Serviços
    if (termoLower.includes('limpeza') || termoLower.includes('manutenção') || 
        termoLower.includes('consultoria') || termoLower.includes('serviço')) {
      return {
        tipo: 'servico',
        sugestoes: {
          modalidade: 'Pregão Eletrônico',
          tamanhoPagina: 30
        }
      };
    }
    
    // Obras
    if (termoLower.includes('construção') || termoLower.includes('reforma') || 
        termoLower.includes('pavimentação') || termoLower.includes('obra')) {
      return {
        tipo: 'obra',
        sugestoes: {
          modalidade: 'Tomada de Preços',
          tamanhoPagina: 20
        }
      };
    }
    
    return {
      tipo: 'geral',
      sugestoes: {
        tamanhoPagina: 50
      }
    };
  }
  
  /**
   * Otimizar parâmetros de busca
   */
  static optimizeParams(params: PNCPRequestParams): PNCPRequestParams {
    const optimized = { ...params };
    
    // Definir página padrão
    if (!optimized.pagina) optimized.pagina = 1;
    
    // Definir tamanho padrão
    if (!optimized.tamanhoPagina) optimized.tamanhoPagina = 50;
    
    // Limitar período se não especificado
    if (!optimized.dataInicio) {
      const umAnoAtras = new Date();
      umAnoAtras.setFullYear(umAnoAtras.getFullYear() - 1);
      optimized.dataInicio = umAnoAtras.toISOString().split('T')[0];
    }
    
    if (!optimized.dataFim) {
      optimized.dataFim = new Date().toISOString().split('T')[0];
    }
    
    return optimized;
  }
}
