/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  
  // Configuração para otimizar o carregamento de imagens
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  
  // Otimizações de performance
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },

  // 🔒 CONFIGURAÇÕES ROBUSTAS PARA RECHARTS + NEXT.JS
  webpack: (config, { dev, isServer }) => {
    // Configuração específica para Recharts evitar problemas SSR
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    if (dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 1,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
          // Separar Recharts em chunk próprio
          recharts: {
            test: /[\\/]node_modules[\\/]recharts[\\/]/,
            name: 'recharts',
            priority: 10,
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },

  // 🔒 TRANSPILAÇÃO FORÇADA PARA RECHARTS
  transpilePackages: ['recharts'],

  // DESABILITAR PAINEL DE DESENVOLVIMENTO (Remove o "N" maldito)
  devIndicators: {
    position: 'bottom-right',
  },
  
  // Configuração de cache para melhor performance
  // Desativa o cache em memória para evitar problemas em ambientes de produção
  cacheMaxMemorySize: 0,
  cacheHandler: require.resolve('./cache-handler.js'),
};

module.exports = nextConfig;



