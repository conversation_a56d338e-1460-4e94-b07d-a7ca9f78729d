import { NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET() {
  try {
    const locais = await CSVReader.getAllLocais();
    
    return NextResponse.json({
      success: true,
      locais: locais,
    });
  } catch (error) {
    console.error('Erro ao buscar locais:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
