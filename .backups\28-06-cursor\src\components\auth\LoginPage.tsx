'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, CheckCircle, Mail } from 'lucide-react';

export default function LoginPage() {
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGoogleLogin = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await login();
    } catch (error: any) {
      console.error('❌ Erro no login:', error);
      setError(error.message || 'Erro ao fazer login com Google. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold">InovaProcess</CardTitle>
          <CardDescription>
            Sistema de Gestão de Processos
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Informações de Segurança */}
          <div className="space-y-3">
            <div className="flex items-center space-x-3 text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span>Autenticação segura com Google</span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-muted-foreground">
              <Mail className="w-4 h-4 text-blue-500" />
              <span>Apenas contas Gmail válidas</span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-muted-foreground">
              <Shield className="w-4 h-4 text-purple-500" />
              <span>Máxima proteção de dados</span>
            </div>
          </div>

          {/* Erro */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Botão de Login com Google */}
          <Button
            onClick={handleGoogleLogin}
            disabled={isLoading}
            className="w-full h-12 text-base font-medium"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Entrando...
              </>
            ) : (
              <>
                <Shield className="w-5 h-5 mr-2" />
                Entrar com Google
              </>
            )}
          </Button>



          {/* Informações Adicionais */}
          <div className="text-center text-xs text-muted-foreground">
            <p>Ao fazer login, você concorda com nossos</p>
            <p>termos de uso e política de privacidade</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
