#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 TESTANDO BUILD LOCAL PARA DEPLOY...\n');

// 1. Verificar se .env.local existe
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  .env.local não encontrado - criando com configurações de teste...');
  
  const envContent = `# Configurações de teste para build
NODE_ENV=production
USE_MOCK_DATA=true
NEXT_PUBLIC_FIREBASE_API_KEY=test_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=test.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=test_project
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=test.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=test_app_id
NEXTAUTH_SECRET=test_secret_for_build_only
NEXTAUTH_URL=http://localhost:3000
`;
  
  fs.writeFileSync(envPath, envContent);
  console.log('✅ .env.local criado para teste\n');
}

// 2. Limpar cache do Next.js
console.log('🧹 Limpando cache...');
try {
  execSync('npm run clean', { stdio: 'inherit' });
} catch (error) {
  console.log('Cache já limpo ou comando não encontrado');
}

// 3. Instalar dependências
console.log('\n📦 Verificando dependências...');
try {
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependências OK');
} catch (error) {
  console.error('❌ Erro ao instalar dependências:', error.message);
  process.exit(1);
}

// 4. Executar build
console.log('\n🏗️  Executando build de produção...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('\n✅ BUILD CONCLUÍDO COM SUCESSO!');
} catch (error) {
  console.error('\n❌ ERRO NO BUILD:', error.message);
  process.exit(1);
}

// 5. Testar start
console.log('\n🚀 Testando servidor de produção...');
console.log('📝 Execute: npm start');
console.log('🌐 Acesse: http://localhost:3000');
console.log('\n🎯 Se tudo funcionar, está pronto para deploy no Vercel!');

console.log('\n📋 PRÓXIMOS PASSOS:');
console.log('1. ✅ Build local funcionando');
console.log('2. 🌐 Criar repositório no GitHub');
console.log('3. 🚀 Deploy no Vercel');
console.log('4. 🔧 Configurar variáveis de ambiente');
console.log('5. 🔐 Configurar autenticação Google');
