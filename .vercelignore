# Arquivos que o Vercel deve ignorar no deploy
# (NÃO DELETA, apenas não envia)

# Backups (manter local, não enviar)
backups/
backup-*/
checkpoints/
*.backup
*.bak

# Scripts desnecessários no deploy
# scripts/ - REMOVIDO: monitor-resets.js é necessário no prebuild

# Arquivos de desenvolvimento
*.log
*.tmp
*.temp
.cache/

# Dados CSV - INCLUIR banco-dados-consolidado.csv para produção
# data/ - REMOVIDO PARA PERMITIR DADOS REAIS
# *.csv - REMOVIDO PARA PERMITIR DADOS REAIS
# Acompanhamento*.csv - REMOVIDO PARA PERMITIR DADOS REAIS

# Node modules (já ignorado por padrão)
node_modules/

# Build artifacts
.next/
out/
build/

# Arquivos de ambiente (usar variáveis do Vercel)
.env*
!.env.example
