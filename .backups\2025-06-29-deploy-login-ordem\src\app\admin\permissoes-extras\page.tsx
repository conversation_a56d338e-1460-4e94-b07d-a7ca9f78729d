'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  CheckCircle, 
  AlertTriangle,
  Info
} from 'lucide-react';
import GerenciadorPermissoesExtras from '@/components/GerenciadorPermissoesExtras';
import { PerfilUsuario } from '@/types/permissoes';

export default function PermissoesExtrasPage() {
  const [alteracoesPendentes, setAlteracoesPendentes] = useState<Array<{
    perfil: PerfilUsuario;
    permissao: string;
    habilitado: boolean;
    timestamp: Date;
  }>>([]);
  
  const [salvando, setSalvando] = useState(false);
  const [mensagem, setMensagem] = useState<{ tipo: 'success' | 'error'; texto: string } | null>(null);

  const handlePermissaoAlterada = (perfil: PerfilUsuario, permissao: string, habilitado: boolean) => {
    setAlteracoesPendentes(prev => [
      ...prev.filter(a => !(a.perfil === perfil && a.permissao === permissao)),
      { perfil, permissao, habilitado, timestamp: new Date() }
    ]);
  };

  const handleSalvar = async () => {
    setSalvando(true);
    setMensagem(null);
    
    try {
      // Simular salvamento (aqui você faria a chamada para API)
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setAlteracoesPendentes([]);
      setMensagem({ 
        tipo: 'success', 
        texto: `${alteracoesPendentes.length} alterações salvas com sucesso!` 
      });
      
      // Limpar mensagem após 3 segundos
      setTimeout(() => setMensagem(null), 3000);
      
    } catch (error) {
      setMensagem({ 
        tipo: 'error', 
        texto: 'Erro ao salvar alterações. Tente novamente.' 
      });
    } finally {
      setSalvando(false);
    }
  };

  const handleReset = () => {
    setAlteracoesPendentes([]);
    setMensagem(null);
    // Aqui você recarregaria as configurações originais
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Configuração de Permissões Extras</h1>
          <p className="text-muted-foreground mt-2">
            Personalize as funcionalidades disponíveis para cada perfil de usuário
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {alteracoesPendentes.length > 0 && (
            <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
              {alteracoesPendentes.length} alterações pendentes
            </Badge>
          )}
          
          <Button 
            variant="outline" 
            onClick={handleReset}
            disabled={salvando || alteracoesPendentes.length === 0}
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Resetar
          </Button>
          
          <Button 
            onClick={handleSalvar}
            disabled={salvando || alteracoesPendentes.length === 0}
          >
            <Save className="mr-2 h-4 w-4" />
            {salvando ? 'Salvando...' : 'Salvar Alterações'}
          </Button>
        </div>
      </div>

      {/* Mensagens */}
      {mensagem && (
        <Alert className={mensagem.tipo === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          {mensagem.tipo === 'success' ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={mensagem.tipo === 'success' ? 'text-green-800' : 'text-red-800'}>
            {mensagem.texto}
          </AlertDescription>
        </Alert>
      )}

      {/* Informações Importantes */}
      <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium mb-2">Como funciona:</p>
              <ul className="space-y-1 text-xs">
                <li>• <strong>Pesquisadores:</strong> Carla, Sonia, Mariane, Fernando, Paulo e Gilson terão acesso a gráficos, relatórios avançados, etc.</li>
                <li>• <strong>Analistas:</strong> Podem ter OCR, aprovações, métricas detalhadas habilitadas</li>
                <li>• <strong>Secretarias:</strong> Funcionalidades como criação avançada, edição, tramitação</li>
                <li>• <strong>Estagiários:</strong> Permissões básicas que podem ser expandidas gradualmente</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alterações Pendentes */}
      {alteracoesPendentes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Alterações Pendentes</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {alteracoesPendentes.map((alteracao, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className="text-xs">
                      {alteracao.perfil}
                    </Badge>
                    <span className="text-sm font-medium">
                      {alteracao.permissao}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={alteracao.habilitado ? 'default' : 'secondary'}>
                      {alteracao.habilitado ? 'Habilitar' : 'Desabilitar'}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {alteracao.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Gerenciador Principal */}
      <GerenciadorPermissoesExtras onPermissaoAlterada={handlePermissaoAlterada} />

      {/* Exemplos de Uso */}
      <Card>
        <CardHeader>
          <CardTitle>💡 Exemplos de Uso</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-foreground mb-2">🟡 Para Pesquisadores:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Habilite "Gráficos de Preços" para Carla e Paulo</li>
                <li>• "Relatórios Avançados" para Sonia e Fernando</li>
                <li>• "Análise Comparativa" para todos</li>
                <li>• "Dashboard Personalizado" conforme necessidade</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-foreground mb-2">🟢 Para Analistas:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• "OCR de Documentos" para análises automáticas</li>
                <li>• "Aprovar Análises" para seniores</li>
                <li>• "Métricas Detalhadas" para supervisores</li>
                <li>• "Alterar Prioridades" conforme hierarquia</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
