#!/usr/bin/env node

/**
 * 🔒 SCRIPT DE VERIFICAÇÃO DE SEGURANÇA
 * Verifica se as configurações do Firebase estão corretas e seguras
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 VERIFICANDO CONFIGURAÇÕES DE SEGURANÇA...\n');

// Verificar se existe .env.local
const envLocalPath = path.join(__dirname, '..', '.env.local');
const hasEnvLocal = fs.existsSync(envLocalPath);

console.log('📁 Arquivo .env.local:', hasEnvLocal ? '✅ Encontrado' : '❌ NÃO ENCONTRADO');

if (hasEnvLocal) {
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  const projectId = envContent.match(/NEXT_PUBLIC_FIREBASE_PROJECT_ID=([^\n]+)/)?.[1];
  
  console.log('🏗️  Projeto Firebase:', projectId || '❌ Não encontrado');
  
  if (projectId === 'inovaprocess-prod') {
    console.log('✅ Usando projeto de PRODUÇÃO (correto)');
  } else if (projectId === 'inovaprocess-novo') {
    console.log('⚠️  ATENÇÃO: Usando projeto de DESENVOLVIMENTO');
    console.log('   Isso pode causar problemas de dados separados!');
  } else {
    console.log('❓ Projeto desconhecido:', projectId);
  }
} else {
  console.log('⚠️  CRIE o arquivo .env.local com as credenciais de produção');
  console.log('   Veja o arquivo config-firebase-seguro.md para instruções');
}

// Verificar regras do Firestore
const rulesPath = path.join(__dirname, '..', 'firestore.rules');
const hasRules = fs.existsSync(rulesPath);

console.log('\n📋 Regras do Firestore:', hasRules ? '✅ Encontradas' : '❌ NÃO ENCONTRADAS');

if (hasRules) {
  const rulesContent = fs.readFileSync(rulesPath, 'utf8');
  
  // Verificar se permite criação do primeiro usuário
  const allowsFirstUser = rulesContent.includes('allow create: if request.auth != null');
  console.log('👤 Permite primeiro usuário:', allowsFirstUser ? '✅ Sim' : '❌ Não');
  
  // Verificar se tem controle de admin
  const hasAdminControl = rulesContent.includes('isAdmin()');
  console.log('🔐 Controle de admin:', hasAdminControl ? '✅ Sim' : '❌ Não');
}

// Verificar se existe backup
const backupPath = path.join(__dirname, '..', 'backup-sistema-aprovacao-20250115');
const hasBackup = fs.existsSync(backupPath);

console.log('\n💾 Backup do sistema:', hasBackup ? '✅ Criado' : '❌ NÃO CRIADO');

// Resumo final
console.log('\n' + '='.repeat(50));
console.log('📊 RESUMO DA VERIFICAÇÃO:');

if (hasEnvLocal && hasRules && hasBackup) {
  console.log('✅ SISTEMA CONFIGURADO COM SEGURANÇA');
  console.log('   Você pode fazer o primeiro acesso com segurança');
} else {
  console.log('⚠️  CONFIGURAÇÕES INCOMPLETAS');
  console.log('   Complete as configurações antes do primeiro acesso');
}

console.log('\n📋 PRÓXIMOS PASSOS:');
console.log('1. Configure o arquivo .env.local (se não existir)');
console.log('2. Adicione "localhost" aos domínios autorizados no Firebase Console');
console.log('3. Faça o primeiro acesso com seu email principal');
console.log('4. Acesse /admin/usuarios para gerenciar outros usuários');

console.log('\n🔗 Links úteis:');
console.log('- Firebase Console: https://console.firebase.google.com/project/inovaprocess-prod');
console.log('- Configuração segura: config-firebase-seguro.md'); 