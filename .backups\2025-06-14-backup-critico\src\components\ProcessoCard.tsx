'use client';

import Link from 'next/link';
import { Processo } from '@/types/processo';
import { Calendar, Building, User, FileText, DollarSign } from 'lucide-react';
import AlertaPrioridade from '@/components/AlertaPrioridade';
import { classificarStatus } from '@/lib/processoUtils';

interface ProcessoCardProps {
  processo: Processo;
}

export default function ProcessoCard({ processo }: ProcessoCardProps) {
  // Usa a classificação estratégica baseada nas regras de negócio da CLMP
  const classificacao = classificarStatus(processo);

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = () => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  // Função para calcular tempo total do processo
  const calcularTempoTotalProcesso = () => {
    if (!processo['DATA DE INÍCIO DO PROCESSO']) return null;

    try {
      const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
      const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      // Se processo finalizado, usar data de finalização
      const isProcessoFinalizado = processo.STATUS?.toLowerCase().includes('finalizado');
      let dataFim = new Date();

      if (isProcessoFinalizado && processo.DATA) {
        const [diaFim, mesFim, anoFim] = processo.DATA.split('/');
        dataFim = new Date(parseInt(anoFim), parseInt(mesFim) - 1, parseInt(diaFim));
      }

      const diffMs = dataFim.getTime() - dataInicio.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
    } catch (error) {
      return null;
    }
  };

  const tempoNoSetor = calcularTempoNoSetor();
  const tempoTotalProcesso = calcularTempoTotalProcesso();

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    
    // Remove caracteres não numéricos exceto vírgula e ponto
    const cleanValue = value.replace(/[^\d,.-]/g, '');
    
    if (!cleanValue) return 'Não informado';
    
    return value; // Retorna o valor original formatado do CSV
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    
    // Se já está no formato brasileiro, retorna como está
    if (dateString.includes('/')) {
      return dateString;
    }
    
    return dateString;
  };

  // Criar URL segura para o processo
  const processoUrl = `/processos/${encodeURIComponent(processo.PROCESSO || '')}`;

  return (
    <div className="group relative">
      {/* Card Compacto - Linha Grossa */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-blue-500/20 group-hover:border-l-blue-500 h-16 flex items-center">
        <div className="p-3 flex-1 flex items-center justify-between">
          <div className="flex-1 min-w-0 flex items-center space-x-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100 truncate">
                {processo.PROCESSO || 'Sem número'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                {processo.OBJETO || 'Objeto não informado'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2 ml-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${classificacao.cor} truncate max-w-32`}>
              <span className="hidden sm:inline">{classificacao.icone} </span>
              <span className="truncate">{processo.STATUS?.substring(0, 12)}...</span>
            </span>
            <AlertaPrioridade processo={processo} compact={true} />
          </div>
        </div>
      </div>

      {/* Banner Detalhado no Hover */}
      <div className="absolute top-0 left-0 w-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-50 pointer-events-none group-hover:pointer-events-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl border-2 border-blue-500/20 backdrop-blur-sm">
          <div className="p-4 sm:p-6">
            {/* Header do card detalhado */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 gap-2">
              <div className="flex-1 min-w-0">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                  {processo.PROCESSO || 'Sem número'}
                </h3>
              </div>

              {processo.STATUS && (
                <div className="flex items-center">
                  <span className={`px-2 sm:px-3 py-1 rounded-full text-xs font-medium border ${classificacao.cor}`}>
                    <span className="hidden sm:inline">{classificacao.icone} </span>
                    <span>{processo.STATUS}</span>
                  </span>
                </div>
              )}
            </div>

            {/* Alertas de Prioridade */}
            <div className="mb-3">
              <AlertaPrioridade processo={processo} compact={false} />
            </div>

            {/* Objeto do processo */}
            <div className="mb-4">
              <p className="text-gray-800 dark:text-gray-200 text-sm leading-relaxed">
                {processo.OBJETO || 'Objeto não informado'}
              </p>
            </div>

            {/* Informações principais */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Building size={16} className="mr-2 text-gray-400" />
                <span className="truncate">
                  {processo.REQUISITANTE || 'Não informado'}
                </span>
              </div>

              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <User size={16} className="mr-2 text-gray-400" />
                <span className="truncate">
                  {processo.RESPONSÁVEL || 'Não informado'}
                </span>
              </div>

              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <FileText size={16} className="mr-2 text-gray-400" />
                <span className="truncate">
                  {processo.MODALIDADE || 'Não informado'}
                </span>
              </div>

              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Calendar size={16} className="mr-2 text-gray-400" />
                <span className="truncate">
                  {formatDate(processo['DATA ENTRADA NA CLMP'] || '')}
                </span>
              </div>
            </div>

            {/* Checkbox Prioridade Governo */}
            <div className="flex items-center justify-end mb-3">
              <div className={`flex items-center p-2 rounded-md transition-colors ${
                processo.prioridadeGoverno
                  ? 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}>
                <input
                  type="checkbox"
                  id={`prioridade-${processo.ITEM}`}
                  checked={processo.prioridadeGoverno || false}
                  onChange={async (e) => {
                    const isChecked = e.target.checked;

                    // Log da ação
                    console.log('Prioridade Governo alterada:', {
                      processo: processo.PROCESSO,
                      item: processo.ITEM,
                      prioridade: isChecked,
                      usuario: 'Marcos Isidoro', // TODO: Pegar do contexto
                      timestamp: new Date().toISOString()
                    });

                    // Atualizar processo via API
                    try {
                      const processoId = encodeURIComponent(processo.PROCESSO || processo.ITEM);
                      const response = await fetch(`/api/processos/${processoId}`, {
                        method: 'PATCH',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          prioridadeGoverno: isChecked,
                          prioridade: isChecked ? 'ALTA' : processo.PRIORIDADE
                        })
                      });

                      const result = await response.json();

                      if (!response.ok || !result.success) {
                        console.error('Erro ao atualizar prioridade:', result.error);
                        // Reverter checkbox se erro
                        e.target.checked = !isChecked;
                        alert('Erro ao atualizar prioridade do processo');
                      } else {
                        console.log('Prioridade atualizada com sucesso');
                        // Atualizar visualmente sem recarregar
                        processo.prioridadeGoverno = isChecked;
                        if (isChecked) {
                          processo.PRIORIDADE = 'ALTA';
                        }
                      }
                    } catch (error) {
                      console.error('Erro ao atualizar prioridade:', error);
                      // Reverter checkbox se erro
                      e.target.checked = !isChecked;
                      alert('Erro de conexão ao atualizar prioridade');
                    }
                  }}
                  className="mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
                <label
                  htmlFor={`prioridade-${processo.ITEM}`}
                  className={`text-xs font-medium cursor-pointer ${
                    processo.prioridadeGoverno
                      ? 'text-red-700 dark:text-red-300'
                      : 'text-red-600 dark:text-red-400'
                  }`}
                >
                  Prioridade Governo
                </label>
              </div>
            </div>

            {/* Tempo no setor atual */}
            {tempoNoSetor && (
              <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center">
                    <Calendar size={16} className="mr-2 text-yellow-600 dark:text-yellow-400" />
                    <span className="text-yellow-700 dark:text-yellow-300 mr-2">Tempo no setor:</span>
                    <span className="font-medium text-yellow-800 dark:text-yellow-200">
                      {tempoNoSetor}
                    </span>
                    <span className="text-xs text-yellow-600 dark:text-yellow-400 ml-2">
                      ({processo.LOCAL || 'Local não informado'})
                    </span>
                  </div>
                  {tempoTotalProcesso && (
                    <div className="flex items-center">
                      <span className="text-yellow-700 dark:text-yellow-300 mr-2">Processo aberto há:</span>
                      <span className="font-medium text-yellow-800 dark:text-yellow-200">
                        {tempoTotalProcesso}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Grid de informações - continuação */}
            <div className="grid grid-cols-1 gap-3 mb-4">
            </div>

            {/* Valor estimado */}
            {processo['VALOR ESTIMADO'] && processo['VALOR ESTIMADO'] !== '-' && (
              <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div className="flex items-center text-sm">
                  <DollarSign size={16} className="mr-2 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400 mr-2">Valor estimado:</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatCurrency(processo['VALOR ESTIMADO'])}
                  </span>
                </div>
              </div>
            )}

            {/* Footer do card */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 pt-4 border-t border-gray-100 dark:border-gray-600">
              <div className="text-xs text-gray-500 dark:text-gray-400 order-2 sm:order-1">
                {processo.DATA && (
                  <span className="truncate">Atualizado em {formatDate(processo.DATA)}</span>
                )}
              </div>

              <Link
                href={processoUrl}
                className="inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors duration-200 order-1 sm:order-2 w-full sm:w-auto"
              >
                Ver detalhes
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
