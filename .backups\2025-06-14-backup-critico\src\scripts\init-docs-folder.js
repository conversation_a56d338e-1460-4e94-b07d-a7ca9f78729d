const fs = require('fs');
const path = require('path');

// Caminho para a pasta docs
const docsFolder = path.join(process.cwd(), 'docs');
const subFolders = ['readme', 'roadmap', 'historico', 'apresentacoes', 'emails'];

// Verifica se a pasta docs existe, se não, cria silenciosamente
if (!fs.existsSync(docsFolder)) {
  fs.mkdirSync(docsFolder);
  console.log('Pasta docs criada.');
}

// Cria as subpastas silenciosamente
let folderCreated = false;
subFolders.forEach(folder => {
  const subFolderPath = path.join(docsFolder, folder);
  if (!fs.existsSync(subFolderPath)) {
    fs.mkdirSync(subFolderPath);
    folderCreated = true;
  }
});

if (folderCreated) {
  console.log('Subpastas de documentação criadas.');
}

// Cria um arquivo README.md na pasta docs silenciosamente
const readmePath = path.join(docsFolder, 'README.md');
if (!fs.existsSync(readmePath)) {
  const readmeContent = `# Documentação Antiga do Projeto

Esta pasta contém documentação histórica do projeto para referência:

- **readme**: Documentação antiga do projeto
- **roadmap**: Planejamento e roadmap do projeto
- **historico**: Histórico de desenvolvimento e mudanças
- **apresentacoes**: Apresentações e slides sobre o projeto
- **emails**: Comunicações importantes sobre requisitos

Para adicionar documentos, basta colocá-los na pasta apropriada.
`;

  fs.writeFileSync(readmePath, readmeContent);
  console.log('Arquivo README.md criado na pasta docs.');
}


