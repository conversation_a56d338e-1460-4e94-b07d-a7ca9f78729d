import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { Processo } from '@/types/processo';

export class CSVReader {
  private static cachedProcessos: Processo[] = [];

  private static dataPaths = [
    path.join(process.cwd(), 'data', 'backup-abril-v1'),
    path.join(process.cwd(), 'data', 'databackup-29 e 30-04'),
    path.join(process.cwd(), 'data', 'databackup-maio-v1'),
    path.join(process.cwd(), 'data', 'databackup-junho-v1')
  ];

  static async getAllProcessos(): Promise<Processo[]> {
    if (this.cachedProcessos.length > 0) {
      return this.cachedProcessos;
    }

    try {
      const filePath = path.join(process.cwd(), 'data', 'banco-dados-consolidado.csv');
      const fileContent = fs.readFileSync(filePath, 'utf-8');

      const records = parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      const processos = records
        .filter((record: any) => record.PROCESSO && record.PROCESSO.trim())
        .map((record: any) => ({
          ...record,
          ITEM: parseInt(record.ITEM) || 0,
        }));

      this.cachedProcessos = processos;
      return processos;
    } catch (error) {
      console.error('Erro ao ler banco consolidado:', error);
      return [];
    }
  }

  /**
   * Filtra arquivos por período específico
   */
  private static filtrarArquivosPorPeriodo(csvFiles: string[], dataPath: string): string[] {
    const dirName = path.basename(dataPath);

    if (dirName === 'backup-abril-v1') {
      // Para abril, usar apenas arquivos do final (estrutura estável)
      return csvFiles.filter(file => {
        const match = file.match(/(\d{2})-(\d{2})/);
        if (match) {
          const dia = parseInt(match[1]);
          const mes = parseInt(match[2]);

          // Usar apenas arquivos de abril após dia 20
          if (mes === 4 && dia >= 20) {
            console.log(`✅ Usando arquivo abril estável: ${file}`);
            return true;
          }
        }
        return false;
      });
    } else {
      // Para outros períodos, usar todos os arquivos processados
      const arquivosProcessados = csvFiles.filter(file => file.includes('processado'));
      console.log(`✅ Usando ${arquivosProcessados.length} arquivos de ${dirName}`);
      return arquivosProcessados;
    }
  }

  /**
   * Identifica o período do arquivo
   */
  private static identificarPeriodo(dataPath: string, fileName: string): string {
    const dirName = path.basename(dataPath);

    if (dirName === 'backup-abril-v1') return 'Abril 2025';
    if (dirName === 'databackup-29 e 30-04') return 'Final Abril 2025';
    if (dirName === 'databackup-maio-v1') return 'Maio 2025';
    if (dirName === 'databackup-junho-v1') return 'Junho 2025';

    return 'Período Desconhecido';
  }

  /**
   * Lê um arquivo CSV específico
   */
  private static async readCSVFile(filePath: string): Promise<Processo[]> {
    try {
      const fileContent = fs.readFileSync(filePath, 'utf-8');

      // Primeira tentativa com configuração padrão
      try {
        const records = parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
          quote: '"',
          delimiter: ',',
          relax_column_count: true,
          skip_records_with_error: true,
        });

        console.log(`📄 Lendo arquivo: ${path.basename(filePath)}`);
        console.log(`📊 Registros encontrados: ${records.length}`);

        // Debug: verificar headers e primeiros registros
        if (records.length > 0) {
          const headers = Object.keys(records[0]);
          console.log(`📋 Headers encontrados (últimas 4): ${headers.slice(-4)}`);

          const firstRecord = records[0];
          console.log(`📄 Primeiro registro (últimas 4 colunas):`, {
            STATUS: firstRecord['STATUS'],
            DATA: firstRecord['DATA'],
            LOCAL: firstRecord['LOCAL'],
            RESPONSÁVEL: firstRecord['RESPONSÁVEL']
          });
        }

        return records.map((record: any) => {
          const processo = this.normalizeProcesso(record);
          processo._arquivo = path.basename(filePath); // Adicionar rastreamento do arquivo
          return processo;
        });
      } catch (parseError) {
        console.warn(`Erro no parsing padrão do arquivo ${filePath}, tentando método alternativo:`, parseError);

        // Método alternativo: processar linha por linha
        const lines = fileContent.split('\n').filter(line => line.trim());
        if (lines.length < 2) return [];

        // Extrair cabeçalho
        const headerLine = lines[0];
        const headers = this.parseCSVLine(headerLine);

        const records: any[] = [];

        // Processar cada linha de dados
        for (let i = 1; i < lines.length; i++) {
          try {
            const dataLine = lines[i];
            const values = this.parseCSVLine(dataLine);

            // Criar objeto com os dados disponíveis
            const record: any = {};
            headers.forEach((header, index) => {
              record[header] = values[index] || '';
            });

            records.push(this.normalizeProcesso(record));
          } catch (lineError) {
            console.warn(`Erro na linha ${i + 1} do arquivo ${filePath}:`, lineError);
            // Continua processando as outras linhas
          }
        }

        return records;
      }
    } catch (error) {
      console.error(`Erro ao ler arquivo ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Parse manual de linha CSV
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Aspas duplas escapadas
          current += '"';
          i++; // Pula a próxima aspa
        } else {
          // Alterna estado das aspas
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        // Fim do campo
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    // Adiciona o último campo
    result.push(current.trim());

    return result;
  }

  /**
   * Corrige a numeração dos itens (erro conhecido: 90 → 100)
   */
  private static corrigirNumeracaoItem(item: string): string {
    const itemNum = parseInt(item);

    // Se o item for >= 100, subtrair 9 (pois faltam os números 91-99)
    if (itemNum >= 100) {
      const itemCorrigido = itemNum - 9;
      console.log(`🔧 Corrigindo ITEM: ${item} → ${itemCorrigido}`);
      return itemCorrigido.toString();
    }

    return item;
  }

  /**
   * Normaliza os dados do processo
   */
  private static normalizeProcesso(record: any): Processo {
    // Remove espaços extras das chaves e valores
    const normalized: any = {};

    Object.keys(record).forEach(key => {
      const cleanKey = key.trim();
      const cleanValue = typeof record[key] === 'string' ? record[key].trim() : record[key];
      normalized[cleanKey] = cleanValue;
    });

    // Corrigir numeração do ITEM
    const itemOriginal = normalized['ITEM'] || '';
    const itemCorrigido = itemOriginal ? this.corrigirNumeracaoItem(itemOriginal) : '';

    return {
      ITEM: itemCorrigido,
      PROCESSO: normalized['PROCESSO'] || '',
      REQUISITANTE: normalized['REQUISITANTE'] || '',
      OBJETO: normalized['OBJETO'] || '',
      MODALIDADE: normalized['MODALIDADE'] || '',
      PRIORIDADE: normalized['PRIORIDADE'] || '',
      'DATA DE INÍCIO DO PROCESSO': normalized['DATA DE INÍCIO DO PROCESSO'] || '',
      'DATA ENTRADA NA CLMP': normalized['DATA ENTRADA NA CLMP'] || normalized['DATA DE ENTRADA NA CLMP'] || '',
      'VALOR ESTIMADO': normalized['VALOR ESTIMADO'] || '',
      'Fonte 0001 (TESOURO)': normalized['Fonte 0001 (TESOURO)'] || '',
      'Fonte 0002 (ESTADUAL)': normalized['Fonte 0002 (ESTADUAL)'] || '',
      'Fonte 0003 (FUNDO)': normalized['Fonte 0003 (FUNDO)'] || '',
      'Fonte 0005 (FEDERAL)': normalized['Fonte 0005 (FEDERAL)'] || '',
      'Fonte 0007 (FINISA)': normalized['Fonte 0007 (FINISA)'] || '',
      'Nº DO CERTAME': normalized['Nº DO CERTAME'] || '',
      'DATA PUBLICAÇÃO': normalized['DATA PUBLICAÇÃO'] || '',
      'DATA ABERTURA': normalized['DATA ABERTURA'] || normalized['DATA DE ABERTURA'] || '',
      'VALOR CONTRATADO': normalized['VALOR CONTRATADO'] || '',
      'CONTRATO NÚMERO': normalized['CONTRATO NÚMERO'] || '',
      VENCIMENTO: normalized['VENCIMENTO'] || '',
      'PROCESSO DE GERENCIAMENTO': normalized['PROCESSO DE GERENCIAMENTO'] || '',
      STATUS: normalized['STATUS'] || '',
      DATA: normalized['DATA'] || '',
      LOCAL: normalized['LOCAL'] || '',
      RESPONSÁVEL: normalized['RESPONSÁVEL'] || '',
    };
  }

  /**
   * Remove processos duplicados baseado no número do processo (mantém o mais recente)
   */
  private static removeDuplicatesKeepLatest(processos: Processo[]): Processo[] {
    const processMap = new Map();

    // Ordem de prioridade dos períodos (mais recente primeiro)
    const periodoPrioridade = {
      'Junho 2025': 4,
      'Maio 2025': 3,
      'Final Abril 2025': 2,
      'Abril 2025': 1
    };

    processos.forEach(processo => {
      const key = processo.PROCESSO;
      const periodoAtual = processo._periodo || 'Abril 2025';
      const prioridadeAtual = periodoPrioridade[periodoAtual] || 0;

      if (!processMap.has(key)) {
        processMap.set(key, processo);
      } else {
        const processoExistente = processMap.get(key);
        const periodoExistente = processoExistente._periodo || 'Abril 2025';
        const prioridadeExistente = periodoPrioridade[periodoExistente] || 0;

        // Manter o processo mais recente
        if (prioridadeAtual > prioridadeExistente) {
          console.log(`🔄 Atualizando processo ${key}: ${periodoExistente} → ${periodoAtual}`);
          processMap.set(key, processo);
        }
      }
    });

    return Array.from(processMap.values());
  }

  /**
   * Remove processos duplicados baseado no número do processo (método antigo)
   */
  private static removeDuplicates(processos: Processo[]): Processo[] {
    const seen = new Set();
    return processos.filter(processo => {
      const key = processo.PROCESSO;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Busca processos por filtros
   */
  static async searchProcessos(filters: {
    search?: string;
    status?: string;
    modalidade?: string;
    responsavel?: string;
    requisitante?: string;
  }): Promise<Processo[]> {
    const allProcessos = await this.getAllProcessos();
    
    return allProcessos.filter(processo => {
      // Filtro de busca geral - BUSCA INTELIGENTE EM TODOS OS CAMPOS
      if (filters.search && filters.search.trim()) {
        const searchTerm = filters.search.toLowerCase().trim();

        // Campos pesquisáveis
        const searchableFields = [
          processo.PROCESSO,
          processo.OBJETO,
          processo.REQUISITANTE,
          processo.RESPONSÁVEL,
          processo.STATUS,
          processo.MODALIDADE,
          processo.LOCAL,
          processo['VALOR ESTIMADO'],
          processo['DATA ENTRADA NA CLMP'],
          processo['DATA SAÍDA DA CLMP'],
          processo['FONTE DE RECURSOS'],
          processo.ITEM,
          processo.DATA,
          processo.OBSERVAÇÕES,
          processo['Nº DO CERTAME']
        ].filter(Boolean);

        // Busca inteligente: dividir o termo em palavras e buscar cada uma
        const searchWords = searchTerm.split(/\s+/).filter(word => word.length > 0);

        // Para cada palavra, verificar se existe em algum campo
        const matchesAllWords = searchWords.every(word => {
          return searchableFields.some(field => {
            const fieldValue = String(field || '').toLowerCase();

            // Busca exata para siglas (ex: SSDAN, SEMAM)
            if (word.length <= 6 && word === word.toUpperCase()) {
              return fieldValue.includes(word.toLowerCase()) ||
                     fieldValue.includes(word.toUpperCase());
            }

            // Busca parcial para palavras normais
            return fieldValue.includes(word);
          });
        });

        // Busca especial para secretarias (siglas comuns)
        const secretariaMatch = this.matchSecretaria(searchTerm, processo.REQUISITANTE || '');

        if (!matchesAllWords && !secretariaMatch) {
          return false;
        }
      }

      // Filtro por status
      if (filters.status && processo.STATUS !== filters.status) {
        return false;
      }

      // Filtro por modalidade
      if (filters.modalidade && processo.MODALIDADE !== filters.modalidade) {
        return false;
      }

      // Filtro por responsável
      if (filters.responsavel && processo.RESPONSÁVEL !== filters.responsavel) {
        return false;
      }

      // Filtro por requisitante
      if (filters.requisitante && processo.REQUISITANTE !== filters.requisitante) {
        return false;
      }

      return true;
    });
  }

  /**
   * Obtém estatísticas dos processos
   */
  static async getEstatisticas() {
    console.log('🚀 INICIANDO getEstatisticas()');
    const processos = await this.getAllProcessos();

    console.log('🔍 ANÁLISE DOS DADOS REAIS:');
    console.log(`📊 Total de processos: ${processos.length}`);
    console.log('📊 Processos é array?', Array.isArray(processos));
    console.log('📊 Primeiro processo:', processos[0]);

    // Vamos ver todos os status únicos
    const statusUnicos = [...new Set(processos.map(p => p.STATUS).filter(Boolean))];
    console.log('📋 Status únicos encontrados:', statusUnicos);

    // Vamos ver alguns exemplos de processos
    console.log('📄 Primeiros 3 processos:', processos.slice(0, 3).map(p => ({
      processo: p.PROCESSO,
      status: p.STATUS,
      data: p.DATA,
      local: p.LOCAL,
      modalidade: p.MODALIDADE,
      requisitante: p.REQUISITANTE,
      responsavel: p.RESPONSÁVEL
    })));

    // Vamos verificar se há processos com STATUS = data
    const processosComStatusData = processos.filter(p => /^\d{2}\/\d{2}\/\d{2,4}$/.test(p.STATUS));
    console.log(`🚨 PROCESSOS COM STATUS = DATA: ${processosComStatusData.length}`);
    if (processosComStatusData.length > 0) {
      console.log('📋 Exemplos de processos com STATUS incorreto:', processosComStatusData.slice(0, 3).map(p => ({
        processo: p.PROCESSO,
        status: p.STATUS,
        data: p.DATA,
        local: p.LOCAL,
        responsavel: p.RESPONSÁVEL
      })));
    }

    const stats = {
      total: processos.length,
      porStatus: {} as Record<string, number>,
      porModalidade: {} as Record<string, number>,
      porSecretaria: {} as Record<string, number>,
      porResponsavel: {} as Record<string, number>,
    };

    processos.forEach((processo, index) => {
      // Debug dos primeiros 3 processos
      if (index < 3) {
        console.log(`📋 Processo ${index + 1}:`, {
          STATUS: processo.STATUS,
          MODALIDADE: processo.MODALIDADE,
          REQUISITANTE: processo.REQUISITANTE,
          RESPONSÁVEL: processo.RESPONSÁVEL
        });
      }

      // Contagem por status
      const status = processo.STATUS || 'Sem status';
      stats.porStatus[status] = (stats.porStatus[status] || 0) + 1;

      // Contagem por modalidade
      const modalidade = processo.MODALIDADE || 'Sem modalidade';
      stats.porModalidade[modalidade] = (stats.porModalidade[modalidade] || 0) + 1;

      // Contagem por secretaria (requisitante)
      const secretaria = processo.REQUISITANTE || 'Sem requisitante';
      stats.porSecretaria[secretaria] = (stats.porSecretaria[secretaria] || 0) + 1;

      // Contagem por responsável
      const responsavel = processo.RESPONSÁVEL || 'Sem responsável';
      stats.porResponsavel[responsavel] = (stats.porResponsavel[responsavel] || 0) + 1;
    });

    console.log('📊 Stats calculadas:', {
      total: stats.total,
      porStatus: stats.porStatus,
      porModalidade: stats.porModalidade,
      porSecretaria: stats.porSecretaria,
      porResponsavel: stats.porResponsavel
    });

    console.log('🎯 RETORNANDO STATS:', stats);
    console.log('🎯 Stats é null?', stats === null);
    console.log('🎯 Stats é undefined?', stats === undefined);
    console.log('🎯 Stats tem total?', stats.total);

    return stats;
  }

  /**
   * Busca inteligente para secretarias (siglas e nomes)
   */
  private static matchSecretaria(searchTerm: string, requisitante: string): boolean {
    const term = searchTerm.toLowerCase();
    const req = requisitante.toLowerCase();

    // Mapeamento de siglas comuns para secretarias
    const secretariaMap: { [key: string]: string[] } = {
      'ssdan': ['secretaria de segurança, defesa e ações de natureza', 'ssdan'],
      'semam': ['secretaria de meio ambiente', 'semam'],
      'seplan': ['secretaria de planejamento', 'seplan'],
      'semob': ['secretaria de mobilidade', 'semob'],
      'sesau': ['secretaria de saúde', 'sesau'],
      'semed': ['secretaria de educação', 'semed'],
      'semas': ['secretaria de assistência social', 'semas'],
      'secom': ['secretaria de comunicação', 'secom'],
      'segov': ['secretaria de governo', 'segov'],
      'sefin': ['secretaria de finanças', 'sefin'],
      'seadm': ['secretaria de administração', 'seadm'],
      'sejuv': ['secretaria de juventude', 'sejuv'],
      'seturismo': ['secretaria de turismo', 'seturismo'],
      'seesporte': ['secretaria de esporte', 'seesporte'],
      'secultura': ['secretaria de cultura', 'secultura']
    };

    // Verificar se o termo corresponde a alguma sigla ou nome
    for (const [sigla, nomes] of Object.entries(secretariaMap)) {
      if (term === sigla || nomes.some(nome => req.includes(nome) && term.includes(sigla))) {
        return true;
      }
    }

    return false;
  }

  /**
   * Busca um processo específico pelo número
   */
  static async getProcessoById(numeroProcesso: string): Promise<Processo | null> {
    const processos = await this.getAllProcessos();
    return processos.find(p => p.PROCESSO === numeroProcesso) || null;
  }

  /**
   * Obtém listas únicas para filtros
   */
  static async getFilterOptions() {
    const processos = await this.getAllProcessos();

    // Extrair fontes de recursos únicas baseadas nas colunas reais
    const fontesSet = new Set<string>();
    processos.forEach(processo => {
      if (processo['Fonte 0001 (TESOURO)'] && processo['Fonte 0001 (TESOURO)'] !== '-' && processo['Fonte 0001 (TESOURO)'] !== 'R$ 0,00') {
        fontesSet.add('Fonte 0001 (TESOURO)');
      }
      if (processo['Fonte 0002 (ESTADUAL)'] && processo['Fonte 0002 (ESTADUAL)'] !== '-' && processo['Fonte 0002 (ESTADUAL)'] !== 'R$ 0,00') {
        fontesSet.add('Fonte 0002 (ESTADUAL)');
      }
      if (processo['Fonte 0003 (FUNDO)'] && processo['Fonte 0003 (FUNDO)'] !== '-' && processo['Fonte 0003 (FUNDO)'] !== 'R$ 0,00') {
        fontesSet.add('Fonte 0003 (FUNDO)');
      }
      if (processo['Fonte 0005 (FEDERAL)'] && processo['Fonte 0005 (FEDERAL)'] !== '-' && processo['Fonte 0005 (FEDERAL)'] !== 'R$ 0,00') {
        fontesSet.add('Fonte 0005 (FEDERAL)');
      }
      if (processo['Fonte 0007 (FINISA)'] && processo['Fonte 0007 (FINISA)'] !== '-' && processo['Fonte 0007 (FINISA)'] !== 'R$ 0,00') {
        fontesSet.add('Fonte 0007 (FINISA)');
      }
    });

    const options = {
      status: [...new Set(processos.map(p => p.STATUS).filter(Boolean))],
      modalidades: [...new Set(processos.map(p => p.MODALIDADE).filter(Boolean))],
      responsaveis: [...new Set(processos.map(p => p.RESPONSÁVEL).filter(Boolean))],
      requisitantes: [...new Set(processos.map(p => p.REQUISITANTE).filter(Boolean))],
      fontes: Array.from(fontesSet).sort(),
    };

    return options;
  }


}
