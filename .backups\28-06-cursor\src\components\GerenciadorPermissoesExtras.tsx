'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Users, 
  BarChart3, 
  FileText, 
  Download, 
  History, 
  Zap,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { PerfilUsuario } from '@/types/permissoes';
import { 
  PERMISSOES_EXTRAS_POR_PERFIL, 
  CATEGORIAS_PERMISSOES,
  habilitarPermissaoExtra,
  desabilitarPermissaoExtra,
  obterEstatisticasPermissoes
} from '@/types/permissoes-extras';

interface GerenciadorPermissoesExtrasProps {
  onPermissaoAlterada?: (perfil: PerfilUsuario, permissao: string, habilitado: boolean) => void;
}

export default function GerenciadorPermissoesExtras({ onPermissaoAlterada }: GerenciadorPermissoesExtrasProps) {
  const [perfilSelecionado, setPerfilSelecionado] = useState<PerfilUsuario>('pesquisador');
  const [categoriaSelecionada, setCategoriaSelecionada] = useState<string>('todas');

  const estatisticas = obterEstatisticasPermissoes();

  const handleTogglePermissao = (chavePermissao: string, habilitado: boolean) => {
    if (habilitado) {
      habilitarPermissaoExtra(perfilSelecionado, chavePermissao);
    } else {
      desabilitarPermissaoExtra(perfilSelecionado, chavePermissao);
    }
    
    onPermissaoAlterada?.(perfilSelecionado, chavePermissao, habilitado);
  };

  const getIconeCategoria = (categoria: string) => {
    switch (categoria) {
      case 'Análise': return <BarChart3 className="h-4 w-4" />;
      case 'Relatórios': return <FileText className="h-4 w-4" />;
      case 'Exportação': return <Download className="h-4 w-4" />;
      case 'Histórico': return <History className="h-4 w-4" />;
      case 'Dashboard': return <BarChart3 className="h-4 w-4" />;
      case 'Tecnologia': return <Zap className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const permissoesFiltradas = () => {
    const permissoes = PERMISSOES_EXTRAS_POR_PERFIL[perfilSelecionado] || {};
    
    if (categoriaSelecionada === 'todas') {
      return Object.entries(permissoes);
    }
    
    return Object.entries(permissoes).filter(([_, permissao]) => 
      permissao.categoria === categoriaSelecionada
    );
  };

  const getCorPerfil = (perfil: PerfilUsuario) => {
    const cores = {
      admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      coordenador: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      assessor: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      analista: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      pesquisador: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
      secretaria: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300',
      consulta: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
      estagiario: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300'
    };
    return cores[perfil];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Gerenciador de Permissões Extras</h2>
          <p className="text-muted-foreground">
            Habilite funcionalidades adicionais para cada perfil de usuário
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Info className="h-4 w-4 text-blue-500" />
          <span className="text-sm text-muted-foreground">
            Clique nos switches para habilitar/desabilitar
          </span>
        </div>
      </div>

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {Object.entries(estatisticas).map(([perfil, stats]) => (
          <Card key={perfil} className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setPerfilSelecionado(perfil as PerfilUsuario)}>
            <CardContent className="p-4">
              <div className="text-center">
                <Badge className={`${getCorPerfil(perfil as PerfilUsuario)} mb-2`}>
                  {perfil}
                </Badge>
                <div className="text-lg font-bold">
                  {stats.habilitadas}/{stats.total}
                </div>
                <div className="text-xs text-muted-foreground">
                  habilitadas
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Seletor de Perfil */}
      <Tabs value={perfilSelecionado} onValueChange={(value) => setPerfilSelecionado(value as PerfilUsuario)}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
          <TabsTrigger value="pesquisador">Pesquisador</TabsTrigger>
          <TabsTrigger value="analista">Analista</TabsTrigger>
          <TabsTrigger value="secretaria">Secretaria</TabsTrigger>
          <TabsTrigger value="estagiario">Estagiário</TabsTrigger>
          <TabsTrigger value="consulta">Consulta</TabsTrigger>
          <TabsTrigger value="assessor">Assessor</TabsTrigger>
          <TabsTrigger value="coordenador">Coordenador</TabsTrigger>
          <TabsTrigger value="admin">Admin</TabsTrigger>
        </TabsList>

        {/* Filtro por Categoria */}
        <div className="flex flex-wrap gap-2 mt-4">
          <Button
            variant={categoriaSelecionada === 'todas' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setCategoriaSelecionada('todas')}
          >
            Todas
          </Button>
          {CATEGORIAS_PERMISSOES.map(categoria => (
            <Button
              key={categoria}
              variant={categoriaSelecionada === categoria ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCategoriaSelecionada(categoria)}
              className="flex items-center space-x-1"
            >
              {getIconeCategoria(categoria)}
              <span>{categoria}</span>
            </Button>
          ))}
        </div>

        {/* Lista de Permissões */}
        <TabsContent value={perfilSelecionado} className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Permissões Extras para {perfilSelecionado.charAt(0).toUpperCase() + perfilSelecionado.slice(1)}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {permissoesFiltradas().length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Nenhuma permissão extra disponível para este perfil</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {permissoesFiltradas().map(([chave, permissao]) => (
                    <div key={chave} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          {getIconeCategoria(permissao.categoria)}
                          <div>
                            <h4 className="font-medium text-foreground">{permissao.nome}</h4>
                            <p className="text-sm text-muted-foreground">{permissao.descricao}</p>
                          </div>
                        </div>
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs">
                            {permissao.categoria}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        {permissao.habilitado ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-gray-400" />
                        )}
                        
                        <Switch
                          checked={permissao.habilitado}
                          onCheckedChange={(checked) => handleTogglePermissao(chave, checked)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
