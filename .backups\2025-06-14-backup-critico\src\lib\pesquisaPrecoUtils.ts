import { PesquisaPreco, PesquisaPrecoStats, HistoricoPesquisa } from '@/types/contrato';

/**
 * Utilitários para pesquisa de preços
 */

/**
 * Formata valor monetário para pesquisa de preços
 */
export function formatarPreco(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(valor);
}

/**
 * Calcula variação percentual entre preços
 */
export function calcularVariacaoPreco(precoBase: number, precoComparado: number): number {
  if (precoBase <= 0) return 0;
  return ((precoComparado - precoBase) / precoBase) * 100;
}

/**
 * Calcula economia estimada baseada no menor preço encontrado
 */
export function calcularEconomiaEstimada(precoAtual: number, menorPrecoEncontrado: number, quantidade: number = 1): number {
  if (precoAtual <= menorPrecoEncontrado) return 0;
  return (precoAtual - menorPrecoEncontrado) * quantidade;
}

/**
 * Classifica a variação de preço
 */
export function classificarVariacaoPreco(variacao: number): {
  categoria: 'MUITO_BAIXO' | 'BAIXO' | 'MEDIO' | 'ALTO' | 'MUITO_ALTO';
  cor: string;
  descricao: string;
} {
  if (variacao <= -20) {
    return {
      categoria: 'MUITO_BAIXO',
      cor: 'text-green-700 bg-green-100 border-green-300',
      descricao: 'Muito abaixo da média'
    };
  } else if (variacao <= -10) {
    return {
      categoria: 'BAIXO',
      cor: 'text-green-600 bg-green-50 border-green-200',
      descricao: 'Abaixo da média'
    };
  } else if (variacao <= 10) {
    return {
      categoria: 'MEDIO',
      cor: 'text-blue-600 bg-blue-50 border-blue-200',
      descricao: 'Dentro da média'
    };
  } else if (variacao <= 20) {
    return {
      categoria: 'ALTO',
      cor: 'text-orange-600 bg-orange-50 border-orange-200',
      descricao: 'Acima da média'
    };
  } else {
    return {
      categoria: 'MUITO_ALTO',
      cor: 'text-red-600 bg-red-50 border-red-200',
      descricao: 'Muito acima da média'
    };
  }
}

/**
 * Filtra resultados de pesquisa de preços
 */
export function filtrarPesquisaPrecos(resultados: PesquisaPreco[], filtros: any): PesquisaPreco[] {
  return resultados.filter(resultado => {
    // Filtro por texto
    if (filtros.search) {
      const searchLower = filtros.search.toLowerCase();
      const matchesSearch = 
        resultado.item.toLowerCase().includes(searchLower) ||
        resultado.categoria.toLowerCase().includes(searchLower) ||
        resultado.orgao.toLowerCase().includes(searchLower) ||
        (resultado.fornecedor && resultado.fornecedor.toLowerCase().includes(searchLower));
      
      if (!matchesSearch) return false;
    }

    // Filtro por categoria
    if (filtros.categoria && !resultado.categoria.toLowerCase().includes(filtros.categoria.toLowerCase())) {
      return false;
    }

    // Filtro por UF
    if (filtros.uf && resultado.uf !== filtros.uf) {
      return false;
    }

    // Filtro por modalidade
    if (filtros.modalidade && !resultado.modalidade.toLowerCase().includes(filtros.modalidade.toLowerCase())) {
      return false;
    }

    // Filtro por faixa de preço
    if (filtros.precoMin && resultado.precoMedio < filtros.precoMin) {
      return false;
    }

    if (filtros.precoMax && resultado.precoMedio > filtros.precoMax) {
      return false;
    }

    return true;
  });
}

/**
 * Ordena resultados de pesquisa
 */
export function ordenarPesquisaPrecos(resultados: PesquisaPreco[], criterio: string, ordem: 'asc' | 'desc' = 'asc'): PesquisaPreco[] {
  return [...resultados].sort((a, b) => {
    let valorA: any;
    let valorB: any;

    switch (criterio) {
      case 'item':
        valorA = a.item;
        valorB = b.item;
        break;
      case 'precoMedio':
        valorA = a.precoMedio;
        valorB = b.precoMedio;
        break;
      case 'menorPreco':
        valorA = a.menorPreco;
        valorB = b.menorPreco;
        break;
      case 'dataLicitacao':
        valorA = new Date(a.dataLicitacao);
        valorB = new Date(b.dataLicitacao);
        break;
      case 'orgao':
        valorA = a.orgao;
        valorB = b.orgao;
        break;
      default:
        valorA = a.precoMedio;
        valorB = b.precoMedio;
    }

    if (valorA < valorB) return ordem === 'asc' ? -1 : 1;
    if (valorA > valorB) return ordem === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Calcula estatísticas de pesquisa de preços
 */
export function calcularEstatisticasPesquisa(resultados: PesquisaPreco[]): PesquisaPrecoStats {
  if (resultados.length === 0) {
    return {
      totalResultados: 0,
      precoMedio: 0,
      menorPrecoGeral: 0,
      maiorPrecoGeral: 0,
      orgaosConsultados: 0,
      economiaEstimada: 0
    };
  }

  const precoMedio = resultados.reduce((sum, r) => sum + r.precoMedio, 0) / resultados.length;
  const menorPrecoGeral = Math.min(...resultados.map(r => r.menorPreco));
  const maiorPrecoGeral = Math.max(...resultados.map(r => r.maiorPreco));
  const orgaosConsultados = new Set(resultados.map(r => r.orgao)).size;

  // Calcula economia estimada baseada na diferença entre preço médio e menor preço
  const economiaEstimada = resultados.reduce((sum, r) => {
    return sum + Math.max(0, r.precoMedio - r.menorPreco);
  }, 0);

  return {
    totalResultados: resultados.length,
    precoMedio,
    menorPrecoGeral,
    maiorPrecoGeral,
    orgaosConsultados,
    economiaEstimada
  };
}

/**
 * Gera relatório de pesquisa de preços
 */
export function gerarRelatorioPesquisa(resultados: PesquisaPreco[], termo: string) {
  const stats = calcularEstatisticasPesquisa(resultados);
  
  // Agrupa por categoria
  const porCategoria = resultados.reduce((acc, resultado) => {
    if (!acc[resultado.categoria]) {
      acc[resultado.categoria] = [];
    }
    acc[resultado.categoria].push(resultado);
    return acc;
  }, {} as Record<string, PesquisaPreco[]>);

  // Agrupa por UF
  const porUF = resultados.reduce((acc, resultado) => {
    if (!acc[resultado.uf]) {
      acc[resultado.uf] = [];
    }
    acc[resultado.uf].push(resultado);
    return acc;
  }, {} as Record<string, PesquisaPreco[]>);

  // Encontra melhores oportunidades (menor preço por categoria)
  const melhoresOportunidades = Object.entries(porCategoria).map(([categoria, items]) => {
    const menorPreco = Math.min(...items.map(i => i.menorPreco));
    const item = items.find(i => i.menorPreco === menorPreco);
    return { categoria, item, economia: stats.precoMedio - menorPreco };
  }).sort((a, b) => b.economia - a.economia);

  return {
    termo,
    dataConsulta: new Date().toLocaleDateString('pt-BR'),
    estatisticas: stats,
    distribuicao: {
      porCategoria: Object.keys(porCategoria).map(cat => ({
        categoria: cat,
        quantidade: porCategoria[cat].length,
        precoMedio: porCategoria[cat].reduce((sum, i) => sum + i.precoMedio, 0) / porCategoria[cat].length
      })),
      porUF: Object.keys(porUF).map(uf => ({
        uf,
        quantidade: porUF[uf].length,
        precoMedio: porUF[uf].reduce((sum, i) => sum + i.precoMedio, 0) / porUF[uf].length
      }))
    },
    melhoresOportunidades: melhoresOportunidades.slice(0, 5),
    recomendacoes: gerarRecomendacoes(resultados, stats)
  };
}

/**
 * Gera recomendações baseadas nos resultados
 */
function gerarRecomendacoes(resultados: PesquisaPreco[], stats: PesquisaPrecoStats): string[] {
  const recomendacoes: string[] = [];

  if (stats.totalResultados === 0) {
    recomendacoes.push('Nenhum resultado encontrado. Tente termos mais genéricos.');
    return recomendacoes;
  }

  if (stats.totalResultados < 5) {
    recomendacoes.push('Poucos resultados encontrados. Considere ampliar os critérios de busca.');
  }

  const variacao = ((stats.maiorPrecoGeral - stats.menorPrecoGeral) / stats.precoMedio) * 100;
  
  if (variacao > 50) {
    recomendacoes.push('Grande variação de preços encontrada. Analise cuidadosamente as especificações.');
  }

  if (stats.orgaosConsultados >= 10) {
    recomendacoes.push('Boa amostra de órgãos consultados. Dados confiáveis para tomada de decisão.');
  }

  if (stats.economiaEstimada > 1000) {
    recomendacoes.push(`Potencial economia de ${formatarPreco(stats.economiaEstimada)} identificada.`);
  }

  // Analisa modalidades mais econômicas
  const modalidades = resultados.reduce((acc, r) => {
    if (!acc[r.modalidade]) {
      acc[r.modalidade] = [];
    }
    acc[r.modalidade].push(r.precoMedio);
    return acc;
  }, {} as Record<string, number[]>);

  const modalidadeMaisEconomica = Object.entries(modalidades)
    .map(([modalidade, precos]) => ({
      modalidade,
      precoMedio: precos.reduce((sum, p) => sum + p, 0) / precos.length
    }))
    .sort((a, b) => a.precoMedio - b.precoMedio)[0];

  if (modalidadeMaisEconomica) {
    recomendacoes.push(`Modalidade mais econômica: ${modalidadeMaisEconomica.modalidade}`);
  }

  return recomendacoes;
}

/**
 * Exporta dados para CSV
 */
export function exportarParaCSV(resultados: PesquisaPreco[], termo: string): string {
  const headers = [
    'Item',
    'Categoria',
    'Preço Médio',
    'Menor Preço',
    'Maior Preço',
    'Órgão',
    'Data Licitação',
    'Modalidade',
    'Status',
    'UF',
    'Cidade',
    'Fornecedor'
  ];

  const rows = resultados.map(r => [
    r.item,
    r.categoria,
    r.precoMedio.toFixed(2),
    r.menorPreco.toFixed(2),
    r.maiorPreco.toFixed(2),
    r.orgao,
    r.dataLicitacao,
    r.modalidade,
    r.status,
    r.uf,
    r.cidade,
    r.fornecedor || ''
  ]);

  const csvContent = [
    `# Pesquisa de Preços - ${termo}`,
    `# Data: ${new Date().toLocaleDateString('pt-BR')}`,
    `# Total de resultados: ${resultados.length}`,
    '',
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n');

  return csvContent;
}
