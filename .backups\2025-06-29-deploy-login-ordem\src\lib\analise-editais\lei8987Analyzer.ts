/**
 * 🏛️ ANALISADOR LEI 8.987/95 - CONCESSÕES E PERMISSÕES
 * Regime de concessão e permissão da prestação de serviços públicos
 */

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface ViolacaoLei8987 {
  artigo: string;
  descricao: string;
  gravidade: 'CRITICA' | 'ALTA' | 'MEDIA' | 'BAIXA';
  evidencia: string;
  sugestaoCorrecao: string;
  tipoViolacao: 'LEGAL' | 'PROCEDIMENTO' | 'DOCUMENTACAO' | 'FORMATO';
}

interface AnaliseLei8987 {
  conformeLei8987: boolean;
  scoreLei8987: number;
  violacoes: ViolacaoLei8987[];
  violacoesGraves: ViolacaoLei8987[];
  recomendacoesConcessao: any[];
  artigosVerificados: string[];
  observacoesOutorga: string[];
  aplicavelProcesso: boolean; // Se a lei se aplica ao processo
}

export async function analisarConformidadeLei8987(documentos: DocumentoAnalise[]): Promise<AnaliseLei8987> {
  const violacoes: ViolacaoLei8987[] = [];
  const observacoesOutorga: string[] = [];

  // Verificar se a lei se aplica ao processo
  const aplicavel = verificarAplicabilidadeLei8987(documentos);
  
  if (!aplicavel) {
    return {
      conformeLei8987: true,
      scoreLei8987: 100,
      violacoes: [],
      violacoesGraves: [],
      recomendacoesConcessao: [],
      artigosVerificados: [],
      observacoesOutorga: ['Lei 8.987/95 não se aplica a este processo'],
      aplicavelProcesso: false
    };
  }

  // Verificações por tipo de documento
  for (const doc of documentos) {
    switch (doc.tipo) {
      case 'etp':
        violacoes.push(...verificarETPLei8987(doc));
        break;
      case 'edital':
        violacoes.push(...verificarEditalLei8987(doc));
        break;
      case 'tr':
        violacoes.push(...verificarTRLei8987(doc));
        break;
    }
  }

  // Verificações gerais do processo de concessão
  violacoes.push(...verificarProcessoConcessao(documentos));

  const violacoesGraves = violacoes.filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA');
  const scoreLei8987 = calcularScoreLei8987(violacoes);
  const conformeLei8987 = violacoesGraves.length === 0 && scoreLei8987 >= 80;

  return {
    conformeLei8987,
    scoreLei8987,
    violacoes,
    violacoesGraves,
    recomendacoesConcessao: gerarRecomendacoesConcessao(violacoes),
    artigosVerificados: [
      'Art. 2º - Conceitos',
      'Art. 14 - Edital de licitação',
      'Art. 15 - Conteúdo do edital',
      'Art. 18 - Critérios de julgamento',
      'Art. 23 - Contrato de concessão'
    ],
    observacoesOutorga,
    aplicavelProcesso: true
  };
}

function verificarAplicabilidadeLei8987(documentos: DocumentoAnalise[]): boolean {
  const conteudoCompleto = documentos.map(doc => doc.conteudo.toLowerCase()).join(' ');
  
  const termosConcessao = [
    'concessão', 'concessao', 'permissão', 'permissao', 'outorga',
    'serviço público', 'servico publico', 'prestação de serviço',
    'delegação', 'parceria público-privada', 'ppp'
  ];

  return termosConcessao.some(termo => conteudoCompleto.includes(termo));
}

function verificarETPLei8987(doc: DocumentoAnalise): ViolacaoLei8987[] {
  const violacoes: ViolacaoLei8987[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Art. 2º - Conceitos fundamentais
  if (!conteudo.includes('serviço público') && !conteudo.includes('servico publico')) {
    violacoes.push({
      artigo: 'Art. 2º, II',
      descricao: 'Ausência de caracterização do serviço público',
      gravidade: 'ALTA',
      evidencia: 'Conceito de serviço público não identificado',
      sugestaoCorrecao: 'Caracterizar claramente o serviço público objeto da concessão',
      tipoViolacao: 'LEGAL'
    });
  }

  // Verificar adequação da modalidade
  if (conteudo.includes('concessão') || conteudo.includes('concessao')) {
    if (!conteudo.includes('interesse público') && !conteudo.includes('interesse publico')) {
      violacoes.push({
        artigo: 'Art. 2º, III',
        descricao: 'Ausência de demonstração do interesse público',
        gravidade: 'CRITICA',
        evidencia: 'Interesse público não fundamentado para concessão',
        sugestaoCorrecao: 'Demonstrar claramente o interesse público na concessão',
        tipoViolacao: 'LEGAL'
      });
    }
  }

  // Verificar estudos de viabilidade
  if (!conteudo.includes('viabilidade') && !conteudo.includes('estudo econômico')) {
    violacoes.push({
      artigo: 'Art. 5º',
      descricao: 'Ausência de estudos de viabilidade econômico-financeira',
      gravidade: 'ALTA',
      evidencia: 'Estudos de viabilidade não identificados',
      sugestaoCorrecao: 'Incluir estudos de viabilidade econômico-financeira',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  return violacoes;
}

function verificarEditalLei8987(doc: DocumentoAnalise): ViolacaoLei8987[] {
  const violacoes: ViolacaoLei8987[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Art. 14 - Edital de licitação para concessão
  const elementosObrigatorios = [
    'objeto da concessão',
    'prazo da concessão',
    'critérios de julgamento',
    'política tarifária',
    'obrigações do poder concedente',
    'obrigações da concessionária'
  ];

  elementosObrigatorios.forEach(elemento => {
    if (!conteudo.includes(elemento.replace('ç', 'c').replace('ã', 'a'))) {
      violacoes.push({
        artigo: 'Art. 15',
        descricao: `Ausência de elemento obrigatório: ${elemento}`,
        gravidade: 'ALTA',
        evidencia: `Elemento "${elemento}" não encontrado no edital`,
        sugestaoCorrecao: `Incluir ${elemento} conforme Art. 15 da Lei 8.987/95`,
        tipoViolacao: 'LEGAL'
      });
    }
  });

  // Art. 18 - Critérios de julgamento específicos
  if (conteudo.includes('menor tarifa') || conteudo.includes('maior oferta')) {
    // Verificar se há justificativa adequada
    if (!conteudo.includes('justificativa') && !conteudo.includes('fundamentação')) {
      violacoes.push({
        artigo: 'Art. 18',
        descricao: 'Critério de julgamento sem fundamentação adequada',
        gravidade: 'MEDIA',
        evidencia: 'Critério de julgamento não fundamentado',
        sugestaoCorrecao: 'Fundamentar adequadamente o critério de julgamento escolhido',
        tipoViolacao: 'PROCEDIMENTO'
      });
    }
  }

  return violacoes;
}

function verificarTRLei8987(doc: DocumentoAnalise): ViolacaoLei8987[] {
  const violacoes: ViolacaoLei8987[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Verificar especificações técnicas para concessão
  if (!conteudo.includes('especificação técnica') && !conteudo.includes('especificacao tecnica')) {
    violacoes.push({
      artigo: 'Especificações Técnicas',
      descricao: 'Ausência de especificações técnicas detalhadas',
      gravidade: 'MEDIA',
      evidencia: 'Especificações técnicas não identificadas',
      sugestaoCorrecao: 'Detalhar especificações técnicas do serviço público',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  return violacoes;
}

function verificarProcessoConcessao(documentos: DocumentoAnalise[]): ViolacaoLei8987[] {
  const violacoes: ViolacaoLei8987[] = [];
  const conteudoCompleto = documentos.map(doc => doc.conteudo.toLowerCase()).join(' ');

  // Verificar coerência entre documentos para concessão
  const temConcessao = conteudoCompleto.includes('concessão') || conteudoCompleto.includes('concessao');
  const temPermissao = conteudoCompleto.includes('permissão') || conteudoCompleto.includes('permissao');

  if (temConcessao && temPermissao) {
    violacoes.push({
      artigo: 'Coerência Processual',
      descricao: 'Inconsistência entre concessão e permissão nos documentos',
      gravidade: 'ALTA',
      evidencia: 'Documentos mencionam tanto concessão quanto permissão',
      sugestaoCorrecao: 'Definir claramente se é processo de concessão ou permissão',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  return violacoes;
}

function calcularScoreLei8987(violacoes: ViolacaoLei8987[]): number {
  let penalizacao = 0;
  
  violacoes.forEach(violacao => {
    switch (violacao.gravidade) {
      case 'CRITICA': penalizacao += 25; break;
      case 'ALTA': penalizacao += 15; break;
      case 'MEDIA': penalizacao += 8; break;
      case 'BAIXA': penalizacao += 3; break;
    }
  });

  return Math.max(0, 100 - penalizacao);
}

function gerarRecomendacoesConcessao(violacoes: ViolacaoLei8987[]): any[] {
  return violacoes
    .filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA')
    .map(violacao => ({
      prioridade: violacao.gravidade === 'CRITICA' ? 'ALTA' : 'MEDIA',
      titulo: `Adequação Lei 8.987/95 - ${violacao.artigo}`,
      descricao: violacao.descricao,
      acoes: [violacao.sugestaoCorrecao],
      artigo: violacao.artigo,
      categoria: 'CONCESSAO'
    }));
}
