'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  UserCheck, 
  UserX,
  AlertCircle,
  Loader2,
  Calendar,
  Mail,
  Shield
} from 'lucide-react';

interface UsuarioPendente {
  uid: string;
  email: string;
  nome: string;
  dataCadastro: any;
  status: 'pendente' | 'aprovado' | 'reprovado';
  nivelAcesso?: string;
  observacoes?: string;
}

export default function GestaoUsuariosPage() {
  const router = useRouter();
  const { user, userProfile, loading } = useAuth();
  const [usuariosPendentes, setUsuariosPendentes] = useState<UsuarioPendente[]>([]);
  const [usuariosAprovados, setUsuariosAprovados] = useState<UsuarioPendente[]>([]);
  const [usuariosReprovados, setUsuariosReprovados] = useState<UsuarioPendente[]>([]);
  const [loadingUsuarios, setLoadingUsuarios] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processando, setProcessando] = useState<string | null>(null);

  useEffect(() => {
    // Verificar se é admin
    if (!loading && (!user || userProfile?.perfil !== 'admin')) {
      router.push('/dashboard');
      return;
    }

    if (user && userProfile?.perfil === 'admin') {
      carregarUsuarios();
    }
  }, [user, userProfile, loading, router]);

  const carregarUsuarios = async () => {
    try {
      setLoadingUsuarios(true);
      setError(null);

      // Buscar usuários do Firestore
      const response = await fetch('/api/usuarios');
      if (!response.ok) {
        throw new Error('Erro ao carregar usuários');
      }

      const usuarios = await response.json();
      
      // Separar por status
      const pendentes = usuarios.filter((u: UsuarioPendente) => u.status === 'pendente');
      const aprovados = usuarios.filter((u: UsuarioPendente) => u.status === 'aprovado');
      const reprovados = usuarios.filter((u: UsuarioPendente) => u.status === 'reprovado');

      setUsuariosPendentes(pendentes);
      setUsuariosAprovados(aprovados);
      setUsuariosReprovados(reprovados);

    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      setError('Erro ao carregar lista de usuários');
    } finally {
      setLoadingUsuarios(false);
    }
  };

  const aprovarUsuario = async (userId: string, nivelAcesso: string) => {
    try {
      setProcessando(userId);
      setError(null);

      const response = await fetch(`/api/usuarios/${userId}/aprovar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nivelAcesso,
          aprovadoPor: user?.uid,
          observacoes: `Aprovado como ${nivelAcesso}`
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao aprovar usuário');
      }

      // Recarregar lista
      await carregarUsuarios();
      
    } catch (error) {
      console.error('Erro ao aprovar usuário:', error);
      setError('Erro ao aprovar usuário');
    } finally {
      setProcessando(null);
    }
  };

  const reprovarUsuario = async (userId: string, motivo?: string) => {
    try {
      setProcessando(userId);
      setError(null);

      const response = await fetch(`/api/usuarios/${userId}/reprovar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reprovadoPor: user?.uid,
          observacoes: motivo || 'Acesso negado pelo administrador'
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao reprovar usuário');
      }

      // Recarregar lista
      await carregarUsuarios();
      
    } catch (error) {
      console.error('Erro ao reprovar usuário:', error);
      setError('Erro ao reprovar usuário');
    } finally {
      setProcessando(null);
    }
  };

  const formatarData = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleString('pt-BR');
    } catch {
      return 'N/A';
    }
  };

  if (loading || loadingUsuarios) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600 dark:text-gray-300">
            Carregando...
          </p>
        </div>
      </div>
    );
  }

  if (!user || userProfile?.perfil !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Acesso negado. Apenas administradores podem acessar esta página.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Gestão de Usuários
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              Aprove ou reprove solicitações de acesso ao sistema
            </p>
          </div>
          <Button onClick={carregarUsuarios} variant="outline">
            <Users className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Pendentes
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {usuariosPendentes.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Aprovados
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {usuariosAprovados.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <XCircle className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    Reprovados
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {usuariosReprovados.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Usuários Pendentes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-yellow-600" />
              Usuários Pendentes ({usuariosPendentes.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {usuariosPendentes.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                Nenhum usuário pendente de aprovação.
              </p>
            ) : (
              <div className="space-y-4">
                {usuariosPendentes.map((usuario) => (
                  <div
                    key={usuario.uid}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                              <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                              {usuario.nome}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                              <Mail className="mr-1 h-3 w-3" />
                              {usuario.email}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                              <Calendar className="mr-1 h-3 w-3" />
                              Cadastrado em: {formatarData(usuario.dataCadastro)}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => aprovarUsuario(usuario.uid, 'usuario')}
                          disabled={processando === usuario.uid}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {processando === usuario.uid ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <UserCheck className="h-4 w-4" />
                          )}
                          Aprovar
                        </Button>
                        
                        <Button
                          onClick={() => aprovarUsuario(usuario.uid, 'gestor')}
                          disabled={processando === usuario.uid}
                          size="sm"
                          variant="outline"
                        >
                          {processando === usuario.uid ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Shield className="h-4 w-4" />
                          )}
                          Gestor
                        </Button>
                        
                        <Button
                          onClick={() => reprovarUsuario(usuario.uid)}
                          disabled={processando === usuario.uid}
                          size="sm"
                          variant="destructive"
                        >
                          {processando === usuario.uid ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <UserX className="h-4 w-4" />
                          )}
                          Reprovar
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Usuários Aprovados */}
        {usuariosAprovados.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5 text-green-600" />
                Usuários Aprovados ({usuariosAprovados.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {usuariosAprovados.map((usuario) => (
                  <div
                    key={usuario.uid}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {usuario.nome}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {usuario.email}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary">
                              {usuario.nivelAcesso || 'Usuário'}
                            </Badge>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Aprovado em: {formatarData(usuario.dataCadastro)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Usuários Reprovados */}
        {usuariosReprovados.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <XCircle className="mr-2 h-5 w-5 text-red-600" />
                Usuários Reprovados ({usuariosReprovados.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {usuariosReprovados.map((usuario) => (
                  <div
                    key={usuario.uid}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                            <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
                          </div>
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {usuario.nome}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {usuario.email}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Reprovado em: {formatarData(usuario.dataCadastro)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
} 