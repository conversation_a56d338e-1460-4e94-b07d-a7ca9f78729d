'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { LucideIcon, TrendingUp, TrendingDown, Info, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedMetricCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  tooltipContent?: string;
  breakdown?: Array<{ label: string; value: number; percentage?: number }>;
  showChart?: boolean;
}

export function EnhancedMetricCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  variant = 'default',
  tooltipContent,
  breakdown,
  showChart = false
}: EnhancedMetricCardProps) {
  const variantStyles = {
    default: 'border-border hover:border-blue-300 hover:shadow-lg',
    success: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/20 hover:border-green-400 hover:shadow-green-100',
    warning: 'border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/20 hover:border-yellow-400 hover:shadow-yellow-100',
    destructive: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20 hover:border-red-400 hover:shadow-red-100'
  };

  const iconStyles = {
    default: 'text-muted-foreground',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    destructive: 'text-red-600 dark:text-red-400'
  };

  const gradientStyles = {
    default: 'from-blue-500 to-purple-600',
    success: 'from-green-500 to-emerald-600',
    warning: 'from-yellow-500 to-orange-600',
    destructive: 'from-red-500 to-pink-600'
  };

  const CardWrapper = ({ children }: { children: React.ReactNode }) => {
    if (tooltipContent || breakdown) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {children}
            </TooltipTrigger>
            <TooltipContent side="top" className="max-w-xs p-4">
              {tooltipContent && (
                <div className="mb-2">
                  <p className="text-sm">{tooltipContent}</p>
                </div>
              )}
              {breakdown && (
                <div className="space-y-2">
                  <h4 className="font-semibold text-sm border-b pb-1">Detalhamento:</h4>
                  {breakdown.map((item, index) => (
                    <div key={index} className="flex justify-between items-center text-xs">
                      <span className="text-muted-foreground">{item.label}:</span>
                      <div className="text-right">
                        <span className="font-medium">{item.value}</span>
                        {item.percentage && (
                          <span className="text-muted-foreground ml-1">
                            ({item.percentage.toFixed(1)}%)
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return <>{children}</>;
  };

  return (
    <CardWrapper>
      <Card className={cn(
        variantStyles[variant], 
        'transition-all duration-300 cursor-pointer group hover:scale-105',
        className
      )}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
            {title}
            {(tooltipContent || breakdown) && (
              <Info className="inline-block w-3 h-3 ml-1 opacity-50 group-hover:opacity-100" />
            )}
          </CardTitle>
          <div className="flex items-center space-x-1">
            {showChart && (
              <BarChart3 className="h-4 w-4 text-muted-foreground group-hover:text-blue-500 transition-colors" />
            )}
            <Icon className={cn('h-5 w-5 group-hover:scale-110 transition-transform', iconStyles[variant])} />
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="relative">
            {/* Valor principal com gradiente */}
            <div className={cn(
              "text-3xl font-bold mb-1 bg-gradient-to-r bg-clip-text text-transparent",
              gradientStyles[variant]
            )}>
              {typeof value === 'number' ? value.toLocaleString('pt-BR') : value}
            </div>
            
            {/* Barra de progresso visual */}
            {typeof value === 'number' && breakdown && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-2 overflow-hidden">
                <div
                  className={cn("h-1 rounded-full bg-gradient-to-r transition-all duration-1000", gradientStyles[variant])}
                  style={{ width: `${Math.min((value / 150) * 100, 100)}%` }}
                />
              </div>
            )}
            
            {description && (
              <p className="text-sm text-muted-foreground mb-2 group-hover:text-foreground transition-colors">
                {description}
              </p>
            )}

            {trend && (
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={trend.isPositive ? "default" : "destructive"}
                  className="text-xs flex items-center space-x-1"
                >
                  {trend.isPositive ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  <span>{trend.value > 0 ? '+' : ''}{trend.value}%</span>
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {trend.label}
                </span>
              </div>
            )}

            {/* Mini breakdown visual */}
            {breakdown && breakdown.length <= 3 && (
              <div className="mt-3 space-y-1">
                {breakdown.slice(0, 3).map((item, index) => (
                  <div key={index} className="flex justify-between text-xs opacity-70 group-hover:opacity-100 transition-opacity">
                    <span className="truncate max-w-24 text-muted-foreground">{item.label}</span>
                    <span className="font-medium text-foreground">{item.value}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </CardWrapper>
  );
}
