const fs = require('fs');
const path = require('path');

// Lista dos processos finalizados identificados pelo usuário
const processosFinalizados = [
  '6752/2024', '8957/2024', '2134/2025', '1034/2025', '7396/2024',
  '4604/2024', '5556/2024', '9903/2024', '8897/2024', '802/2025',
  '2984/2024', '8083/2024', '9698/2024', '5989/2024', '7054/2024',
  '9650/2024', '7092/2024', '4514/2024', '7155/2024', '1032/2025', '2114/2025'
];

function parseCSV(content) {
  const lines = content.split('\n');
  if (lines.length === 0) return [];

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    if (lines[i].trim()) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      const row = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      data.push(row);
    }
  }

  return data;
}

async function analisarProcessosFinalizados() {
  console.log('🔍 ANALISANDO PROCESSOS FINALIZADOS...\n');

  const statusFinalizadosReais = new Set();
  const processosEncontrados = [];
  const processosNaoEncontrados = [];

  // Diretórios de dados
  const dataDirs = [
    'databackup-junho-v1',
    'databackup-maio-v1',
    'databackup-29 e 30-04'
  ];

  for (const dataDir of dataDirs) {
    const dirPath = path.join(process.cwd(), dataDir);

    if (!fs.existsSync(dirPath)) {
      console.log(`❌ Diretório não encontrado: ${dataDir}`);
      continue;
    }

    console.log(`📁 Analisando diretório: ${dataDir}`);

    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.csv'));

    for (const file of files) {
      const filePath = path.join(dirPath, file);
      console.log(`  📄 Processando arquivo: ${file}`);

      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const results = parseCSV(content);

        // Analisar cada processo finalizado
        processosFinalizados.forEach(numeroProcesso => {
          const processo = results.find(row => {
            // Verificar diferentes possíveis nomes de colunas
            const processoValue = row.PROCESSO || row.processo || row['Nº do Processo'] || row['Num do Processo'] || row['Número do Processo'];
            return processoValue && processoValue.toString().trim() === numeroProcesso;
          });

          if (processo) {
            const status = processo.STATUS || processo.status || processo.Status;
            if (status) {
              statusFinalizadosReais.add(status.trim());
              processosEncontrados.push({
                processo: numeroProcesso,
                status: status.trim(),
                arquivo: file
              });
              console.log(`    ✅ ${numeroProcesso}: STATUS = "${status.trim()}"`);
            }
          }
        });
      } catch (error) {
        console.log(`    ❌ Erro ao processar ${file}: ${error.message}`);
      }
    }
  }
  
  // Verificar quais processos não foram encontrados
  processosFinalizados.forEach(numeroProcesso => {
    const encontrado = processosEncontrados.find(p => p.processo === numeroProcesso);
    if (!encontrado) {
      processosNaoEncontrados.push(numeroProcesso);
    }
  });
  
  console.log('\n📊 RESULTADOS DA ANÁLISE:');
  console.log('='.repeat(50));
  
  console.log('\n✅ PROCESSOS ENCONTRADOS:');
  processosEncontrados.forEach(p => {
    console.log(`  ${p.processo} → "${p.status}" (${p.arquivo})`);
  });
  
  console.log('\n❌ PROCESSOS NÃO ENCONTRADOS:');
  processosNaoEncontrados.forEach(p => {
    console.log(`  ${p}`);
  });
  
  console.log('\n🏷️ STATUS REAIS DOS PROCESSOS FINALIZADOS:');
  const statusArray = Array.from(statusFinalizadosReais).sort();
  statusArray.forEach(status => {
    console.log(`  "${status}"`);
  });
  
  console.log('\n📝 LISTA PARA ATUALIZAR STATUS_FINALIZADOS:');
  console.log('[');
  statusArray.forEach((status, index) => {
    const comma = index < statusArray.length - 1 ? ',' : '';
    console.log(`  "${status}"${comma}`);
  });
  console.log(']');
  
  console.log('\n🔢 ESTATÍSTICAS:');
  console.log(`  Total de processos analisados: ${processosFinalizados.length}`);
  console.log(`  Processos encontrados: ${processosEncontrados.length}`);
  console.log(`  Processos não encontrados: ${processosNaoEncontrados.length}`);
  console.log(`  Status únicos identificados: ${statusFinalizadosReais.size}`);
}

analisarProcessosFinalizados().catch(console.error);
