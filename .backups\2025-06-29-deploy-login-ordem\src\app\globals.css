@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 25%;
    --input: 217.2 32.6% 25%;
    --ring: 221.2 83.2% 53.3%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Animações suaves */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ocultar elementos indesejados que possam aparecer no canto inferior esquerdo */
div[style*="position: fixed"][style*="bottom: 0"][style*="left: 0"],
div[style*="position: absolute"][style*="bottom: 0"][style*="left: 0"],
div[style*="z-index"][style*="bottom"][style*="left"] {
  display: none !important;
}

/* Ocultar elementos de notificação mal posicionados */
.notification-overlay,
.toast-container,
[data-testid*="notification"],
[class*="notification"][style*="bottom"],
[class*="toast"][style*="bottom"] {
  display: none !important;
}

/* Ocultar elementos suspeitos que possam conter apenas "N" */
div:not([class]):not([id]):empty::before,
div:not([class]):not([id]):empty::after,
span:not([class]):not([id]):empty::before,
span:not([class]):not([id]):empty::after {
  display: none !important;
}

/* Ocultar elementos de desenvolvimento/debug */
[data-reactroot] > div:last-child:not([class]):not([id]),
body > div:last-child:not([class]):not([id]):not([data-reactroot]) {
  display: none !important;
}

/* Regras simplificadas para ocultar elementos problemáticos */

/* Força ocultar qualquer elemento suspeito no canto inferior esquerdo */
div[style*="position"][style*="left"][style*="bottom"],
span[style*="position"][style*="left"][style*="bottom"] {
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* CSS específico para ocultar o "N" problemático */
body::after,
body::before,
html::after,
html::before {
  content: none !important;
  display: none !important;
}

/* Ocultar elementos que possam estar sendo injetados */
[data-overlay],
[data-portal],
[data-floating],
[role="tooltip"],
[role="alert"]:not(.alert):not(.notification) {
  display: none !important;
}

/* Regras simples para ocultar o "N" problemático */
div:empty {
  display: none !important;
}

span:empty {
  display: none !important;
}

/* Transparência específica para cards no dark mode */
.dark .bg-card {
  background-color: hsla(222.2, 84%, 4.9%, 0.5) !important;
}

/* Campos na cor dos cards - modo escuro */
.dark .bg-card input,
.dark .bg-card textarea,
.dark .bg-card select {
  background-color: hsla(222.2, 84%, 4.9%, 0.5) !important;
  border: 1px solid hsl(217.2, 32.6%, 25%) !important;
}

/* Campos na cor dos cards - modo claro */
input,
textarea,
select {
  background-color: hsl(0, 0%, 100%) !important;
  border: 1px solid hsl(214.3, 31.8%, 80%) !important;
}

/* Garantir que campos fiquem na cor dos cards no modo claro */
.light .bg-card input,
.light .bg-card textarea,
.light .bg-card select {
  background-color: hsl(0, 0%, 100%) !important;
  border: 1px solid hsl(214.3, 31.8%, 80%) !important;
}

/* Contornos mais claros na página de processos */
.processos-page input,
.processos-page textarea,
.processos-page select {
  border: 1px solid hsl(214.3, 31.8%, 70%) !important;
  background-color: hsl(0, 0%, 100%) !important;
}

.dark .processos-page input,
.dark .processos-page textarea,
.dark .processos-page select {
  border: 1px solid hsl(217.2, 32.6%, 35%) !important;
  background-color: hsl(222.2, 84%, 4.9%) !important;
}

/* Correções específicas para modo claro */
input[type="text"],
input[type="search"],
input[type="date"],
input[type="number"],
textarea,
select {
  background-color: white !important;
  border: 1px solid hsl(214.3, 31.8%, 75%) !important;
  color: hsl(222.2, 84%, 4.9%) !important;
}

input[type="text"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: hsl(221.2, 83.2%, 53.3%) !important;
  box-shadow: 0 0 0 2px hsl(221.2, 83.2%, 53.3%, 0.2) !important;
}

/* Garantir que placeholders sejam visíveis no modo claro */
input::placeholder,
textarea::placeholder {
  color: hsl(215.4, 16.3%, 46.9%) !important;
  opacity: 0.8;
}

/* Modo escuro - preservar comportamento atual */
.dark input[type="text"],
.dark input[type="search"],
.dark input[type="date"],
.dark input[type="number"],
.dark textarea,
.dark select {
  background-color: hsl(222.2, 84%, 4.9%) !important;
  border: 1px solid hsl(217.2, 32.6%, 35%) !important;
  color: hsl(210, 40%, 98%) !important;
}

/* Garantir texto branco no botão primário */
.bg-primary,
button[class*="bg-primary"] {
  color: white !important;
}

.dark .bg-primary,
.dark button[class*="bg-primary"] {
  color: white !important;
}

/* Focus states */
input:focus,
textarea:focus,
select:focus {
  border-color: hsl(221.2, 83.2%, 53.3%) !important;
  outline: none !important;
  box-shadow: 0 0 0 2px hsla(221.2, 83.2%, 53.3%, 0.2) !important;
}

/* Correções para cards no modo claro */
.bg-card {
  background-color: white !important;
  border: 1px solid hsl(214.3, 31.8%, 91.4%) !important;
}

.dark .bg-card {
  background-color: hsl(222.2, 84%, 4.9%) !important;
  border: 1px solid hsl(217.2, 32.6%, 25%) !important;
}

/* Correções para badges e elementos coloridos no modo claro */
.bg-green-50 {
  background-color: hsl(120, 60%, 97%) !important;
}

.text-green-700 {
  color: hsl(120, 60%, 25%) !important;
}

.bg-yellow-50 {
  background-color: hsl(45, 100%, 97%) !important;
}

.text-yellow-700 {
  color: hsl(45, 100%, 25%) !important;
}

.bg-blue-50 {
  background-color: hsl(210, 100%, 97%) !important;
}

.text-blue-700 {
  color: hsl(210, 100%, 25%) !important;
}

.bg-gray-50 {
  background-color: hsl(0, 0%, 98%) !important;
}

.text-gray-700 {
  color: hsl(0, 0%, 25%) !important;
}

/* Garantir contraste adequado para texto no modo claro */
.text-gray-900 {
  color: hsl(0, 0%, 10%) !important;
}

.text-gray-600 {
  color: hsl(0, 0%, 40%) !important;
}

/* Correções para componentes UI no modo claro */
.border-input {
  border-color: hsl(214.3, 31.8%, 75%) !important;
}

.dark .border-input {
  border-color: hsl(217.2, 32.6%, 25%) !important;
}

/* Melhorar visibilidade de selects no modo claro */
select option {
  background-color: white !important;
  color: hsl(222.2, 84%, 4.9%) !important;
}

.dark select option {
  background-color: hsl(222.2, 84%, 4.9%) !important;
  color: hsl(210, 40%, 98%) !important;
}

/* Garantir que badges tenham bom contraste */
.bg-muted {
  background-color: hsl(210, 40%, 96%) !important;
}

.dark .bg-muted {
  background-color: hsl(217.2, 32.6%, 17.5%) !important;
}

.text-muted-foreground {
  color: hsl(215.4, 16.3%, 46.9%) !important;
}

.dark .text-muted-foreground {
  color: hsl(215, 20.2%, 65.1%) !important;
}

/* Correções finais para modo claro */
.text-foreground {
  color: hsl(222.2, 84%, 4.9%) !important;
}

.dark .text-foreground {
  color: hsl(210, 40%, 98%) !important;
}

/* Garantir que botões tenham bom contraste */
.bg-primary {
  background-color: hsl(221.2, 83.2%, 53.3%) !important;
  color: white !important;
}

.dark .bg-primary {
  background-color: hsl(221.2, 83.2%, 53.3%) !important;
  color: white !important;
}

/* Melhorar visibilidade de bordas no modo claro */
.border {
  border-color: hsl(214.3, 31.8%, 85%) !important;
}

.dark .border {
  border-color: hsl(217.2, 32.6%, 25%) !important;
}

/* Garantir que dropdowns funcionem bem */
.bg-popover {
  background-color: white !important;
  border: 1px solid hsl(214.3, 31.8%, 85%) !important;
}

.dark .bg-popover {
  background-color: hsl(222.2, 84%, 4.9%) !important;
  border: 1px solid hsl(217.2, 32.6%, 25%) !important;
}

