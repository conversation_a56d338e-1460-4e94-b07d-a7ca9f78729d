/**
 * 🤝 ANALISADOR LEI 13.019/14 - MARC<PERSON> REGULATÓRIO DAS OSCs
 * <PERSON> das Organizações da Sociedade Civil
 */

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface ViolacaoLei13019 {
  artigo: string;
  descricao: string;
  gravidade: 'CRITICA' | 'ALTA' | 'MEDIA' | 'BAIXA';
  evidencia: string;
  sugestaoCorrecao: string;
  tipoViolacao: 'LEGAL' | 'PROCEDIMENTO' | 'DOCUMENTACAO' | 'FORMATO';
}

interface AnaliseLei13019 {
  conformeLei13019: boolean;
  scoreLei13019: number;
  violacoes: ViolacaoLei13019[];
  violacoesGraves: ViolacaoLei13019[];
  recomendacoesOSC: any[];
  artigosVerificados: string[];
  observacoesMarco: string[];
  aplicavelProcesso: boolean; // Se a lei se aplica ao processo
  tipoInstrumento: 'TERMO_COLABORACAO' | 'TERMO_FOMENTO' | 'ACORDO_COOPERACAO' | 'NAO_APLICAVEL';
}

export async function analisarConformidadeLei13019(documentos: DocumentoAnalise[]): Promise<AnaliseLei13019> {
  const violacoes: ViolacaoLei13019[] = [];
  const observacoesMarco: string[] = [];

  // Verificar se a lei se aplica ao processo
  const aplicabilidade = verificarAplicabilidadeLei13019(documentos);
  
  if (!aplicabilidade.aplicavel) {
    return {
      conformeLei13019: true,
      scoreLei13019: 100,
      violacoes: [],
      violacoesGraves: [],
      recomendacoesOSC: [],
      artigosVerificados: [],
      observacoesMarco: ['Lei 13.019/14 não se aplica a este processo'],
      aplicavelProcesso: false,
      tipoInstrumento: 'NAO_APLICAVEL'
    };
  }

  // Verificações por tipo de documento
  for (const doc of documentos) {
    switch (doc.tipo) {
      case 'etp':
        violacoes.push(...verificarETPLei13019(doc, aplicabilidade.tipoInstrumento));
        break;
      case 'edital':
        violacoes.push(...verificarEditalLei13019(doc, aplicabilidade.tipoInstrumento));
        break;
      case 'tr':
        violacoes.push(...verificarTRLei13019(doc, aplicabilidade.tipoInstrumento));
        break;
    }
  }

  // Verificações gerais do processo OSC
  violacoes.push(...verificarProcessoOSC(documentos, aplicabilidade.tipoInstrumento));

  const violacoesGraves = violacoes.filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA');
  const scoreLei13019 = calcularScoreLei13019(violacoes);
  const conformeLei13019 = violacoesGraves.length === 0 && scoreLei13019 >= 80;

  return {
    conformeLei13019,
    scoreLei13019,
    violacoes,
    violacoesGraves,
    recomendacoesOSC: gerarRecomendacoesOSC(violacoes),
    artigosVerificados: [
      'Art. 2º - Conceitos',
      'Art. 23 - Chamamento público',
      'Art. 27 - Edital',
      'Art. 35 - Termo de colaboração',
      'Art. 42 - Termo de fomento'
    ],
    observacoesMarco,
    aplicavelProcesso: true,
    tipoInstrumento: aplicabilidade.tipoInstrumento
  };
}

function verificarAplicabilidadeLei13019(documentos: DocumentoAnalise[]): {
  aplicavel: boolean;
  tipoInstrumento: 'TERMO_COLABORACAO' | 'TERMO_FOMENTO' | 'ACORDO_COOPERACAO' | 'NAO_APLICAVEL';
} {
  const conteudoCompleto = documentos.map(doc => doc.conteudo.toLowerCase()).join(' ');
  
  const termosOSC = [
    'organização da sociedade civil', 'organizacao da sociedade civil', 'osc',
    'termo de colaboração', 'termo de colaboracao',
    'termo de fomento', 'acordo de cooperação', 'acordo de cooperacao',
    'chamamento público', 'chamamento publico',
    'parceria', 'entidade sem fins lucrativos'
  ];

  const aplicavel = termosOSC.some(termo => conteudoCompleto.includes(termo));

  if (!aplicavel) {
    return { aplicavel: false, tipoInstrumento: 'NAO_APLICAVEL' };
  }

  // Determinar tipo de instrumento
  if (conteudoCompleto.includes('termo de colaboração') || conteudoCompleto.includes('termo de colaboracao')) {
    return { aplicavel: true, tipoInstrumento: 'TERMO_COLABORACAO' };
  }
  if (conteudoCompleto.includes('termo de fomento')) {
    return { aplicavel: true, tipoInstrumento: 'TERMO_FOMENTO' };
  }
  if (conteudoCompleto.includes('acordo de cooperação') || conteudoCompleto.includes('acordo de cooperacao')) {
    return { aplicavel: true, tipoInstrumento: 'ACORDO_COOPERACAO' };
  }

  return { aplicavel: true, tipoInstrumento: 'TERMO_COLABORACAO' }; // Default
}

function verificarETPLei13019(doc: DocumentoAnalise, tipoInstrumento: string): ViolacaoLei13019[] {
  const violacoes: ViolacaoLei13019[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Art. 2º - Conceitos fundamentais
  if (!conteudo.includes('interesse público') && !conteudo.includes('interesse publico')) {
    violacoes.push({
      artigo: 'Art. 2º, I',
      descricao: 'Ausência de caracterização do interesse público',
      gravidade: 'ALTA',
      evidencia: 'Interesse público não identificado',
      sugestaoCorrecao: 'Caracterizar claramente o interesse público da parceria',
      tipoViolacao: 'LEGAL'
    });
  }

  // Art. 5º - Diretrizes fundamentais
  if (!conteudo.includes('transparência') && !conteudo.includes('transparencia')) {
    violacoes.push({
      artigo: 'Art. 5º, I',
      descricao: 'Ausência de observância ao princípio da transparência',
      gravidade: 'MEDIA',
      evidencia: 'Transparência não mencionada',
      sugestaoCorrecao: 'Incluir diretrizes de transparência conforme Lei 13.019/14',
      tipoViolacao: 'LEGAL'
    });
  }

  // Verificar adequação do instrumento
  if (tipoInstrumento === 'TERMO_COLABORACAO') {
    if (!conteudo.includes('colaboração') && !conteudo.includes('colaboracao')) {
      violacoes.push({
        artigo: 'Art. 16',
        descricao: 'Instrumento inadequado para o tipo de parceria',
        gravidade: 'ALTA',
        evidencia: 'Termo de colaboração não caracterizado adequadamente',
        sugestaoCorrecao: 'Adequar ao regime de termo de colaboração',
        tipoViolacao: 'PROCEDIMENTO'
      });
    }
  }

  return violacoes;
}

function verificarEditalLei13019(doc: DocumentoAnalise, tipoInstrumento: string): ViolacaoLei13019[] {
  const violacoes: ViolacaoLei13019[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Art. 23 - Chamamento público obrigatório
  if (!conteudo.includes('chamamento público') && !conteudo.includes('chamamento publico')) {
    violacoes.push({
      artigo: 'Art. 23',
      descricao: 'Ausência de caracterização como chamamento público',
      gravidade: 'CRITICA',
      evidencia: 'Chamamento público não identificado',
      sugestaoCorrecao: 'Caracterizar adequadamente como chamamento público',
      tipoViolacao: 'LEGAL'
    });
  }

  // Art. 27 - Conteúdo mínimo do edital
  const elementosObrigatorios = [
    'descrição da realidade',
    'objeto da parceria',
    'metas a serem atingidas',
    'prazo de execução',
    'valor global',
    'critérios de seleção'
  ];

  elementosObrigatorios.forEach(elemento => {
    if (!conteudo.includes(elemento.replace('ç', 'c').replace('ã', 'a'))) {
      violacoes.push({
        artigo: 'Art. 27',
        descricao: `Ausência de elemento obrigatório: ${elemento}`,
        gravidade: 'ALTA',
        evidencia: `Elemento "${elemento}" não encontrado no edital`,
        sugestaoCorrecao: `Incluir ${elemento} conforme Art. 27 da Lei 13.019/14`,
        tipoViolacao: 'LEGAL'
      });
    }
  });

  // Art. 29 - Critérios de seleção
  if (!conteudo.includes('critério') && !conteudo.includes('criterio')) {
    violacoes.push({
      artigo: 'Art. 29',
      descricao: 'Ausência de critérios de seleção claros',
      gravidade: 'ALTA',
      evidencia: 'Critérios de seleção não identificados',
      sugestaoCorrecao: 'Estabelecer critérios de seleção conforme Art. 29',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  return violacoes;
}

function verificarTRLei13019(doc: DocumentoAnalise, tipoInstrumento: string): ViolacaoLei13019[] {
  const violacoes: ViolacaoLei13019[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Verificar especificações para OSC
  if (!conteudo.includes('meta') && !conteudo.includes('resultado')) {
    violacoes.push({
      artigo: 'Metas e Resultados',
      descricao: 'Ausência de metas e resultados esperados',
      gravidade: 'MEDIA',
      evidencia: 'Metas e resultados não identificados',
      sugestaoCorrecao: 'Detalhar metas e resultados esperados da parceria',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  return violacoes;
}

function verificarProcessoOSC(documentos: DocumentoAnalise[], tipoInstrumento: string): ViolacaoLei13019[] {
  const violacoes: ViolacaoLei13019[] = [];
  const conteudoCompleto = documentos.map(doc => doc.conteudo.toLowerCase()).join(' ');

  // Verificar coerência do tipo de instrumento
  const instrumentos = ['termo de colaboração', 'termo de fomento', 'acordo de cooperação'];
  const instrumentosEncontrados = instrumentos.filter(inst => 
    conteudoCompleto.includes(inst.replace('ç', 'c').replace('ã', 'a'))
  );

  if (instrumentosEncontrados.length > 1) {
    violacoes.push({
      artigo: 'Coerência Instrumental',
      descricao: 'Inconsistência entre tipos de instrumentos nos documentos',
      gravidade: 'ALTA',
      evidencia: `Múltiplos instrumentos identificados: ${instrumentosEncontrados.join(', ')}`,
      sugestaoCorrecao: 'Definir claramente um único tipo de instrumento',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  return violacoes;
}

function calcularScoreLei13019(violacoes: ViolacaoLei13019[]): number {
  let penalizacao = 0;
  
  violacoes.forEach(violacao => {
    switch (violacao.gravidade) {
      case 'CRITICA': penalizacao += 25; break;
      case 'ALTA': penalizacao += 15; break;
      case 'MEDIA': penalizacao += 8; break;
      case 'BAIXA': penalizacao += 3; break;
    }
  });

  return Math.max(0, 100 - penalizacao);
}

function gerarRecomendacoesOSC(violacoes: ViolacaoLei13019[]): any[] {
  return violacoes
    .filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA')
    .map(violacao => ({
      prioridade: violacao.gravidade === 'CRITICA' ? 'ALTA' : 'MEDIA',
      titulo: `Adequação Lei 13.019/14 - ${violacao.artigo}`,
      descricao: violacao.descricao,
      acoes: [violacao.sugestaoCorrecao],
      artigo: violacao.artigo,
      categoria: 'OSC'
    }));
}
