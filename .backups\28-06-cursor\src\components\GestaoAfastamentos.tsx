'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Calendar,
  Plus,
  AlertTriangle,
  Users,
  Clock,
  FileText,
  CheckCircle,
  XCircle,
  Edit,
  Trash2,
  BarChart3
} from 'lucide-react';
import { Usuario, PeriodoAfastamento } from '@/types/usuario';
import { analisarRedistribuicao, AnaliseRedistribuicao } from '@/lib/redistribuicaoUtils';

interface GestaoAfastamentosProps {
  usuario: Usuario;
  todosUsuarios: Usuario[];
  todosProcessos: any[]; // Tipo Processo
  onUpdateUsuario: (usuario: Usuario) => void;
}

export default function GestaoAfastamentos({
  usuario,
  todosUsuarios,
  todosProcessos,
  onUpdateUsuario
}: GestaoAfastamentosProps) {
  const [periodos, setPeriodos] = useState<PeriodoAfastamento[]>(usuario.periodos || []);
  const [showNovoModal, setShowNovoModal] = useState(false);
  const [showAnaliseModal, setShowAnaliseModal] = useState(false);
  const [analiseAtual, setAnaliseAtual] = useState<AnaliseRedistribuicao | null>(null);
  const [novoPeriodo, setNovoPeriodo] = useState<Partial<PeriodoAfastamento>>({
    tipo: 'ferias',
    status: 'agendado'
  });

  // Atualizar períodos quando usuário mudar
  useEffect(() => {
    setPeriodos(usuario.periodos || []);
  }, [usuario]);

  const handleSalvarPeriodo = () => {
    if (!novoPeriodo.dataInicio || !novoPeriodo.dataFim || !novoPeriodo.tipo) {
      alert('Preencha todos os campos obrigatórios');
      return;
    }

    const periodo: PeriodoAfastamento = {
      id: `periodo_${Date.now()}`,
      tipo: novoPeriodo.tipo as any,
      dataInicio: novoPeriodo.dataInicio,
      dataFim: novoPeriodo.dataFim,
      motivo: novoPeriodo.motivo || '',
      observacoes: novoPeriodo.observacoes || '',
      status: 'agendado',
      criadoPor: 'admin', // TODO: Pegar usuário atual
      criadoEm: new Date().toISOString()
    };

    const novosPeriodos = [...periodos, periodo];
    setPeriodos(novosPeriodos);
    
    // Atualizar usuário
    const usuarioAtualizado = {
      ...usuario,
      periodos: novosPeriodos
    };
    onUpdateUsuario(usuarioAtualizado);

    // Limpar formulário
    setNovoPeriodo({
      tipo: 'ferias',
      status: 'agendado'
    });
    setShowNovoModal(false);
  };

  const handleAnalisarRedistribuicao = (periodo: PeriodoAfastamento) => {
    const analise = analisarRedistribuicao(usuario, periodo, todosProcessos, todosUsuarios);
    setAnaliseAtual(analise);
    setShowAnaliseModal(true);
  };

  const handleRemoverPeriodo = (periodoId: string) => {
    if (confirm('Tem certeza que deseja remover este período?')) {
      const novosPeriodos = periodos.filter(p => p.id !== periodoId);
      setPeriodos(novosPeriodos);
      
      const usuarioAtualizado = {
        ...usuario,
        periodos: novosPeriodos
      };
      onUpdateUsuario(usuarioAtualizado);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'agendado': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ativo': return 'bg-green-100 text-green-800 border-green-200';
      case 'concluido': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelado': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'ferias': return '🏖️';
      case 'licenca': return '📋';
      case 'afastamento': return '🚫';
      case 'atestado': return '🏥';
      case 'capacitacao': return '📚';
      default: return '📅';
    }
  };

  const formatarData = (data: string) => {
    return new Date(data).toLocaleDateString('pt-BR');
  };

  const calcularDias = (inicio: string, fim: string) => {
    const dataInicio = new Date(inicio);
    const dataFim = new Date(fim);
    const diffTime = Math.abs(dataFim.getTime() - dataInicio.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Períodos de Afastamento</h3>
          <p className="text-sm text-muted-foreground">
            Gestão de férias, licenças e afastamentos de {usuario.nome}
          </p>
        </div>
        
        <Dialog open={showNovoModal} onOpenChange={setShowNovoModal}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Novo Período
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Cadastrar Período de Afastamento</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="tipo">Tipo de Afastamento</Label>
                <Select 
                  value={novoPeriodo.tipo} 
                  onValueChange={(value) => setNovoPeriodo({...novoPeriodo, tipo: value as any})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ferias">🏖️ Férias</SelectItem>
                    <SelectItem value="licenca">📋 Licença</SelectItem>
                    <SelectItem value="afastamento">🚫 Afastamento</SelectItem>
                    <SelectItem value="atestado">🏥 Atestado Médico</SelectItem>
                    <SelectItem value="capacitacao">📚 Capacitação</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dataInicio">Data Início</Label>
                  <Input
                    id="dataInicio"
                    type="date"
                    value={novoPeriodo.dataInicio || ''}
                    onChange={(e) => setNovoPeriodo({...novoPeriodo, dataInicio: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="dataFim">Data Fim</Label>
                  <Input
                    id="dataFim"
                    type="date"
                    value={novoPeriodo.dataFim || ''}
                    onChange={(e) => setNovoPeriodo({...novoPeriodo, dataFim: e.target.value})}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="motivo">Motivo</Label>
                <Input
                  id="motivo"
                  placeholder="Ex: Férias anuais, Licença médica..."
                  value={novoPeriodo.motivo || ''}
                  onChange={(e) => setNovoPeriodo({...novoPeriodo, motivo: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="observacoes">Observações</Label>
                <Textarea
                  id="observacoes"
                  placeholder="Informações adicionais..."
                  value={novoPeriodo.observacoes || ''}
                  onChange={(e) => setNovoPeriodo({...novoPeriodo, observacoes: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowNovoModal(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleSalvarPeriodo}>
                  Salvar Período
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Lista de Períodos */}
      <div className="space-y-4">
        {periodos.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                Nenhum período cadastrado
              </h3>
              <p className="text-muted-foreground mb-4">
                Cadastre períodos de férias, licenças e afastamentos para melhor gestão da equipe.
              </p>
              <Button onClick={() => setShowNovoModal(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Cadastrar Primeiro Período
              </Button>
            </CardContent>
          </Card>
        ) : (
          periodos.map((periodo) => (
            <Card key={periodo.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getTipoIcon(periodo.tipo)}
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-foreground capitalize">
                          {periodo.tipo}
                        </h4>
                        <Badge className={getStatusColor(periodo.status)}>
                          {periodo.status}
                        </Badge>
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          {formatarData(periodo.dataInicio)} até {formatarData(periodo.dataFim)}
                          <span className="ml-2">
                            ({calcularDias(periodo.dataInicio, periodo.dataFim)} dias)
                          </span>
                        </span>
                      </div>
                      
                      {periodo.motivo && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {periodo.motivo}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {periodo.status === 'agendado' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAnalisarRedistribuicao(periodo)}
                      >
                        <BarChart3 className="mr-1 h-3 w-3" />
                        Analisar
                      </Button>
                    )}
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRemoverPeriodo(periodo.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Modal de Análise de Redistribuição */}
      <Dialog open={showAnaliseModal} onOpenChange={setShowAnaliseModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Análise de Redistribuição de Processos</DialogTitle>
          </DialogHeader>
          
          {analiseAtual && (
            <div className="space-y-6">
              {/* Estatísticas */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <FileText className="mx-auto h-8 w-8 text-blue-600 mb-2" />
                    <p className="text-2xl font-bold text-blue-600">
                      {analiseAtual.estatisticas.totalProcessos}
                    </p>
                    <p className="text-sm text-muted-foreground">Processos Afetados</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <Clock className="mx-auto h-8 w-8 text-orange-600 mb-2" />
                    <p className="text-2xl font-bold text-orange-600">
                      {analiseAtual.estatisticas.diasAfastamento}
                    </p>
                    <p className="text-sm text-muted-foreground">Dias de Afastamento</p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4 text-center">
                    <AlertTriangle className="mx-auto h-8 w-8 text-red-600 mb-2" />
                    <p className="text-2xl font-bold text-red-600">
                      {analiseAtual.alertas.length}
                    </p>
                    <p className="text-sm text-muted-foreground">Alertas</p>
                  </CardContent>
                </Card>
              </div>

              {/* Alertas */}
              {analiseAtual.alertas.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">⚠️ Alertas Importantes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {analiseAtual.alertas.map((alerta, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border ${
                            alerta.severidade === 'critica' ? 'bg-red-50 border-red-200 text-red-800' :
                            alerta.severidade === 'alta' ? 'bg-orange-50 border-orange-200 text-orange-800' :
                            'bg-yellow-50 border-yellow-200 text-yellow-800'
                          }`}
                        >
                          <p className="text-sm font-medium">{alerta.mensagem}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Sugestões de Redistribuição */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">📋 Sugestões de Redistribuição</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analiseAtual.sugestoes.map((sugestao, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex-1">
                          <p className="font-medium text-sm">
                            Processo {sugestao.processoNumero}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {sugestao.usuarioAtual} → {sugestao.usuarioSugerido}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {sugestao.motivo}
                          </p>
                        </div>
                        
                        <Badge
                          className={
                            sugestao.prioridade === 'alta' ? 'bg-red-100 text-red-800' :
                            sugestao.prioridade === 'media' ? 'bg-orange-100 text-orange-800' :
                            'bg-green-100 text-green-800'
                          }
                        >
                          {sugestao.prioridade}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowAnaliseModal(false)}>
                  Fechar
                </Button>
                <Button>
                  Aplicar Redistribuição
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
