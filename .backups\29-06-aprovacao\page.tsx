'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from 'next-themes';
import { getTermosDeUsoAtivos, registrarAceitacaoTermos, verificarAceitacaoTermos } from '@/lib/termosDeUso';
import {
  LogIn,
  Building,
  Shield,
  AlertCircle,
  CheckCircle,
  Sun,
  Moon,
  FileText,
  X,
  Loader2,
  ExternalLink
} from 'lucide-react';

export default function LoginPage() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const { login, loading, user } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [error, setError] = useState('');
  const [showTerms, setShowTerms] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [termosDeUso, setTermosDeUso] = useState<any>(null);
  const [loadingTermos, setLoadingTermos] = useState(true);
  const [checkingTerms, setCheckingTerms] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Carregar termos de uso
    carregarTermosDeUso();
  }, []);

  // Novo useEffect para verificar termos quando usuário logar
  useEffect(() => {
    if (user && !checkingTerms) {
      verificarTermosAposLogin();
    }
  }, [user]);

  const carregarTermosDeUso = async () => {
    try {
      setLoadingTermos(true);
      const termos = await getTermosDeUsoAtivos();
      setTermosDeUso(termos);
    } catch (error) {
      console.error('❌ Erro ao carregar termos:', error);
      setError('Erro ao carregar termos de uso');
    } finally {
      setLoadingTermos(false);
    }
  };

  // Nova função para verificar termos após login
  const verificarTermosAposLogin = async () => {
    if (!user || !termosDeUso) return;
    
    try {
      setCheckingTerms(true);
      console.log('🔍 Verificando se usuário aceitou os termos...');
      
      const jaAceitou = await verificarAceitacaoTermos(user.uid, termosDeUso.versao);
      console.log('✅ Usuário já aceitou os termos:', jaAceitou);
      
      if (jaAceitou) {
        // Usuário já aceitou, redirecionar para dashboard
        console.log('✅ Usuário já aceitou os termos, redirecionando...');
        router.push('/dashboard');
      } else {
        // Usuário não aceitou, mostrar modal
        console.log('⚠️ Usuário não aceitou os termos, mostrando modal...');
        setShowTerms(true);
      }
    } catch (error) {
      console.error('❌ Erro ao verificar aceitação dos termos:', error);
      setError('Erro ao verificar aceitação dos termos');
    } finally {
      setCheckingTerms(false);
    }
  };

  const handleGoogleLogin = async () => {
    console.log('🟦 Botão de login clicado');
    setError('');
    setLoginAttempts(prev => prev + 1);

    try {
      await login();
      console.log('✅ Login realizado com sucesso');
      // A verificação dos termos será feita no useEffect quando user mudar
    } catch (err: any) {
      console.error('❌ Erro no login:', err);
      setError(err.message || 'Erro ao fazer login com Google');
    }
  };

  const handleTermsAccept = async () => {
    try {
      if (!user || !termosDeUso) {
        throw new Error('Usuário ou termos não disponíveis');
      }

      console.log('📝 Registrando aceitação dos termos...');
      
      // Registrar aceitação no backend
      await registrarAceitacaoTermos(
        user.uid,
        user.email || '',
        termosDeUso.versao,
        undefined, // IP será capturado pelo backend
        navigator.userAgent
      );

      console.log('✅ Aceitação registrada com sucesso');
      setTermsAccepted(true);
      setShowTerms(false);
      
      // Redirecionar para dashboard
      router.push('/dashboard');
    } catch (error: any) {
      console.error('❌ Erro ao aceitar termos:', error);
      setError('Erro ao registrar aceitação dos termos');
    }
  };

  // Se estiver verificando termos, mostrar loading
  if (checkingTerms) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-900 dark:to-black flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-slate-600 dark:text-slate-300">
            Verificando permissões...
          </p>
        </div>
      </div>
    );
  }

  if (!mounted || loadingTermos) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-900 dark:to-black flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-slate-600 dark:text-slate-300">
            {loadingTermos ? 'Carregando termos de uso...' : 'Carregando...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-black flex items-center justify-center p-4 relative">
      {/* Botão Dark Mode */}
      <div className="absolute top-4 right-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          className="bg-white/80 border-slate-200 hover:bg-white text-slate-700 dark:bg-slate-800/80 dark:border-slate-600 dark:hover:bg-slate-700 dark:text-white backdrop-blur-sm"
        >
          {theme === 'dark' ? (
            <Sun className="h-4 w-4" />
          ) : (
            <Moon className="h-4 w-4" />
          )}
        </Button>
      </div>

      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-3 bg-white rounded-xl shadow-lg dark:bg-slate-800">
              <Building className="h-10 w-10 text-slate-700 dark:text-slate-300" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-slate-800 dark:text-white">InovaProcess</h1>
              <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
                Sistema de Gestão de Processos
              </p>
            </div>
          </div>
          
          {/* Status do sistema */}
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">Sistema Online</span>
          </div>
        </div>

        {/* Modal de Termos e Condições */}
        {showTerms && termosDeUso && user && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-2xl max-h-[80vh] flex flex-col shadow-2xl bg-white dark:bg-slate-800">
              <CardHeader className="bg-blue-600 dark:bg-blue-700 text-white flex-shrink-0">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    {termosDeUso.titulo}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowTerms(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-6 overflow-y-auto flex-1 min-h-0">
                <div className="space-y-6 text-sm text-slate-700 dark:text-slate-300">
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <div dangerouslySetInnerHTML={{ 
                      __html: termosDeUso.conteudo.replace(/\n/g, '<br>').replace(/#{1,6}\s+(.+)/g, '<h3 class=\"font-bold text-lg text-slate-800 dark:text-slate-200 mb-3\">$1</h3>')
                    }} />
                  </div>
                </div>
              </CardContent>
              <div className="p-6 border-t border-gray-200 dark:border-slate-600 flex-shrink-0 bg-transparent dark:bg-transparent">
                <div className="flex items-center space-x-4 mb-4 p-4 bg-transparent dark:bg-transparent rounded-lg">
                  <label className="flex items-center space-x-3 cursor-pointer w-full">
                    <input
                      type="checkbox"
                      checked={termsAccepted}
                      onChange={(e) => setTermsAccepted(e.target.checked)}
                      className="w-5 h-5 text-blue-600 bg-white border-2 border-blue-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-slate-700 dark:border-blue-500"
                    />
                    <span className="text-base font-semibold text-slate-900 dark:text-white">
                      Li e aceito os termos de uso e política de privacidade
                    </span>
                  </label>
                </div>
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowTerms(false)}
                    className="px-6 py-2"
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={handleTermsAccept}
                    disabled={!termsAccepted}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Aceitar e Continuar
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Login com Google */}
        <Card className="shadow-xl bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
          <CardHeader className="space-y-1 bg-blue-600 dark:bg-blue-700 text-white rounded-t-lg">
            <CardTitle className="text-xl text-center flex items-center justify-center">
              <LogIn className="mr-2 h-5 w-5" />
              Entrar no Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-800 dark:text-red-200">{error}</AlertDescription>
                </Alert>
              )}

              {/* Informações de segurança */}
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-gray-900 dark:text-white">
                    <p className="font-medium mb-1">
                      🔐 Login Seguro com Google
                    </p>
                    <p>
                      Apenas contas Gmail são permitidas. Seus dados estão protegidos pela autenticação do Google.
                    </p>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => {
                  console.log('🟦 Clique detectado no botão Entrar com Google');
                  handleGoogleLogin();
                }}
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 hover:border-blue-400 shadow-md hover:shadow-lg transition-all duration-200 h-12 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <span className="font-medium">Conectando...</span>
                  </>
                ) : (
                  <>
                    <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span className="font-medium">Entrar com Google</span>
                  </>
                )}
              </Button>

              {/* Dicas de solução de problemas */}
              {loginAttempts > 0 && (
                <div className="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                  <p className="font-medium">💡 Dicas:</p>
                  <ul className="list-disc list-inside space-y-1 ml-2">
                    <li>Permita popups para este site</li>
                    <li>Use uma conta Gmail válida</li>
                    <li>Verifique sua conexão com a internet</li>
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <div className="bg-white/80 dark:bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200 dark:border-white/20">
            <p className="text-slate-700 dark:text-white font-medium">InovaProcess © 2025</p>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Sistema de Gestão de Processos Administrativos
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
