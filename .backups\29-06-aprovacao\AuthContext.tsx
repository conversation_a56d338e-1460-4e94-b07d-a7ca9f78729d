'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { useAuthState, signInWithGoogle, logoutUser, isValidGmailUser, initializeFirestoreCollections } from '@/lib/firebase';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  error: string | null;
  clearError: () => void;
  userProfile: any | null;
  updateUserProfile: (data: any) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);

  useEffect(() => {
    // Modo produção - Firebase real (sem DEV_MODE)
    const unsubscribe = useAuthState((user) => {
      console.log('🔄 Estado de autenticação alterado:', user?.email || 'Nenhum usuário');
      
      // Verificar se é Gmail válido
      if (user && !isValidGmailUser(user)) {
        console.warn('⚠️ Usuário não é Gmail válido, fazendo logout');
        logoutUser();
        setUser(null);
        setUserProfile(null);
        setError('Apenas contas Gmail são permitidas');
      } else {
        setUser(user);
        if (user) {
          // Carregar perfil do usuário
          loadUserProfile(user);
        } else {
          setUserProfile(null);
        }
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const loadUserProfile = async (user: User) => {
    try {
      // Aqui você pode carregar dados adicionais do usuário do Firestore
      // Por enquanto, vamos criar um perfil básico
      const profile = {
        uid: user.uid,
        email: user.email,
        nome: user.displayName || user.email?.split('@')[0] || 'Usuário',
        foto: user.photoURL,
        ultimoAcesso: new Date().toISOString(),
        perfil: 'usuario', // Pode ser 'admin', 'usuario', etc.
        permissoes: ['dashboard.view', 'processos.view'], // Permissões básicas
        status: 'ativo'
      };
      
      setUserProfile(profile);
      console.log('✅ Perfil do usuário carregado:', profile.nome);
    } catch (error) {
      console.error('❌ Erro ao carregar perfil:', error);
      setError('Erro ao carregar perfil do usuário');
    }
  };

  const login = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Iniciando processo de login...');
      const user = await signInWithGoogle();
      
      if (!isValidGmailUser(user)) {
        await logoutUser();
        throw new Error('Apenas contas Gmail são permitidas');
      }
      
      setUser(user);
      console.log('✅ Login realizado com sucesso:', user.email);
    } catch (error: any) {
      console.error('❌ Erro no login:', error);
      setError(error.message || 'Erro desconhecido no login');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Iniciando logout...');
      await logoutUser();
      setUser(null);
      setUserProfile(null);
      console.log('✅ Logout realizado com sucesso');
    } catch (error: any) {
      console.error('❌ Erro no logout:', error);
      setError(error.message || 'Erro desconhecido no logout');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const updateUserProfile = async (data: any) => {
    try {
      if (!user) {
        throw new Error('Usuário não autenticado');
      }
      
      // Aqui você pode atualizar o perfil no Firestore
      const updatedProfile = { ...userProfile, ...data };
      setUserProfile(updatedProfile);
      
      console.log('✅ Perfil atualizado:', updatedProfile);
    } catch (error: any) {
      console.error('❌ Erro ao atualizar perfil:', error);
      setError(error.message || 'Erro ao atualizar perfil');
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
    error,
    clearError,
    userProfile,
    updateUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
