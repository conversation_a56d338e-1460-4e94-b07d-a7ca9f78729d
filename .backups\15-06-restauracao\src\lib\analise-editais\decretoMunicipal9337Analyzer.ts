// Analisador de conformidade com o Decreto Municipal 9337/2024 - Prefeitura de Mauá

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface ViolacaoDecreto {
  artigo: string;
  descricao: string;
  gravidade: 'BAIXA' | 'MEDIA' | 'ALTA' | 'CRITICA';
  evidencia: string;
  sugestaoCorrecao: string;
  tipoViolacao: 'PROCEDIMENTO' | 'DOCUMENTACAO' | 'PRAZO' | 'FORMATO' | 'CONTEUDO';
}

interface AnaliseDecreto9337 {
  conformeDecreto: boolean;
  scoreDecreto: number;
  violacoes: ViolacaoDecreto[];
  violacoesGraves: ViolacaoDecreto[];
  recomendacoesMunicipais: any[];
  artigosVerificados: string[];
  observacoesMaua: string[];
}

export async function analisarConformidadeDecreto9337(documentos: DocumentoAnalise[]): Promise<AnaliseDecreto9337> {
  const violacoes: ViolacaoDecreto[] = [];
  const observacoesMaua: string[] = [];

  // Verificações por tipo de documento
  for (const doc of documentos) {
    switch (doc.tipo) {
      case 'etp':
        violacoes.push(...verificarETPDecreto9337(doc));
        break;
      case 'edital':
        violacoes.push(...verificarEditalDecreto9337(doc));
        break;
      case 'tr':
        violacoes.push(...verificarTRDecreto9337(doc));
        break;
    }
  }

  // Verificações gerais do processo conforme padrão Mauá
  violacoes.push(...verificarPadroesGeraisMaua(documentos));

  const violacoesGraves = violacoes.filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA');
  const scoreDecreto = calcularScoreDecreto(violacoes);
  const conformeDecreto = violacoesGraves.length === 0 && scoreDecreto >= 80;

  return {
    conformeDecreto,
    scoreDecreto,
    violacoes,
    violacoesGraves,
    recomendacoesMunicipais: gerarRecomendacoesMunicipais(violacoes),
    artigosVerificados: [
      'Art. 1º - Disposições Gerais',
      'Art. 2º - Procedimentos',
      'Art. 3º - Documentação',
      'Art. 4º - Prazos',
      'Art. 5º - Responsabilidades'
    ],
    observacoesMaua
  };
}

function verificarETPDecreto9337(doc: DocumentoAnalise): ViolacaoDecreto[] {
  const violacoes: ViolacaoDecreto[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // DECRETO MUNICIPAL 9337/2024 - REGULA LICITAÇÕES E CONTRATOS
  // Verificações específicas baseadas em decretos municipais de licitação:

  // Art. 1º - Identificação da Prefeitura de Mauá
  if (!conteudo.includes('mauá') && !conteudo.includes('prefeitura municipal')) {
    violacoes.push({
      artigo: 'Art. 1º - Identificação Municipal',
      descricao: 'Ausência de identificação clara da Prefeitura de Mauá',
      gravidade: 'ALTA',
      evidencia: 'Identificação municipal não encontrada no documento',
      sugestaoCorrecao: 'Incluir identificação completa: "Prefeitura Municipal de Mauá"',
      tipoViolacao: 'FORMATO'
    });
  }

  // Art. 2º - Competência da CLMP
  if (!conteudo.includes('clmp') && !conteudo.includes('coordenadoria de licitações')) {
    violacoes.push({
      artigo: 'Art. 2º - Competência CLMP',
      descricao: 'Ausência de referência à competência da CLMP',
      gravidade: 'ALTA',
      evidencia: 'CLMP não mencionada como órgão competente',
      sugestaoCorrecao: 'Incluir referência à CLMP - Coordenadoria de Licitações, Materiais e Patrimônio',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  // Art. 3º - Referência ao Decreto Municipal
  if (!conteudo.includes('decreto') && !conteudo.includes('9337')) {
    violacoes.push({
      artigo: 'Art. 3º - Decreto Municipal 9337/2024',
      descricao: 'Ausência de referência ao decreto municipal regulamentador',
      gravidade: 'CRITICA',
      evidencia: 'Decreto Municipal 9337/2024 não referenciado',
      sugestaoCorrecao: 'Incluir referência expressa ao Decreto Municipal 9337/2024',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  // Art. 4º - Procedimentos de Licitação
  if (!conteudo.includes('procedimento') && !conteudo.includes('tramitação')) {
    violacoes.push({
      artigo: 'Art. 4º - Procedimentos',
      descricao: 'Ausência de definição de procedimentos conforme decreto',
      gravidade: 'MEDIA',
      evidencia: 'Procedimentos de tramitação não especificados',
      sugestaoCorrecao: 'Definir procedimentos conforme Decreto Municipal 9337/2024',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  // Art. 5º - Documentação Obrigatória
  const documentosObrigatorios = ['cnpj', 'endereço', 'telefone', 'email'];
  const documentosEncontrados = documentosObrigatorios.filter(doc =>
    conteudo.includes(doc)
  );

  if (documentosEncontrados.length < 3) {
    violacoes.push({
      artigo: 'Art. 5º - Documentação Obrigatória',
      descricao: 'Documentação municipal incompleta',
      gravidade: 'ALTA',
      evidencia: `Apenas ${documentosEncontrados.length}/4 elementos obrigatórios encontrados`,
      sugestaoCorrecao: 'Incluir CNPJ, endereço completo, telefone e email da Prefeitura',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  return violacoes;
}

function verificarEditalDecreto9337(doc: DocumentoAnalise): ViolacaoDecreto[] {
  const violacoes: ViolacaoDecreto[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // PLACEHOLDER - Aguardando detalhes específicos do Decreto 9337/2024
  
  // Verificações padrão para editais municipais
  if (!conteudo.includes('prefeitura municipal de mauá')) {
    violacoes.push({
      artigo: 'Identificação do Órgão',
      descricao: 'Identificação inadequada do órgão licitante',
      gravidade: 'ALTA',
      evidencia: 'Prefeitura Municipal de Mauá não claramente identificada',
      sugestaoCorrecao: 'Incluir identificação completa: Prefeitura Municipal de Mauá',
      tipoViolacao: 'FORMATO'
    });
  }

  // Verificar se segue modelo padrão municipal
  const elementosPadraoMaua = ['cnpj', 'endereço', 'telefone', 'email'];
  const elementosEncontrados = elementosPadraoMaua.filter(elemento => 
    conteudo.includes(elemento)
  );

  if (elementosEncontrados.length < 2) {
    violacoes.push({
      artigo: 'Padrão Municipal',
      descricao: 'Edital não segue padrão municipal completo',
      gravidade: 'MEDIA',
      evidencia: `Apenas ${elementosEncontrados.length}/4 elementos padrão encontrados`,
      sugestaoCorrecao: 'Incluir todos os elementos do padrão municipal (CNPJ, endereço, telefone, email)',
      tipoViolacao: 'FORMATO'
    });
  }

  return violacoes;
}

function verificarTRDecreto9337(doc: DocumentoAnalise): ViolacaoDecreto[] {
  const violacoes: ViolacaoDecreto[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // PLACEHOLDER - Aguardando detalhes específicos do Decreto 9337/2024

  // Verificações padrão para TR municipal
  if (!conteudo.includes('secretaria') && !conteudo.includes('departamento')) {
    violacoes.push({
      artigo: 'Identificação do Solicitante',
      descricao: 'Secretaria/Departamento solicitante não identificado',
      gravidade: 'MEDIA',
      evidencia: 'Órgão solicitante não claramente identificado',
      sugestaoCorrecao: 'Identificar claramente a secretaria/departamento solicitante',
      tipoViolacao: 'DOCUMENTACAO'
    });
  }

  // Verificar responsável técnico
  if (!conteudo.includes('responsável técnico') && !conteudo.includes('gestor')) {
    violacoes.push({
      artigo: 'Responsabilidade Técnica',
      descricao: 'Responsável técnico não identificado',
      gravidade: 'ALTA',
      evidencia: 'Responsável técnico ausente',
      sugestaoCorrecao: 'Identificar responsável técnico conforme padrão municipal',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  return violacoes;
}

function verificarPadroesGeraisMaua(documentos: DocumentoAnalise[]): ViolacaoDecreto[] {
  const violacoes: ViolacaoDecreto[] = [];

  // Verificar coerência entre documentos conforme padrão municipal
  const secretarias = documentos.map(doc => extrairSecretaria(doc.conteudo));
  const secretariasUnicas = [...new Set(secretarias.filter(s => s))];

  if (secretariasUnicas.length > 1) {
    violacoes.push({
      artigo: 'Coerência Municipal',
      descricao: 'Inconsistência entre secretarias nos documentos',
      gravidade: 'ALTA',
      evidencia: `Secretarias diferentes: ${secretariasUnicas.join(', ')}`,
      sugestaoCorrecao: 'Alinhar identificação da secretaria em todos os documentos',
      tipoViolacao: 'PROCEDIMENTO'
    });
  }

  // Verificar se todos os documentos seguem padrão municipal
  const documentosSemPadrao = documentos.filter(doc => 
    !doc.conteudo.toLowerCase().includes('mauá')
  );

  if (documentosSemPadrao.length > 0) {
    violacoes.push({
      artigo: 'Padrão Municipal Geral',
      descricao: 'Documentos não seguem padrão municipal',
      gravidade: 'MEDIA',
      evidencia: `${documentosSemPadrao.length} documento(s) sem padrão municipal`,
      sugestaoCorrecao: 'Adequar todos os documentos ao padrão da Prefeitura de Mauá',
      tipoViolacao: 'FORMATO'
    });
  }

  return violacoes;
}

function extrairSecretaria(conteudo: string): string | null {
  const secretariasComuns = [
    'secretaria de saúde',
    'secretaria de educação',
    'secretaria de infraestrutura',
    'secretaria de agricultura',
    'secretaria de meio ambiente',
    'secretaria de assistência social'
  ];

  const conteudoLower = conteudo.toLowerCase();
  
  for (const secretaria of secretariasComuns) {
    if (conteudoLower.includes(secretaria)) {
      return secretaria;
    }
  }

  return null;
}

function calcularScoreDecreto(violacoes: ViolacaoDecreto[]): number {
  let penalizacao = 0;
  
  violacoes.forEach(violacao => {
    switch (violacao.gravidade) {
      case 'CRITICA': penalizacao += 20; break;
      case 'ALTA': penalizacao += 12; break;
      case 'MEDIA': penalizacao += 6; break;
      case 'BAIXA': penalizacao += 2; break;
    }
  });

  return Math.max(0, 100 - penalizacao);
}

function gerarRecomendacoesMunicipais(violacoes: ViolacaoDecreto[]): any[] {
  return violacoes
    .filter(v => v.gravidade === 'ALTA' || v.gravidade === 'CRITICA')
    .map(violacao => ({
      prioridade: violacao.gravidade === 'CRITICA' ? 'ALTA' : 'MEDIA',
      titulo: `Adequação Municipal - ${violacao.artigo}`,
      descricao: violacao.descricao,
      acoes: [violacao.sugestaoCorrecao],
      artigo: violacao.artigo,
      tipo: violacao.tipoViolacao,
      origem: 'Decreto Municipal 9337/2024'
    }));
}

// Função para atualizar com detalhes específicos do decreto
export function configurarDecreto9337(detalhesDecreto: any): void {
  // Esta função será usada para configurar as verificações específicas
  // quando os detalhes do Decreto 9337/2024 forem fornecidos
  console.log('Configurando verificações específicas do Decreto 9337/2024:', detalhesDecreto);
}
