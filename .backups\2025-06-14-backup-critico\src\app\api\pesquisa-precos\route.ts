import { NextRequest, NextResponse } from 'next/server';

interface PesquisaPreco {
  id: string;
  item: string;
  categoria: string;
  precoMedio: number;
  menorPreco: number;
  maiorPreco: number;
  orgao: string;
  dataLicitacao: string;
  modalidade: string;
  status: string;
  uf: string;
  cidade: string;
  numeroLicitacao?: string;
  fornecedor?: string;
  unidade?: string;
  quantidade?: number;
  observacoes?: string;
}

interface HistoricoPesquisa {
  id: string;
  termo: string;
  dataConsulta: string;
  resultados: number;
  usuario: string;
  fonte: 'PNCP' | 'MANUAL' | 'IMPORTACAO';
}

// Mock data - Período: 01/05/25 até 06/06/25
const mockResultados: PesquisaPreco[] = [
  {
    id: '1',
    item: 'Notebook Dell Inspiron 15 3000',
    categoria: 'Equipamentos de Informática',
    precoMedio: 2850.00,
    menorPreco: 2650.00,
    maiorPreco: 3200.00,
    orgao: 'Prefeitura de São Paulo',
    dataLicitacao: '15/05/25',
    modalidade: 'Pregão Eletrônico',
    status: 'Homologado',
    uf: 'SP',
    cidade: 'São Paulo',
    numeroLicitacao: 'PE 045/2025',
    fornecedor: 'TechSolutions Ltda',
    unidade: 'UN',
    quantidade: 50,
    observacoes: 'Inclui garantia de 2 anos'
  },
  {
    id: '2',
    item: 'Papel A4 75g Resma 500 folhas',
    categoria: 'Material de Escritório',
    precoMedio: 18.50,
    menorPreco: 16.80,
    maiorPreco: 22.00,
    orgao: 'Prefeitura de Campinas',
    dataLicitacao: '28/05/25',
    modalidade: 'Pregão Presencial',
    status: 'Em Andamento',
    uf: 'SP',
    cidade: 'Campinas',
    numeroLicitacao: 'PP 012/2025',
    fornecedor: 'Papelaria Central',
    unidade: 'RESMA',
    quantidade: 1000,
    observacoes: 'Papel reciclado'
  },
  {
    id: '3',
    item: 'Medicamento Dipirona 500mg',
    categoria: 'Medicamentos',
    precoMedio: 0.85,
    menorPreco: 0.75,
    maiorPreco: 1.20,
    orgao: 'Secretaria de Saúde - RJ',
    dataLicitacao: '10/05/25',
    modalidade: 'Pregão Eletrônico',
    status: 'Homologado',
    uf: 'RJ',
    cidade: 'Rio de Janeiro',
    numeroLicitacao: 'PE 089/2025',
    fornecedor: 'Farmacêutica Brasil',
    unidade: 'COMPRIMIDO',
    quantidade: 100000,
    observacoes: 'Registro ANVISA válido'
  },
  {
    id: '4',
    item: 'Combustível Gasolina Comum',
    categoria: 'Combustíveis',
    precoMedio: 5.45,
    menorPreco: 5.20,
    maiorPreco: 5.80,
    orgao: 'Prefeitura de Belo Horizonte',
    dataLicitacao: '02/06/25',
    modalidade: 'Pregão Eletrônico',
    status: 'Vigente',
    uf: 'MG',
    cidade: 'Belo Horizonte',
    numeroLicitacao: 'PE 156/2025',
    fornecedor: 'Posto Central',
    unidade: 'LITRO',
    quantidade: 50000,
    observacoes: 'Fornecimento por 12 meses'
  },
  {
    id: '5',
    item: 'Serviços de Limpeza Urbana',
    categoria: 'Serviços',
    precoMedio: 125000.00,
    menorPreco: 98000.00,
    maiorPreco: 150000.00,
    orgao: 'Prefeitura de Salvador',
    dataLicitacao: '25/05/25',
    modalidade: 'Concorrência',
    status: 'Homologado',
    uf: 'BA',
    cidade: 'Salvador',
    numeroLicitacao: 'CC 008/2025',
    fornecedor: 'Limpa Cidade S.A.',
    unidade: 'MENSAL',
    quantidade: 12,
    observacoes: 'Inclui coleta seletiva'
  }
];

const mockHistorico: HistoricoPesquisa[] = [
  {
    id: '1',
    termo: 'notebook',
    dataConsulta: '06/06/25',
    resultados: 15,
    usuario: 'Marcos Isidoro',
    fonte: 'PNCP'
  },
  {
    id: '2',
    termo: 'medicamentos',
    dataConsulta: '05/06/25',
    resultados: 8,
    usuario: 'Maria Silva',
    fonte: 'PNCP'
  },
  {
    id: '3',
    termo: 'papel a4',
    dataConsulta: '04/06/25',
    resultados: 12,
    usuario: 'João Santos',
    fonte: 'PNCP'
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'search';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const categoria = searchParams.get('categoria') || '';
    const uf = searchParams.get('uf') || '';
    const modalidade = searchParams.get('modalidade') || '';

    if (action === 'historico') {
      return NextResponse.json({
        success: true,
        data: {
          historico: mockHistorico,
          stats: {
            totalPesquisas: mockHistorico.length,
            pesquisasHoje: mockHistorico.filter(h => h.dataConsulta === '06/06/25').length,
            economiaEstimada: 45200.00
          }
        }
      });
    }

    if (action === 'categorias') {
      const categorias = [...new Set(mockResultados.map(r => r.categoria))];
      return NextResponse.json({
        success: true,
        data: { categorias }
      });
    }

    // Busca principal
    let filteredResultados = mockResultados;

    if (search) {
      filteredResultados = filteredResultados.filter(resultado =>
        resultado.item.toLowerCase().includes(search.toLowerCase()) ||
        resultado.categoria.toLowerCase().includes(search.toLowerCase()) ||
        resultado.orgao.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (categoria) {
      filteredResultados = filteredResultados.filter(resultado => 
        resultado.categoria.toLowerCase().includes(categoria.toLowerCase())
      );
    }

    if (uf) {
      filteredResultados = filteredResultados.filter(resultado => resultado.uf === uf);
    }

    if (modalidade) {
      filteredResultados = filteredResultados.filter(resultado => 
        resultado.modalidade.toLowerCase().includes(modalidade.toLowerCase())
      );
    }

    // Paginação
    const offset = (page - 1) * limit;
    const totalResultados = filteredResultados.length;
    const resultadosPaginados = filteredResultados.slice(offset, offset + limit);

    // Calcular estatísticas
    const stats = {
      totalResultados,
      precoMedio: filteredResultados.length > 0 
        ? filteredResultados.reduce((sum, r) => sum + r.precoMedio, 0) / filteredResultados.length 
        : 0,
      menorPrecoGeral: filteredResultados.length > 0 
        ? Math.min(...filteredResultados.map(r => r.menorPreco)) 
        : 0,
      maiorPrecoGeral: filteredResultados.length > 0 
        ? Math.max(...filteredResultados.map(r => r.maiorPreco)) 
        : 0,
      orgaosConsultados: [...new Set(filteredResultados.map(r => r.orgao))].length,
      economiaEstimada: 45200.00 // Valor mockado
    };

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalResultados / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        resultados: resultadosPaginados,
        stats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalResultados,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error('Erro na API de pesquisa de preços:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { termo, usuario = 'Marcos Isidoro' } = body;
    
    if (!termo) {
      return NextResponse.json(
        { success: false, error: 'Termo de pesquisa é obrigatório' },
        { status: 400 }
      );
    }

    // Simular pesquisa no PNCP
    const resultados = mockResultados.filter(resultado =>
      resultado.item.toLowerCase().includes(termo.toLowerCase()) ||
      resultado.categoria.toLowerCase().includes(termo.toLowerCase())
    );

    // Adicionar ao histórico
    const novaPesquisa: HistoricoPesquisa = {
      id: (mockHistorico.length + 1).toString(),
      termo,
      dataConsulta: new Date().toLocaleDateString('pt-BR'),
      resultados: resultados.length,
      usuario,
      fonte: 'PNCP'
    };

    mockHistorico.unshift(novaPesquisa);

    return NextResponse.json({
      success: true,
      message: 'Pesquisa realizada com sucesso',
      data: {
        resultados,
        pesquisa: novaPesquisa,
        stats: {
          totalEncontrados: resultados.length,
          precoMedio: resultados.length > 0 
            ? resultados.reduce((sum, r) => sum + r.precoMedio, 0) / resultados.length 
            : 0
        }
      }
    });
  } catch (error) {
    console.error('Erro ao realizar pesquisa:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
