/**
 * Sistema de Perfis Individuais para Pesquisadores de Preços
 * Permite habilitações específicas para cada membro da equipe
 */

import { TipoPermissao } from './permissoes';

// Dados dos pesquisadores da equipe
export interface PesquisadorPerfil {
  id: string;
  nome: string;
  email: string;
  telefone?: string;
  especialidades: string[];
  permissoesExtras: TipoPermissao[];
  configuracoes: ConfiguracoesPesquisador;
  status: 'ativo' | 'inativo' | 'ferias';
  dataIngresso: string;
  observacoes?: string;
}

export interface ConfiguracoesPesquisador {
  graficosHabilitados: boolean;
  relatoriosAvancados: boolean;
  exportacaoHabilitada: boolean;
  dashboardPersonalizado: boolean;
  historicoCompleto: boolean;
  analiseComparativa: boolean;
  notificacoesPush: boolean;
  temaPreferido: 'light' | 'dark' | 'auto';
}

// Permissões extras disponíveis para pesquisadores
export const PERMISSOES_EXTRAS_PESQUISADOR: TipoPermissao[] = [
  'precos.graficos',
  'precos.relatorios.avancados',
  'precos.exportar',
  'precos.historico',
  'precos.comparativo',
  'precos.dashboard',
  'relatorios.avancados',
  'relatorios.exportar'
];

// Descrições das permissões extras
export const DESCRICOES_PERMISSOES_EXTRAS: Record<string, string> = {
  'precos.graficos': 'Visualizar gráficos e estatísticas de preços',
  'precos.relatorios.avancados': 'Gerar relatórios avançados de pesquisa',
  'precos.exportar': 'Exportar dados para Excel/PDF',
  'precos.historico': 'Acessar histórico completo de pesquisas',
  'precos.comparativo': 'Fazer análises comparativas entre fornecedores',
  'precos.dashboard': 'Dashboard personalizado com métricas',
  'relatorios.avancados': 'Relatórios gerais avançados',
  'relatorios.exportar': 'Exportar qualquer relatório'
};

// Equipe de pesquisadores
export const EQUIPE_PESQUISADORES: PesquisadorPerfil[] = [
  {
    id: 'pesq_carla',
    nome: 'Carla',
    email: '<EMAIL>',
    telefone: '(11) 4512-8001',
    especialidades: ['Materiais de Construção', 'Equipamentos'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'light'
    },
    status: 'ativo',
    dataIngresso: '2024-03-01',
    observacoes: 'Especialista em materiais de construção'
  },
  {
    id: 'pesq_sonia',
    nome: 'Sonia',
    email: '<EMAIL>',
    telefone: '(11) 4512-8002',
    especialidades: ['Medicamentos', 'Material Hospitalar'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'light'
    },
    status: 'ativo',
    dataIngresso: '2024-02-15',
    observacoes: 'Especialista em área da saúde'
  },
  {
    id: 'pesq_mariane',
    nome: 'Mariane',
    email: '<EMAIL>',
    telefone: '(11) 4512-8003',
    especialidades: ['Material Escolar', 'Mobiliário'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'light'
    },
    status: 'ativo',
    dataIngresso: '2024-01-20',
    observacoes: 'Especialista em material educacional'
  },
  {
    id: 'pesq_fernando',
    nome: 'Fernando',
    email: '<EMAIL>',
    telefone: '(11) 4512-8004',
    especialidades: ['Veículos', 'Combustíveis'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'light'
    },
    status: 'ativo',
    dataIngresso: '2024-04-10',
    observacoes: 'Especialista em veículos e combustíveis'
  },
  {
    id: 'pesq_paulo',
    nome: 'Paulo',
    email: '<EMAIL>',
    telefone: '(11) 4512-8005',
    especialidades: ['Informática', 'Eletrônicos'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'dark'
    },
    status: 'ativo',
    dataIngresso: '2024-03-25',
    observacoes: 'Especialista em tecnologia'
  },
  {
    id: 'pesq_gilson',
    nome: 'Gilson',
    email: '<EMAIL>',
    telefone: '(11) 4512-8006',
    especialidades: ['Serviços Gerais', 'Manutenção'],
    permissoesExtras: [],
    configuracoes: {
      graficosHabilitados: false,
      relatoriosAvancados: false,
      exportacaoHabilitada: false,
      dashboardPersonalizado: false,
      historicoCompleto: false,
      analiseComparativa: false,
      notificacoesPush: true,
      temaPreferido: 'light'
    },
    status: 'ativo',
    dataIngresso: '2024-02-01',
    observacoes: 'Especialista em serviços e manutenção'
  }
];

/**
 * Habilita uma permissão extra para um pesquisador
 */
export function habilitarPermissao(pesquisadorId: string, permissao: TipoPermissao): boolean {
  const pesquisador = EQUIPE_PESQUISADORES.find(p => p.id === pesquisadorId);
  if (!pesquisador) return false;
  
  if (!pesquisador.permissoesExtras.includes(permissao)) {
    pesquisador.permissoesExtras.push(permissao);
    
    // Atualizar configurações relacionadas
    switch (permissao) {
      case 'precos.graficos':
        pesquisador.configuracoes.graficosHabilitados = true;
        break;
      case 'precos.relatorios.avancados':
        pesquisador.configuracoes.relatoriosAvancados = true;
        break;
      case 'precos.exportar':
        pesquisador.configuracoes.exportacaoHabilitada = true;
        break;
      case 'precos.dashboard':
        pesquisador.configuracoes.dashboardPersonalizado = true;
        break;
      case 'precos.historico':
        pesquisador.configuracoes.historicoCompleto = true;
        break;
      case 'precos.comparativo':
        pesquisador.configuracoes.analiseComparativa = true;
        break;
    }
  }
  
  return true;
}

/**
 * Remove uma permissão extra de um pesquisador
 */
export function removerPermissao(pesquisadorId: string, permissao: TipoPermissao): boolean {
  const pesquisador = EQUIPE_PESQUISADORES.find(p => p.id === pesquisadorId);
  if (!pesquisador) return false;
  
  const index = pesquisador.permissoesExtras.indexOf(permissao);
  if (index > -1) {
    pesquisador.permissoesExtras.splice(index, 1);
    
    // Atualizar configurações relacionadas
    switch (permissao) {
      case 'precos.graficos':
        pesquisador.configuracoes.graficosHabilitados = false;
        break;
      case 'precos.relatorios.avancados':
        pesquisador.configuracoes.relatoriosAvancados = false;
        break;
      case 'precos.exportar':
        pesquisador.configuracoes.exportacaoHabilitada = false;
        break;
      case 'precos.dashboard':
        pesquisador.configuracoes.dashboardPersonalizado = false;
        break;
      case 'precos.historico':
        pesquisador.configuracoes.historicoCompleto = false;
        break;
      case 'precos.comparativo':
        pesquisador.configuracoes.analiseComparativa = false;
        break;
    }
  }
  
  return true;
}

/**
 * Obtém todas as permissões de um pesquisador (básicas + extras)
 */
export function obterPermissoesPesquisador(pesquisadorId: string): TipoPermissao[] {
  const pesquisador = EQUIPE_PESQUISADORES.find(p => p.id === pesquisadorId);
  if (!pesquisador) return [];
  
  // Permissões básicas do perfil pesquisador
  const permissoesBasicas: TipoPermissao[] = [
    'precos.visualizar',
    'precos.criar',
    'precos.editar',
    'processos.visualizar',
    'processos.visualizar.detalhes',
    'relatorios.basicos'
  ];
  
  return [...permissoesBasicas, ...pesquisador.permissoesExtras];
}

/**
 * Verifica se um pesquisador tem uma permissão específica
 */
export function pesquisadorTemPermissao(pesquisadorId: string, permissao: TipoPermissao): boolean {
  const permissoes = obterPermissoesPesquisador(pesquisadorId);
  return permissoes.includes(permissao);
}
