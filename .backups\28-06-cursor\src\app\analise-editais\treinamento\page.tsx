'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  Brain, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText,
  Database,
  TrendingUp,
  Download,
  Trash2,
  Eye,
  Tag
} from 'lucide-react';

interface DocumentoTreinamento {
  id: string;
  nome: string;
  tipo: 'etp' | 'edital' | 'tr';
  categoria: 'aprovado' | 'reprovado' | 'conflito' | 'nao_classificado';
  objeto: string;
  secretaria: string;
  dataUpload: string;
  tamanho: number;
  observacoes?: string;
  problemas?: string[];
}

export default function TreinamentoMLPage() {
  const [documentos, setDocumentos] = useState<DocumentoTreinamento[]>([]);

  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('todos');
  const [isDragging, setIsDragging] = useState(false);

  const handleBulkUpload = async () => {
    setUploadingFiles(true);
    // Simular upload em lote
    setTimeout(() => {
      setUploadingFiles(false);
      alert('Upload em lote realizado com sucesso!');
    }, 2000);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    handleFileUpload(files);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      handleFileUpload(files);
    }
  };

  const handleFileUpload = (files: File[]) => {
    setUploadingFiles(true);

    // Simular processamento dos arquivos
    console.log('Arquivos selecionados:', files.map(f => f.name));

    setTimeout(() => {
      setUploadingFiles(false);
      alert(`${files.length} arquivo(s) processado(s) com sucesso!`);
    }, 2000);
  };

  const handleClassificarDocumento = (id: string, categoria: 'aprovado' | 'reprovado' | 'conflito') => {
    setDocumentos(prev => prev.map(doc => 
      doc.id === id ? { ...doc, categoria } : doc
    ));
  };

  const documentosFiltrados = selectedCategory === 'todos' 
    ? documentos 
    : documentos.filter(doc => doc.categoria === selectedCategory);

  const estatisticas = {
    total: documentos.length,
    aprovados: documentos.filter(d => d.categoria === 'aprovado').length,
    reprovados: documentos.filter(d => d.categoria === 'reprovado').length,
    conflitos: documentos.filter(d => d.categoria === 'conflito').length,
    naoClassificados: documentos.filter(d => d.categoria === 'nao_classificado').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Treinamento ML</h1>
          <p className="text-muted-foreground mt-2">
            Sistema para treinar IA com documentos reais de processos
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <Brain className="mr-1 h-3 w-3" />
            Machine Learning
          </Badge>
          <Badge variant="outline" className="text-xs">
            <Database className="mr-1 h-3 w-3" />
            Dataset Processos
          </Badge>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{estatisticas.total}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{estatisticas.aprovados}</div>
            <div className="text-sm text-muted-foreground">Aprovados</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{estatisticas.reprovados}</div>
            <div className="text-sm text-muted-foreground">Reprovados</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{estatisticas.conflitos}</div>
            <div className="text-sm text-muted-foreground">Conflitos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{estatisticas.naoClassificados}</div>
            <div className="text-sm text-muted-foreground">Não Class.</div>
          </CardContent>
        </Card>
      </div>

      {/* Upload em Lote */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Upload className="mr-2 h-5 w-5" />
            Upload em Lote para Treinamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Área de Drag & Drop */}
          <div
            className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.odt,.ods,.odp"
              onChange={handleFileSelect}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={uploadingFiles}
            />

            <div className="space-y-4">
              <div className="flex justify-center">
                <Upload className={`h-12 w-12 ${isDragging ? 'text-blue-500' : 'text-gray-400'}`} />
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">
                  {isDragging ? 'Solte os arquivos aqui' : 'Arraste seus documentos aqui'}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Ou clique para selecionar arquivos
                </p>

                <div className="flex justify-center gap-4 text-xs text-muted-foreground">
                  <span>📄 ETPs</span>
                  <span>📋 Editais</span>
                  <span>📝 TRs</span>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <p>Formatos aceitos: PDF, DOC, DOCX, ODT, ODS, ODP</p>
                <p>Tamanho máximo: 50MB por arquivo</p>
              </div>

              {uploadingFiles && (
                <div className="flex items-center justify-center gap-2 text-blue-600">
                  <Upload className="h-4 w-4 animate-pulse" />
                  <span>Processando arquivos...</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Tag className="mr-2 h-5 w-5" />
            Classificação de Documentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 mb-4">
            {[
              { key: 'todos', label: 'Todos', color: 'default' },
              { key: 'aprovado', label: 'Aprovados', color: 'default' },
              { key: 'reprovado', label: 'Reprovados', color: 'destructive' },
              { key: 'conflito', label: 'Conflitos', color: 'default' },
              { key: 'nao_classificado', label: 'Não Classificados', color: 'secondary' }
            ].map(filtro => (
              <Button
                key={filtro.key}
                variant={selectedCategory === filtro.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(filtro.key)}
              >
                {filtro.label}
              </Button>
            ))}
          </div>

          <div className="space-y-3">
            {documentosFiltrados.map(doc => (
              <div key={doc.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium">{doc.nome}</span>
                      <Badge variant="outline" className="text-xs">
                        {doc.tipo.toUpperCase()}
                      </Badge>
                      <Badge 
                        variant={
                          doc.categoria === 'aprovado' ? 'default' :
                          doc.categoria === 'reprovado' ? 'destructive' :
                          doc.categoria === 'conflito' ? 'default' : 'secondary'
                        }
                        className="text-xs"
                      >
                        {doc.categoria.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p><strong>Objeto:</strong> {doc.objeto}</p>
                      <p><strong>Secretaria:</strong> {doc.secretaria}</p>
                      <p><strong>Data:</strong> {doc.dataUpload} | <strong>Tamanho:</strong> {Math.round(doc.tamanho / 1024)} KB</p>
                      {doc.observacoes && (
                        <p className="text-green-600"><strong>Observações:</strong> {doc.observacoes}</p>
                      )}
                      {doc.problemas && (
                        <div className="text-red-600">
                          <strong>Problemas:</strong>
                          <ul className="list-disc list-inside ml-2">
                            {doc.problemas.map((problema, index) => (
                              <li key={index}>{problema}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'aprovado')}
                    >
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'reprovado')}
                    >
                      <XCircle className="h-4 w-4 text-red-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'conflito')}
                    >
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Progresso do Treinamento */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="mr-2 h-5 w-5" />
            Progresso do Treinamento ML
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Documentos Classificados</span>
                <span>{estatisticas.total - estatisticas.naoClassificados}/{estatisticas.total}</span>
              </div>
              <Progress value={((estatisticas.total - estatisticas.naoClassificados) / estatisticas.total) * 100} />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
              <div>
                <h4 className="font-medium mb-2">📊 Qualidade do Dataset</h4>
                <ul className="text-sm space-y-1">
                  <li>✅ Documentos aprovados: {estatisticas.aprovados}</li>
                  <li>❌ Casos de erro: {estatisticas.reprovados}</li>
                  <li>⚠️ Conflitos identificados: {estatisticas.conflitos}</li>
                  <li>🎯 Recomendado: 15+ de cada categoria</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">🚀 Próximos Passos</h4>
                <ul className="text-sm space-y-1">
                  <li>1. Classificar documentos restantes</li>
                  <li>2. Adicionar mais exemplos de cada tipo</li>
                  <li>3. Treinar modelo personalizado</li>
                  <li>4. Testar com novos documentos</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
