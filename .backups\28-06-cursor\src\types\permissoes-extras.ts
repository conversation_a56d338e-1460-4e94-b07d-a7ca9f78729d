/**
 * Sistema de Permissões Extras por Perfil
 * Permite habilitar funcionalidades adicionais para cada tipo de usuário
 */

import { PerfilUsuario, TipoPermissao } from './permissoes';

// Permissões extras disponíveis para cada perfil
export interface PermissoesExtras {
  [key: string]: {
    nome: string;
    descricao: string;
    permissao: TipoPermissao;
    categoria: string;
    habilitado: boolean;
  };
}

// Configurações de permissões extras por perfil
export const PERMISSOES_EXTRAS_POR_PERFIL: Record<PerfilUsuario, PermissoesExtras> = {
  
  // === PESQUISADORES ===
  pesquisador: {
    graficos_precos: {
      nome: 'Gráficos de Preços',
      descricao: 'Visualizar gráficos e estatísticas avançadas de preços',
      permissao: 'precos.graficos',
      categoria: '<PERSON><PERSON><PERSON><PERSON>',
      habilitado: false
    },
    relatorios_avancados: {
      nome: 'Relatórios Avançados',
      descricao: 'Gerar relatórios detalhados de pesquisa de preços',
      permissao: 'precos.relatorios.avancados',
      categoria: 'Relatórios',
      habilitado: false
    },
    exportar_dados: {
      nome: 'Exportar Dados',
      descricao: 'Exportar pesquisas para Excel, PDF e outros formatos',
      permissao: 'precos.exportar',
      categoria: 'Exportação',
      habilitado: false
    },
    historico_completo: {
      nome: 'Histórico Completo',
      descricao: 'Acessar histórico completo de todas as pesquisas',
      permissao: 'precos.historico',
      categoria: 'Histórico',
      habilitado: false
    },
    analise_comparativa: {
      nome: 'Análise Comparativa',
      descricao: 'Comparar preços entre fornecedores e períodos',
      permissao: 'precos.comparativo',
      categoria: 'Análise',
      habilitado: false
    },
    dashboard_personalizado: {
      nome: 'Dashboard Personalizado',
      descricao: 'Dashboard com métricas específicas de preços',
      permissao: 'precos.dashboard',
      categoria: 'Dashboard',
      habilitado: false
    }
  },

  // === ANALISTAS ===
  analista: {
    ocr_documentos: {
      nome: 'OCR de Documentos',
      descricao: 'Usar reconhecimento de texto em documentos',
      permissao: 'analises.ocr',
      categoria: 'Tecnologia',
      habilitado: false
    },
    aprovar_analises: {
      nome: 'Aprovar Análises',
      descricao: 'Aprovar análises de outros usuários',
      permissao: 'analises.aprovar',
      categoria: 'Aprovação',
      habilitado: false
    },
    relatorios_gerenciais: {
      nome: 'Relatórios Gerenciais',
      descricao: 'Gerar relatórios gerenciais avançados',
      permissao: 'relatorios.avancados',
      categoria: 'Relatórios',
      habilitado: false
    },
    metricas_detalhadas: {
      nome: 'Métricas Detalhadas',
      descricao: 'Visualizar métricas detalhadas do sistema',
      permissao: 'relatorios.metricas',
      categoria: 'Análise',
      habilitado: false
    },
    alterar_prioridades: {
      nome: 'Alterar Prioridades',
      descricao: 'Modificar prioridade de processos',
      permissao: 'processos.prioridade',
      categoria: 'Gestão',
      habilitado: false
    }
  },

  // === SECRETARIAS ===
  secretaria: {
    criar_processos_avancado: {
      nome: 'Criação Avançada',
      descricao: 'Criar processos com campos avançados',
      permissao: 'processos.criar',
      categoria: 'Processos',
      habilitado: false
    },
    editar_processos: {
      nome: 'Editar Processos',
      descricao: 'Editar processos da sua secretaria',
      permissao: 'processos.editar',
      categoria: 'Processos',
      habilitado: false
    },
    relatorios_secretaria: {
      nome: 'Relatórios da Secretaria',
      descricao: 'Relatórios específicos da sua secretaria',
      permissao: 'relatorios.avancados',
      categoria: 'Relatórios',
      habilitado: false
    },
    tramitar_processos: {
      nome: 'Tramitar Processos',
      descricao: 'Tramitar processos dentro da secretaria',
      permissao: 'processos.tramitar',
      categoria: 'Tramitação',
      habilitado: false
    },
    anexar_documentos: {
      nome: 'Anexar Documentos',
      descricao: 'Anexar documentos aos processos',
      permissao: 'processos.anexos',
      categoria: 'Documentos',
      habilitado: false
    }
  },

  // === ESTAGIÁRIOS ===
  estagiario: {
    criar_processos_simples: {
      nome: 'Criar Processos Simples',
      descricao: 'Criar processos básicos com supervisão',
      permissao: 'processos.criar',
      categoria: 'Processos',
      habilitado: false
    },
    relatorios_basicos: {
      nome: 'Relatórios Básicos',
      descricao: 'Gerar relatórios básicos do sistema',
      permissao: 'relatorios.basicos',
      categoria: 'Relatórios',
      habilitado: false
    },
    pesquisa_precos_basica: {
      nome: 'Pesquisa de Preços',
      descricao: 'Auxiliar em pesquisas de preços básicas',
      permissao: 'precos.visualizar',
      categoria: 'Preços',
      habilitado: false
    }
  },

  // === CONSULTA ===
  consulta: {
    exportar_consultas: {
      nome: 'Exportar Consultas',
      descricao: 'Exportar resultados de consultas',
      permissao: 'relatorios.exportar',
      categoria: 'Exportação',
      habilitado: false
    },
    historico_completo: {
      nome: 'Histórico Completo',
      descricao: 'Ver histórico completo de processos',
      permissao: 'processos.historico',
      categoria: 'Histórico',
      habilitado: false
    }
  },

  // === COORDENADOR ===
  coordenador: {
    // Coordenador já tem acesso amplo, poucas extras necessárias
    backup_sistema: {
      nome: 'Backup do Sistema',
      descricao: 'Realizar backups manuais do sistema',
      permissao: 'config.backup',
      categoria: 'Sistema',
      habilitado: false
    }
  },

  // === ASSESSOR ===
  assessor: {
    // Assessor já tem acesso amplo, poucas extras necessárias
    ratificar_precos: {
      nome: 'Ratificar Preços',
      descricao: 'Ratificar pesquisas de preços',
      permissao: 'precos.ratificar',
      categoria: 'Aprovação',
      habilitado: false
    }
  },

  // === ADMIN ===
  admin: {
    // Admin já tem tudo, sem extras necessárias
  }
};

// Categorias de permissões para organização
export const CATEGORIAS_PERMISSOES = [
  'Processos',
  'Análise', 
  'Relatórios',
  'Exportação',
  'Histórico',
  'Dashboard',
  'Tecnologia',
  'Aprovação',
  'Gestão',
  'Tramitação',
  'Documentos',
  'Preços',
  'Sistema'
];

/**
 * Habilita uma permissão extra para um perfil
 */
export function habilitarPermissaoExtra(perfil: PerfilUsuario, chavePermissao: string): boolean {
  const permissoesExtras = PERMISSOES_EXTRAS_POR_PERFIL[perfil];
  if (permissoesExtras && permissoesExtras[chavePermissao]) {
    permissoesExtras[chavePermissao].habilitado = true;
    return true;
  }
  return false;
}

/**
 * Desabilita uma permissão extra para um perfil
 */
export function desabilitarPermissaoExtra(perfil: PerfilUsuario, chavePermissao: string): boolean {
  const permissoesExtras = PERMISSOES_EXTRAS_POR_PERFIL[perfil];
  if (permissoesExtras && permissoesExtras[chavePermissao]) {
    permissoesExtras[chavePermissao].habilitado = false;
    return true;
  }
  return false;
}

/**
 * Obtém todas as permissões habilitadas para um perfil
 */
export function obterPermissoesHabilitadas(perfil: PerfilUsuario): TipoPermissao[] {
  const permissoesExtras = PERMISSOES_EXTRAS_POR_PERFIL[perfil];
  if (!permissoesExtras) return [];
  
  return Object.values(permissoesExtras)
    .filter(p => p.habilitado)
    .map(p => p.permissao);
}

/**
 * Verifica se uma permissão extra está habilitada
 */
export function permissaoExtraHabilitada(perfil: PerfilUsuario, chavePermissao: string): boolean {
  const permissoesExtras = PERMISSOES_EXTRAS_POR_PERFIL[perfil];
  return permissoesExtras?.[chavePermissao]?.habilitado || false;
}

/**
 * Obtém estatísticas de permissões extras por perfil
 */
export function obterEstatisticasPermissoes() {
  const stats: Record<PerfilUsuario, { total: number; habilitadas: number }> = {} as any;
  
  Object.entries(PERMISSOES_EXTRAS_POR_PERFIL).forEach(([perfil, permissoes]) => {
    const total = Object.keys(permissoes).length;
    const habilitadas = Object.values(permissoes).filter(p => p.habilitado).length;
    stats[perfil as PerfilUsuario] = { total, habilitadas };
  });
  
  return stats;
}
