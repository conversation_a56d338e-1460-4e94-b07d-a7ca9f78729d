{"name": "inovaprocess-new", "version": "0.1.0", "private": true, "scripts": {"predev": "node src/scripts/prevent-reset.js --backup && node src/scripts/monitor-resets.js", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "backup": "node src/scripts/prevent-reset.js --backup", "clean-cache": "node src/scripts/prevent-reset.js --clean-cache", "restore-backup": "node src/scripts/prevent-reset.js --restore", "fix": "node src/scripts/prevent-reset.js --clean-cache", "prebuild": "node src/scripts/monitor-resets.js", "check-protection": "node src/scripts/monitor-resets.js", "restore-checkpoint": "node src/scripts/prevent-reset.js --restore-checkpoint", "checkpoint": "node src/scripts/checkpoint-system.js create", "checkpoint:list": "node src/scripts/checkpoint-system.js list", "checkpoint:restore": "node src/scripts/checkpoint-system.js restore"}}