# 🔒 CONFIGURAÇÃO SEGURA DO FIREBASE

## 🚨 PROBLEMA IDENTIFICADO
Você tem DOIS projetos Firebase diferentes:
- **Dev**: `inovaprocess-novo` 
- **Prod**: `inovaprocess-prod`

Isso é PERIGOSO! Os dados ficam separados.

## ✅ SOLUÇÃO SEGURA

### 1. Criar arquivo `.env.local` na raiz do projeto:

```bash
# .env.local
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC6A5OAOPoadEFloAohOlFr19Y7mddNDOw
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=inovaprocess.app
NEXT_PUBLIC_FIREBASE_PROJECT_ID=inovaprocess-prod
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=inovaprocess-prod.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=680146328118
NEXT_PUBLIC_FIREBASE_APP_ID=1:680146328118:web:6844f90d16e14051291697
```

### 2. Configurar domínios autorizados no Firebase Console:

1. Acesse: https://console.firebase.google.com/project/inovaprocess-prod
2. Authentication > Settings > Authorized domains
3. Adicionar: `localhost` (para desenvolvimento)
4. Adicionar: `inovaprocess.app` (para produção)

### 3. Verificar regras do Firestore:

As regras devem permitir:
- Criação do primeiro usuário
- Leitura/escrita para usuários autenticados
- Controle de acesso baseado em perfil

## 🎯 RESULTADO

Após essa configuração:
- ✅ Dev e prod usam o MESMO banco
- ✅ Primeiro acesso pode ser feito em qualquer ambiente
- ✅ Não há risco de dados separados
- ✅ Sistema de aprovação funciona corretamente

## ⚠️ IMPORTANTE

- Qualquer alteração local afetará o banco de produção
- Faça backup antes de testes
- O primeiro admin será criado no projeto `inovaprocess-prod` 