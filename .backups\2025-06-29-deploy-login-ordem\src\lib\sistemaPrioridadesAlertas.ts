/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 SISTEMA UNIFICADO DE PRIORIDADES E ALERTAS - INOVAPROCESS
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * 📋 DOCUMENTO OFICIAL DE REGRAS E ESPECIFICAÇÕES
 * 🏢 Prefeitura de Mauá - CLMP (Coordenadoria de Licitações, Materiais e Patrimônio)
 * 👨‍💼 Responsável: <PERSON>
 * 📅 Data: 09/06/2025
 * 🔄 Versão: 1.0 - "Versão Sobrevivência"
 * 
 * ⚠️ IMPORTANTE: Este arquivo contém as regras CRÍTICAS do sistema
 * Qualquer alteração deve ser aprovada pela CLMP
 * 
 * 🏆 CERTIFICAÇÃO: Este sistema pode ser usado para certificação do setor
 */

import { Processo } from '@/types/processo';

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 SEÇÃO 1: REGRAS DE PRIORIDADE ALTA
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * REGRA 1: FONTES DE RECURSOS NÃO-TESOURO
 * 📋 Descrição: Processos com recursos externos precisam de celeridade 
 *              para não perder prazos de prestação de contas
 * 🎯 Fontes: ESTADUAL, FUNDO, FEDERAL, FINISA
 */
export function temFonteNaoTesouro(processo: Processo): boolean {
  const fontes = [
    processo['Fonte 0002 (ESTADUAL)'],
    processo['Fonte 0003 (FUNDO)'],
    processo['Fonte 0005 (FEDERAL)'],
    processo['Fonte 0007 (FINISA)']
  ];

  return fontes.some(fonte => 
    fonte && 
    fonte !== '-' && 
    fonte !== '0' && 
    fonte !== 'R$ 0,00' && 
    fonte !== '' && 
    fonte !== undefined
  );
}

/**
 * REGRA 2: DEMANDAS JUDICIAIS
 * 📋 Descrição: Qualquer processo relacionado ao Poder Judiciário
 * 🎯 Palavras-chave: judicial, processo judicial, demanda judicial, etc.
 */
export function temDemandasJudiciais(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  
  const palavrasJudiciais = [
    'judicial',
    'processo judicial',
    'demanda judicial',
    'mandado',
    'liminar',
    'sentença',
    'decisão judicial',
    'ordem judicial'
  ];

  return palavrasJudiciais.some(palavra => objeto.includes(palavra));
}

/**
 * REGRA 3: ALIMENTAÇÃO ESCOLAR (SSAN)
 * 📋 Descrição: Merenda, alimentação, bebidas, panificação para alunos
 * 🎯 Secretaria: SSAN (Secretaria de Segurança Alimentar e Nutricional)
 */
export function temAlimentacaoEscolar(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  const requisitante = (processo.REQUISITANTE || '').toLowerCase();
  
  const palavrasAlimentacao = [
    'merenda', 'alimentação', 'alimentacao', 'bebidas', 'panificação', 'panificacao',
    'lanche', 'refeição', 'refeicao', 'cardápio', 'cardapio', 'nutrição', 'nutricao',
    'alimentos', 'gêneros alimentícios', 'generos alimenticios'
  ];

  const isSsan = requisitante.includes('ssan') || 
                 requisitante.includes('secretaria de segurança alimentar');

  return isSsan || palavrasAlimentacao.some(palavra => objeto.includes(palavra));
}

/**
 * REGRA 4: MEDICAMENTOS E SAÚDE
 * 📋 Descrição: Demandas de saúde têm prioridade alta
 * 🎯 Área: Saúde pública, medicamentos, equipamentos médicos
 */
export function temMedicamentos(processo: Processo): boolean {
  const objeto = (processo.OBJETO || '').toLowerCase();
  
  const palavrasMedicamentos = [
    'medicamento', 'medicamentos', 'remédio', 'remedios', 'farmácia', 'farmacia',
    'hospitalar', 'cirúrgico', 'cirurgico', 'ambulatorial', 'emergência', 'emergencia',
    'urgência', 'urgencia', 'uti', 'pronto socorro', 'saúde', 'saude', 'médico', 'medico',
    'enfermagem', 'vacina', 'imunização', 'imunizacao'
  ];

  return palavrasMedicamentos.some(palavra => objeto.includes(palavra));
}

/**
 * REGRA 5: PRIORIDADES DO GOVERNO
 * 📋 Descrição: Classificação manual via botão "Prioridades do Governo"
 * 👥 Usuários habilitados: Secretário de Governo + Secretária Adjunta
 * ⏰ Timeout: 7 dias → Auto-classificação como NORMAL
 */
export function temPrioridadeGoverno(processo: Processo): boolean {
  return processo.prioridadeGoverno === true || false;
}

/**
 * FUNÇÃO PRINCIPAL: VERIFICA PRIORIDADE ALTA
 * 📋 Aplica TODAS as 5 regras definidas acima
 */
export function temPrioridadeAlta(processo: Processo): boolean {
  return (
    temFonteNaoTesouro(processo) ||
    temDemandasJudiciais(processo) ||
    temAlimentacaoEscolar(processo) ||
    temMedicamentos(processo) ||
    temPrioridadeGoverno(processo)
  );
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔔 SEÇÃO 2: SISTEMA DE ALERTAS
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * TIPOS DE ALERTAS DO SININHO
 */
export type TipoAlerta =
  | 'PRAZO_CRITICO'
  | 'PRIORIDADE_PARADA'
  | 'NOVO_PROCESSO'
  | 'GARGALO_CRITICO'
  | 'RETRABALHO'
  | 'PRIORIDADE_CLASSIFICADA'
  | 'RECURSO_SEM_DATA'
  | 'ENCAMINHAMENTO_DIRETO_REQUISITANTE';

export interface AlertaNotificacao {
  id: string;
  tipo: TipoAlerta;
  titulo: string;
  preview: string;
  processo: Processo;
  dataAlerta: Date;
  lido: boolean;
  urgencia: 'BAIXA' | 'MEDIA' | 'ALTA' | 'CRITICA';
  detalhes: {
    valor?: string;
    responsavel?: string;
    local?: string;
    diasParado?: number;
    classificadoPor?: string;
    motivo?: string;
  };
}

/**
 * ALERTA 1: PRAZOS CRÍTICOS
 * 📋 Regra: Processos há mais de 15 dias no mesmo local OU 30 dias sem movimentação
 */
export function verificarPrazoCritico(processo: Processo): AlertaNotificacao | null {
  // TODO: Implementar cálculo de dias baseado em datas reais
  const diasNoLocal = 20; // Exemplo - calcular com dados reais
  
  if (diasNoLocal > 15) {
    return {
      id: `prazo_${processo.PROCESSO}`,
      tipo: 'PRAZO_CRITICO',
      titulo: `Processo ${processo.PROCESSO} - PRAZO CRÍTICO`,
      preview: `Há ${diasNoLocal} dias em ${processo.LOCAL}`,
      processo,
      dataAlerta: new Date(),
      lido: false,
      urgencia: diasNoLocal > 30 ? 'CRITICA' : 'ALTA',
      detalhes: {
        valor: processo.VALOR,
        local: processo.LOCAL,
        diasParado: diasNoLocal
      }
    };
  }
  
  return null;
}

/**
 * ALERTA 2: PRIORIDADES ALTAS PARADAS
 * 📋 Regra: Alerta DIÁRIO para usuário com processo prioritário há mais de 5 dias
 */
export function verificarPrioridadeParada(processo: Processo): AlertaNotificacao | null {
  if (!temPrioridadeAlta(processo)) return null;
  
  const diasParado = 6; // Exemplo - calcular com dados reais
  
  if (diasParado > 5) {
    return {
      id: `prioridade_parada_${processo.PROCESSO}`,
      tipo: 'PRIORIDADE_PARADA',
      titulo: `PRIORIDADE ALTA PARADA - ${processo.PROCESSO}`,
      preview: `Processo prioritário há ${diasParado} dias com ${processo.RESPONSÁVEL}`,
      processo,
      dataAlerta: new Date(),
      lido: false,
      urgencia: 'CRITICA',
      detalhes: {
        valor: processo.VALOR,
        responsavel: processo.RESPONSÁVEL,
        diasParado,
        motivo: getMotivosPrioridade(processo).join(', ')
      }
    };
  }
  
  return null;
}

/**
 * ALERTA 3: NOVO PROCESSO
 * 📋 Regra: Todos os usuários recebem + Popup para habilitados
 */
export function criarAlertaNovoProcesso(processo: Processo): AlertaNotificacao {
  return {
    id: `novo_${processo.PROCESSO}`,
    tipo: 'NOVO_PROCESSO',
    titulo: `NOVO PROCESSO - ${processo.PROCESSO}`,
    preview: `${processo.OBJETO?.substring(0, 50)}... - ${processo.VALOR}`,
    processo,
    dataAlerta: new Date(),
    lido: false,
    urgencia: 'MEDIA',
    detalhes: {
      valor: processo.VALOR,
      responsavel: processo.RESPONSÁVEL,
      local: processo.LOCAL
    }
  };
}

/**
 * ALERTA 7: RECURSOS EXTERNOS SEM DATA DE VENCIMENTO
 * 📋 Regra: Alertar Isabela e Tath quando processo tem recursos externos mas não tem datas CORRESPONDENTES
 * ⚠️ IMPORTANTE: Só alerta se TEM VALOR mas NÃO TEM DATA do mesmo recurso
 */
export function verificarRecursoSemData(processo: Processo): AlertaNotificacao | null {
  const fontesComProblema: string[] = [];

  // Verificar cada fonte individualmente - só alerta se TEM VALOR mas NÃO TEM DATA

  // Fonte Estadual
  const temEstadual = processo['Fonte 0002 (ESTADUAL)'] &&
                     processo['Fonte 0002 (ESTADUAL)'] !== '-' &&
                     processo['Fonte 0002 (ESTADUAL)'] !== '0' &&
                     processo['Fonte 0002 (ESTADUAL)'] !== 'R$ 0,00' &&
                     processo['Fonte 0002 (ESTADUAL)'] !== '';
  const temDataEstadual = processo['Data Vencimento Estadual'] &&
                         processo['Data Vencimento Estadual'] !== '-' &&
                         processo['Data Vencimento Estadual'] !== '';
  if (temEstadual && !temDataEstadual) {
    fontesComProblema.push('Estadual');
  }

  // Fonte Fundo
  const temFundo = processo['Fonte 0003 (FUNDO)'] &&
                  processo['Fonte 0003 (FUNDO)'] !== '-' &&
                  processo['Fonte 0003 (FUNDO)'] !== '0' &&
                  processo['Fonte 0003 (FUNDO)'] !== 'R$ 0,00' &&
                  processo['Fonte 0003 (FUNDO)'] !== '';
  const temDataFundo = processo['Data Vencimento Fundo'] &&
                      processo['Data Vencimento Fundo'] !== '-' &&
                      processo['Data Vencimento Fundo'] !== '';
  if (temFundo && !temDataFundo) {
    fontesComProblema.push('Fundo');
  }

  // Fonte Federal
  const temFederal = processo['Fonte 0005 (FEDERAL)'] &&
                    processo['Fonte 0005 (FEDERAL)'] !== '-' &&
                    processo['Fonte 0005 (FEDERAL)'] !== '0' &&
                    processo['Fonte 0005 (FEDERAL)'] !== 'R$ 0,00' &&
                    processo['Fonte 0005 (FEDERAL)'] !== '';
  const temDataFederal = processo['Data Vencimento Federal'] &&
                        processo['Data Vencimento Federal'] !== '-' &&
                        processo['Data Vencimento Federal'] !== '';
  if (temFederal && !temDataFederal) {
    fontesComProblema.push('Federal');
  }

  // Fonte Finisa
  const temFinisa = processo['Fonte 0007 (FINISA)'] &&
                   processo['Fonte 0007 (FINISA)'] !== '-' &&
                   processo['Fonte 0007 (FINISA)'] !== '0' &&
                   processo['Fonte 0007 (FINISA)'] !== 'R$ 0,00' &&
                   processo['Fonte 0007 (FINISA)'] !== '';
  const temDataFinisa = processo['Data Vencimento Finisa'] &&
                       processo['Data Vencimento Finisa'] !== '-' &&
                       processo['Data Vencimento Finisa'] !== '';
  if (temFinisa && !temDataFinisa) {
    fontesComProblema.push('Finisa');
  }

  // Se não há problemas, não gerar alerta
  if (fontesComProblema.length === 0) return null;

  return {
    id: `recurso_sem_data_${processo.PROCESSO}`,
    tipo: 'RECURSO_SEM_DATA',
    titulo: `DATAS FALTANTES - ${processo.PROCESSO}`,
    preview: `Fontes sem data: ${fontesComProblema.join(', ')}`,
    processo,
    dataAlerta: new Date(),
    lido: false,
    urgencia: 'MEDIA',
    detalhes: {
      valor: processo.VALOR,
      responsavel: processo.RESPONSÁVEL,
      local: processo.LOCAL,
      motivo: `Fontes com valor mas sem data de vencimento: ${fontesComProblema.join(', ')}`
    }
  };
}

/**
 * ALERTA 4: GARGALOS CRÍTICOS
 * 📋 Regra: SF ou SAJ com mais de 8 processos
 */
export function verificarGargalos(processos: Processo[]): AlertaNotificacao[] {
  const alertas: AlertaNotificacao[] = [];
  
  const processosNaSF = processos.filter(p => p.LOCAL?.includes('SF')).length;
  const processosNaSAJ = processos.filter(p => p.LOCAL?.includes('SAJ')).length;
  
  if (processosNaSF > 8) {
    alertas.push({
      id: 'gargalo_sf',
      tipo: 'GARGALO_CRITICO',
      titulo: 'GARGALO CRÍTICO - SF',
      preview: `${processosNaSF} processos acumulados na Secretaria de Finanças`,
      processo: processos[0], // Processo exemplo
      dataAlerta: new Date(),
      lido: false,
      urgencia: 'ALTA',
      detalhes: {
        local: 'SF',
        diasParado: processosNaSF
      }
    });
  }
  
  if (processosNaSAJ > 8) {
    alertas.push({
      id: 'gargalo_saj',
      tipo: 'GARGALO_CRITICO',
      titulo: 'GARGALO CRÍTICO - SAJ',
      preview: `${processosNaSAJ} processos acumulados na Secretaria de Assuntos Jurídicos`,
      processo: processos[0], // Processo exemplo
      dataAlerta: new Date(),
      lido: false,
      urgencia: 'ALTA',
      detalhes: {
        local: 'SAJ',
        diasParado: processosNaSAJ
      }
    });
  }
  
  return alertas;
}

/**
 * ALERTA 5: RETRABALHOS
 * 📋 Regra: Processos RETORNADOS para adequação (precisa ação CLMP)
 */
export function verificarRetrabalho(processo: Processo): AlertaNotificacao | null {
  const status = processo.STATUS?.toLowerCase() || '';

  const statusRetrabalho = [
    'retornado para adequações',
    'devolvido para correções',
    'aguardando adequações da secretaria'
  ];

  if (statusRetrabalho.some(s => status.includes(s))) {
    return {
      id: `retrabalho_${processo.PROCESSO}`,
      tipo: 'RETRABALHO',
      titulo: `RETRABALHO - ${processo.PROCESSO}`,
      preview: `Retornado para adequações - Ação necessária`,
      processo,
      dataAlerta: new Date(),
      lido: false,
      urgencia: 'ALTA',
      detalhes: {
        valor: processo.VALOR,
        responsavel: processo.RESPONSÁVEL,
        motivo: processo.STATUS
      }
    };
  }

  return null;
}

/**
 * ALERTA 6: ENCAMINHAMENTO DIRETO PELO REQUISITANTE
 * 📋 Regra: Detecta quando requisitante encaminhou diretamente para SF/SAJ sem passar pela CLMP
 * 🎯 Situação: Processo vai direto da secretaria para SF ou SAJ para impacto financeiro/parecer jurídico
 * 👥 Destinatário: QUALQUER usuário da CLMP que estiver processando o processo
 */
export function verificarEncaminhamentoDiretoRequisitante(processo: Processo): AlertaNotificacao | null {
  const status = processo.STATUS?.toLowerCase() || '';
  const local = processo.LOCAL?.toLowerCase() || '';
  const dataEntradaCLMP = processo['DATA ENTRADA NA CLMP'];
  const dataInicio = processo['DATA DE INÍCIO DO PROCESSO'];

  // Indicadores de encaminhamento direto:
  // 1. Status indica que foi para SF/SAJ para análise específica
  // 2. Não há data de entrada na CLMP OU entrada muito próxima do início
  // 3. Local atual é SF ou SAJ OU retornou para CLMP após análise externa

  const statusEncaminhamentoDireto = [
    'para prosseguimento após impacto financeiro',
    'encaminhado para manifestação - adequação ao pca e disponibilidade orçamentária',
    'para prosseguimento após parecer jurídico',
    'encaminhado para emissão de parecer jurídico'
  ];

  const localSFouSAJ = local.includes('sf') || local.includes('saj');
  const localCLMP = local.includes('clmp');
  const statusIndicaEncaminhamentoDireto = statusEncaminhamentoDireto.some(s => status.includes(s));

  // Verificar se há indícios de encaminhamento direto
  let indiciosEncaminhamentoDireto = false;
  let motivo = '';

  // Caso 1: Status indica retorno de análise externa + está na CLMP
  if (statusIndicaEncaminhamentoDireto && localCLMP) {
    indiciosEncaminhamentoDireto = true;
    motivo = `Status "${processo.STATUS}" indica retorno de análise externa (SF/SAJ)`;
  }

  // Caso 2: Status indica análise externa + está em SF/SAJ
  if (statusIndicaEncaminhamentoDireto && localSFouSAJ) {
    indiciosEncaminhamentoDireto = true;
    motivo = `Status "${processo.STATUS}" com localização em ${processo.LOCAL} indica encaminhamento direto`;
  }

  // Verificar padrão temporal suspeito
  if (dataEntradaCLMP && dataInicio) {
    try {
      const [diaI, mesI, anoI] = dataInicio.split('/');
      const [diaE, mesE, anoE] = dataEntradaCLMP.split('/');
      const inicio = new Date(parseInt(anoI), parseInt(mesI) - 1, parseInt(diaI));
      const entrada = new Date(parseInt(anoE), parseInt(mesE) - 1, parseInt(diaE));
      const diasEntreCLMP = Math.floor((entrada.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24));

      // Se passou muito tempo entre início e entrada na CLMP, pode ter ido direto para SF/SAJ
      if (diasEntreCLMP > 30 && statusIndicaEncaminhamentoDireto) {
        indiciosEncaminhamentoDireto = true;
        motivo += ` | ${diasEntreCLMP} dias entre início e entrada na CLMP`;
      }
    } catch (error) {
      // Ignorar erro de parsing de data
    }
  }

  if (indiciosEncaminhamentoDireto) {
    return {
      id: `encaminhamento_direto_${processo.PROCESSO}`,
      tipo: 'ENCAMINHAMENTO_DIRETO_REQUISITANTE',
      titulo: `CONFIRMAR ENCAMINHAMENTO DIRETO - ${processo.PROCESSO}`,
      preview: `Requisitante pode ter encaminhado direto para análise externa`,
      processo,
      dataAlerta: new Date(),
      lido: false,
      urgencia: 'MEDIA',
      detalhes: {
        valor: processo['VALOR ESTIMADO'],
        responsavel: 'Qualquer usuário CLMP',
        local: processo.LOCAL,
        motivo: motivo,
        requisitante: processo.REQUISITANTE
      }
    };
  }

  return null;
}

/**
 * ALERTA 6: PRIORIDADE CLASSIFICADA
 * 📋 Regra: Quando botão "Prioridades do Governo" é acionado
 * 🚨 CRÍTICO: Alerta para Marcos + usuário atual
 */
export function criarAlertaPrioridadeClassificada(
  processo: Processo, 
  classificadoPor: string
): AlertaNotificacao {
  return {
    id: `classificada_${processo.PROCESSO}`,
    tipo: 'PRIORIDADE_CLASSIFICADA',
    titulo: `PRIORIDADE CLASSIFICADA - ${processo.PROCESSO}`,
    preview: `Classificado como ALTA por ${classificadoPor} - ${processo.VALOR}`,
    processo,
    dataAlerta: new Date(),
    lido: false,
    urgencia: 'CRITICA',
    detalhes: {
      valor: processo.VALOR,
      responsavel: processo.RESPONSÁVEL,
      local: processo.LOCAL,
      classificadoPor,
      motivo: 'Prioridade do Governo'
    }
  };
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 SEÇÃO 3: FUNÇÕES AUXILIARES
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * OBTÉM MOTIVOS DA PRIORIDADE ALTA
 */
export function getMotivosPrioridade(processo: Processo): string[] {
  const motivos: string[] = [];

  if (temFonteNaoTesouro(processo)) motivos.push('Fonte não-tesouro');
  if (temDemandasJudiciais(processo)) motivos.push('Demanda judicial');
  if (temAlimentacaoEscolar(processo)) motivos.push('Alimentação escolar');
  if (temMedicamentos(processo)) motivos.push('Medicamentos/Saúde');
  if (temPrioridadeGoverno(processo)) motivos.push('Prioridade do Governo');

  return motivos;
}

/**
 * OBTÉM ESTILO VISUAL DA PRIORIDADE
 */
export function getEstiloPrioridade(processo: Processo) {
  if (temPrioridadeAlta(processo)) {
    return {
      cor: 'text-red-600 bg-red-100 border-red-200',
      icone: '🔴',
      badge: 'PRIORIDADE ALTA',
      descricao: getMotivosPrioridade(processo).join(', ')
    };
  }

  return {
    cor: 'text-blue-600 bg-blue-100 border-blue-200',
    icone: '🔵',
    badge: 'NORMAL',
    descricao: 'Processo com prioridade normal'
  };
}

/**
 * GERA TODOS OS ALERTAS PARA UM PROCESSO
 */
export function gerarAlertasProcesso(processo: Processo): AlertaNotificacao[] {
  const alertas: AlertaNotificacao[] = [];

  const prazoCritico = verificarPrazoCritico(processo);
  if (prazoCritico) alertas.push(prazoCritico);

  const prioridadeParada = verificarPrioridadeParada(processo);
  if (prioridadeParada) alertas.push(prioridadeParada);

  const retrabalho = verificarRetrabalho(processo);
  if (retrabalho) alertas.push(retrabalho);

  const encaminhamentoDireto = verificarEncaminhamentoDiretoRequisitante(processo);
  if (encaminhamentoDireto) alertas.push(encaminhamentoDireto);

  return alertas;
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔥 SEÇÃO 4: MONITORAMENTO DO "EFEITO CAGÃO"
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * INTERFACE PARA MONITORAR IMPACTO DE PRIORIZAÇÃO MANUAL
 * 📋 Objetivo: Medir quanto a classificação manual impacta a eficiência
 */
export interface EfeitoCagao {
  usuario: string;
  processo: Processo;
  dataClassificacao: Date;
  impacto: {
    processosNormaisPausados: number;
    agentesRedirecionados: number;
    diasAtrasoGerados: number;
    valorTotalImpactado: number;
    tempoMedioCLMPAntes: number;
    tempoMedioCLMPDepois: number;
    produtividadeAntes: number; // %
    produtividadeDepois: number; // %
  };
}

/**
 * CALCULA O IMPACTO DE UMA CLASSIFICAÇÃO MANUAL
 */
export function calcularEfeitoCagao(
  processo: Processo,
  usuarioClassificador: string,
  processosEmAndamento: Processo[]
): EfeitoCagao {

  // Simular cálculos baseados nos dados reais
  const valorProcesso = parseFloat(processo.VALOR?.replace(/[R$\s.,]/g, '') || '0') / 100;

  const processosNormaisPausados = Math.floor(valorProcesso / 1000000) + 2; // Mais valor = mais impacto
  const agentesRedirecionados = Math.min(3, Math.floor(valorProcesso / 2000000) + 1);
  const diasAtrasoGerados = processosNormaisPausados * 2;
  const valorTotalImpactado = processosEmAndamento
    .filter(p => !temPrioridadeAlta(p))
    .slice(0, processosNormaisPausados)
    .reduce((total, p) => total + (parseFloat(p.VALOR?.replace(/[R$\s.,]/g, '') || '0') / 100), 0);

  return {
    usuario: usuarioClassificador,
    processo,
    dataClassificacao: new Date(),
    impacto: {
      processosNormaisPausados,
      agentesRedirecionados,
      diasAtrasoGerados,
      valorTotalImpactado,
      tempoMedioCLMPAntes: 8, // dias
      tempoMedioCLMPDepois: 8 + diasAtrasoGerados,
      produtividadeAntes: 100,
      produtividadeDepois: Math.max(40, 100 - (diasAtrasoGerados * 5))
    }
  };
}

/**
 * DETECÇÃO AUTOMÁTICA DE PROCESSOS ESPECIAIS
 * 📋 Baseado em dados reais do campo PRIORIDADE e padrões no OBJETO
 */

/**
 * PALAVRAS-CHAVE PARA SOLICITAÇÕES ESPECIAIS
 */
const PALAVRAS_ESPECIAIS = {
  solicitacoes: [
    'a pedido', 'solicitação especial', 'demanda especial', 'urgente',
    'prioridade', 'solicitação do secretário', 'pedido do prefeito',
    'demanda urgente', 'prazo apertado', 'emergencial'
  ],
  contextos: [
    'especializada', 'específico', 'especial', 'customizado',
    'sob medida', 'personalizado', 'exclusivo'
  ],
  status_especiais: [
    'encaminhado a pedido', 'recebido da agatha para continuação',
    'para análise da assessora', 'para análise do coordenador'
  ]
};

/**
 * VERIFICA SE UM PROCESSO É "ESPECIAL"
 * ✅ Baseado no campo SOLICITACAO_ESPECIAL com dados reais
 */
export function isProcessoEspecial(processo: Processo): boolean {
  return processo.SOLICITACAO_ESPECIAL?.ativa === true;
}

/**
 * OBTÉM O MOTIVO DA CLASSIFICAÇÃO COMO ESPECIAL
 */
export function getMotivoEspecial(processo: Processo): string[] {
  const motivos: string[] = [];

  const temPrioridadeAlta = processo.PRIORIDADE?.toLowerCase() === 'alta';
  const naoEhPrioridadeTecnica = !(
    temFonteNaoTesouro(processo) ||
    temDemandasJudiciais(processo) ||
    temAlimentacaoEscolar(processo) ||
    temMedicamentos(processo)
  );

  if (temPrioridadeAlta && naoEhPrioridadeTecnica) {
    motivos.push('Prioridade ALTA sem justificativa técnica');
  }

  const objeto = (processo.OBJETO || '').toLowerCase();
  const status = (processo.STATUS || '').toLowerCase();

  // Verificar palavras específicas
  PALAVRAS_ESPECIAIS.solicitacoes.forEach(palavra => {
    if (objeto.includes(palavra)) {
      motivos.push(`Solicitação especial: "${palavra}"`);
    }
  });

  PALAVRAS_ESPECIAIS.status_especiais.forEach(statusEsp => {
    if (status.includes(statusEsp)) {
      motivos.push(`Status especial: "${statusEsp}"`);
    }
  });

  return motivos.length > 0 ? motivos : ['Processo identificado como especial'];
}

/**
 * CALCULA ESTATÍSTICAS DE PROCESSOS ESPECIAIS POR SECRETARIA
 */
export function calcularEstatisticasEspeciais(processos: Processo[]): Array<{
  secretaria: string;
  totalProcessos: number;
  processosEspeciais: number;
  percentualEspeciais: number;
  classificacao: 'BAIXO' | 'MODERADO' | 'ALTO' | 'CRITICO';
  processosEspeciaisList: string[];
}> {

  const secretariaMap = new Map();

  processos.forEach(processo => {
    const secretaria = processo.REQUISITANTE || 'NÃO INFORMADO';

    if (!secretariaMap.has(secretaria)) {
      secretariaMap.set(secretaria, {
        secretaria,
        totalProcessos: 0,
        processosEspeciais: 0,
        processosEspeciaisList: []
      });
    }

    const stats = secretariaMap.get(secretaria);
    stats.totalProcessos++;

    if (isProcessoEspecial(processo)) {
      stats.processosEspeciais++;
      stats.processosEspeciaisList.push(processo.PROCESSO || '');
    }
  });

  return Array.from(secretariaMap.values())
    .map(stats => ({
      ...stats,
      percentualEspeciais: stats.totalProcessos > 0 ?
        (stats.processosEspeciais / stats.totalProcessos) * 100 : 0,
      classificacao: (() => {
        const percentual = (stats.processosEspeciais / stats.totalProcessos) * 100;
        if (percentual >= 40) return 'CRITICO';
        if (percentual >= 25) return 'ALTO';
        if (percentual >= 15) return 'MODERADO';
        return 'BAIXO';
      })()
    }))
    .filter(stats => stats.totalProcessos > 0)
    .sort((a, b) => b.percentualEspeciais - a.percentualEspeciais);
}

/**
 * GERA DASHBOARD DE "VERGONHA CONSTRUTIVA"
 */
export function gerarDashboardVergonha(processos: Processo[]) {
  const estatisticas = calcularEstatisticasEspeciais(processos);
  const totalProcessos = processos.length;
  const totalEspeciais = processos.filter(isProcessoEspecial).length;

  return {
    resumo: {
      totalProcessos,
      totalEspeciais,
      percentualGeralEspeciais: totalProcessos > 0 ?
        ((totalEspeciais / totalProcessos) * 100).toFixed(1) : '0',
      secretariasComProblemas: estatisticas.filter(s => s.classificacao !== 'BAIXO').length
    },
    ranking: estatisticas.slice(0, 10), // Top 10
    alertas: [
      ...estatisticas
        .filter(s => s.classificacao === 'CRITICO')
        .map(s => `🚨 CRÍTICO: ${s.secretaria} com ${s.percentualEspeciais.toFixed(1)}% de processos especiais`),
      ...estatisticas
        .filter(s => s.classificacao === 'ALTO')
        .map(s => `⚠️ ALTO: ${s.secretaria} com ${s.percentualEspeciais.toFixed(1)}% de processos especiais`)
    ],
    recomendacoes: [
      'Implementar treinamento para secretarias com alta incidência',
      'Revisar processos internos das secretarias críticas',
      'Estabelecer metas de redução de processos especiais',
      'Criar sistema de acompanhamento preventivo'
    ]
  };
}

/**
 * DASHBOARD DE IMPACTO OPERACIONAL
 */
export function gerarDashboardImpacto(efeitosCagao: EfeitoCagao[]) {
  const totalEfeitos = efeitosCagao.length;
  const diasPerdidosTotal = efeitosCagao.reduce((total, e) => total + e.impacto.diasAtrasoGerados, 0);
  const valorImpactadoTotal = efeitosCagao.reduce((total, e) => total + e.impacto.valorTotalImpactado, 0);
  const produtividadeMediaAntes = 100;
  const produtividadeMediaDepois = efeitosCagao.length > 0 ?
    efeitosCagao.reduce((total, e) => total + e.impacto.produtividadeDepois, 0) / efeitosCagao.length : 100;

  return {
    resumo: {
      totalClassificacoesManuais: totalEfeitos,
      diasPerdidosTotal,
      valorImpactadoTotal,
      perdaProdutividade: `${(produtividadeMediaAntes - produtividadeMediaDepois).toFixed(1)}%`,
      custoOperacional: valorImpactadoTotal * 0.02 // 2% do valor como custo operacional
    },
    metricas: {
      tempoMedioCLMPAntes: 8,
      tempoMedioCLMPDepois: 8 + (diasPerdidosTotal / totalEfeitos || 0),
      processosEmFilaAntes: 25,
      processosEmFilaDepois: 25 + (totalEfeitos * 3),
      eficienciaAntes: '100%',
      eficienciaDepois: `${produtividadeMediaDepois.toFixed(1)}%`
    },
    alertas: [
      diasPerdidosTotal > 50 ? 'CRÍTICO: Mais de 50 dias perdidos por classificações manuais' : null,
      valorImpactadoTotal > 10000000 ? 'ALTO: Mais de R$ 10 milhões em processos impactados' : null,
      produtividadeMediaDepois < 70 ? 'MÉDIO: Produtividade abaixo de 70%' : null
    ].filter(Boolean)
  };
}

// ═══════════════════════════════════════════════════════════════════════════════
// 📊 SEÇÃO 5: ESTATÍSTICAS E MÉTRICAS TRADICIONAIS
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * CALCULA ESTATÍSTICAS DE PRIORIDADES
 */
export function calcularEstatisticasPrioridade(processos: Processo[]) {
  const total = processos.length;
  const prioridadeAlta = processos.filter(temPrioridadeAlta).length;

  const porMotivo = {
    fonteNaoTesouro: processos.filter(temFonteNaoTesouro).length,
    judicial: processos.filter(temDemandasJudiciais).length,
    alimentacao: processos.filter(temAlimentacaoEscolar).length,
    medicamentos: processos.filter(temMedicamentos).length,
    governo: processos.filter(temPrioridadeGoverno).length
  };

  return {
    total,
    prioridadeAlta,
    prioridadeNormal: total - prioridadeAlta,
    percentualAlta: total > 0 ? ((prioridadeAlta / total) * 100).toFixed(1) : '0',
    porMotivo
  };
}

/**
 * CALCULA MÉTRICAS DE EFICIÊNCIA (SEM RISCO DE PERDA)
 * ⚠️ IMPORTANTE: Removida métrica "riscoPerdaRecursos" conforme solicitado
 */
export function calcularMetricasEficiencia(processos: Processo[]) {
  const total = processos.length;
  const prioridadeAlta = processos.filter(temPrioridadeAlta).length;

  // TODO: Implementar cálculos reais de tempo baseados em dados CSV
  const tempoMedioCLMP = 8; // dias - calcular com dados reais
  const tempoMedioSecretaria = 15; // dias - calcular com dados reais

  return {
    total,
    prioridadeAlta,
    prioridadeNormal: total - prioridadeAlta,
    percentualAlta: total > 0 ? ((prioridadeAlta / total) * 100).toFixed(1) : '0',
    tempoMedioCLMP,
    tempoMedioSecretaria,
    eficienciaCLMP: tempoMedioSecretaria > 0 ?
      ((tempoMedioSecretaria - tempoMedioCLMP) / tempoMedioSecretaria * 100).toFixed(1) : '0'
  };
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🔄 SEÇÃO 5: COMPATIBILIDADE COM CÓDIGO EXISTENTE
// ═══════════════════════════════════════════════════════════════════════════════

/**
 * EXPORTAÇÕES PARA MANTER COMPATIBILIDADE
 */
export { temFonteNaoTesouro as temFonteNaoTesouroLegacy };
export { temPrioridadeAlta as getPrioridadeProcesso };

/**
 * STATUS ESPECIAIS DO SISTEMA
 */
export const STATUS_ESPECIAIS = {
  AGUARDANDO_CLASSIFICACAO: 'Aguardando classificação de prioridade',
  PRIORIDADE_CLASSIFICADA: 'Prioridade classificada pelo governo',
  TIMEOUT_NORMAL: 'Auto-classificado como normal (timeout 7 dias)'
} as const;

/**
 * CONFIGURAÇÕES DO SISTEMA
 */
export const CONFIG_SISTEMA = {
  TIMEOUT_CLASSIFICACAO_DIAS: 7,
  LIMITE_GARGALO_SF: 8,
  LIMITE_GARGALO_SAJ: 8,
  DIAS_ALERTA_PRIORIDADE_PARADA: 5,
  DIAS_ALERTA_PRAZO_CRITICO: 15,
  USUARIOS_HABILITADOS_GOVERNO: [
    'Secretário de Governo',
    'Secretária Adjunta de Governo'
  ]
} as const;
