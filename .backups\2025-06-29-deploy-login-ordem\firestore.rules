rules_version='2'

service cloud.firestore {
  match /databases/{database}/documents {

    // Coleção de usuários: permitir criação do primeiro usuário e sistema de aprovação
    match /usuarios/{userId} {
      // Permitir criação do primeiro usuário (admin automático)
      allow create: if request.auth != null && request.auth.uid == userId;
      
      // Permitir que usuários leiam/editem apenas seu próprio documento
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Permitir que admins leiam todos os usuários (para gestão)
      allow read: if request.auth != null && isAdmin();
      
      // Permitir que admins atualizem status de outros usuários (aprovação/reprovação)
      allow update: if request.auth != null && isAdmin() && 
        (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'perfil', 'dataAprovacao', 'aprovadoPor', 'observacoes']));
    }

    // Configurações: todos autenticados podem ler, só admin pode escrever
    match /configuracoes/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin();
    }

    // Termos de uso: usuários autenticados podem ler e registrar aceitações
    match /configuracoes/termos_de_uso {
      allow read: if request.auth != null;
      allow update: if request.auth != null && 
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['aceitacoes', 'dataAtualizacao']);
    }

    // Dashboard: todos autenticados podem ler, só admin pode escrever
    match /dashboard/{docId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin();
    }

    // Processos: regras completas
    match /processos/{docId} {
      // Leitura:
      // - CLMP e admin veem todos os processos
      // - Secretaria municipal só vê processos da própria secretaria
      allow read: if request.auth != null && (isClmp() || isAdmin()
        || (isSecretariaMunicipal() && resource.data.secretariaId == getUser().data.secretariaId));

      // Escrita:
      // - Admin pode tudo
      // - CLMP pode editar conforme a fase do processo
      // - Secretaria municipal só pode editar processos da própria secretaria
      allow write: if request.auth != null && (isAdmin()
        || podeEditarProcesso(resource.data.fase, getUser().data.perfil)
        || (isSecretariaMunicipal() && resource.data.secretariaId == getUser().data.secretariaId));
    }

    // Pesquisa de preços: só pesquisadores e admin podem editar, todos da CLMP podem ler
    match /pesquisa_precos/{docId} {
      allow read: if request.auth != null && (isClmp() || isAdmin());
      allow write: if request.auth != null && (isAdmin() || getUser().data.perfil == "pesquisador");
    }

    // Análise de editais: só agentes de contratação e admin podem editar, todos da CLMP podem ler
    match /analise_editais/{docId} {
      allow read: if request.auth != null && (isClmp() || isAdmin());
      allow write: if request.auth != null && (isAdmin() || getUser().data.perfil == "gestor");
    }
  }

  // Funções auxiliares
  function getUser() {
    return get(/databases/$(database)/documents/usuarios/$(request.auth.uid));
  }
  function isAdmin() {
    return getUser().data.perfil == "admin";
  }
  function isClmp() {
    // Todos os papéis da CLMP: ajuste conforme necessário
    let perfil = getUser().data.perfil;
    return perfil == "admin" ||
           perfil == "gestor" ||
           perfil == "pesquisador" ||
           perfil == "analista";
  }
  function isSecretariaMunicipal() {
    return getUser().data.perfil == "secretaria";
  }
  // Função que define quem pode editar em cada fase do processo
  function podeEditarProcesso(fase, perfil) {
    return
      (fase == "em_pesquisa" && perfil == "pesquisador") ||
      (fase == "em_analise" && perfil == "gestor") ||
      (fase == "em_contrato" && perfil == "gestor") ||
      (fase == "em_assessoria" && perfil == "gestor") ||
      (fase == "em_expediente" && perfil == "gestor") ||
      (fase == "em_apoio" && perfil == "gestor");
  }
}
