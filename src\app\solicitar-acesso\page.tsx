'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  TipoOrganizacao, 
  NivelAcesso, 
  CONFIGURACOES_ORGANIZACAO, 
  OPCOES_PREFEITURA_MAUA,
  PERMISSOES_NIVEL 
} from '@/types/organizacao';
import { criarSolicitacaoAcesso } from '@/lib/solicitacaoAcesso';

export default function SolicitarAcesso() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const [sucesso, setSucesso] = useState(false);

  // Estados do formulário
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    organizacao: {
      nome: '',
      tipo: '' as TipoOrganizacao,
      estrutura: {}
    },
    nivel_acesso_solicitado: 'operador' as NivelAcesso,
    justificativa: ''
  });

  const handleTipoOrganizacaoChange = (tipo: TipoOrganizacao) => {
    setFormData(prev => ({
      ...prev,
      organizacao: {
        ...prev.organizacao,
        tipo,
        nome: tipo === 'prefeitura' ? 'Prefeitura Municipal de Mauá' : '',
        estrutura: {}
      }
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErro(null);

    try {
      // Validações simplificadas
      if (!formData.nome || !formData.email || !formData.organizacao.tipo) {
        throw new Error('Preencha todos os campos obrigatórios');
      }

      if (!formData.email.endsWith('@gmail.com')) {
        throw new Error('Apenas emails do Gmail são aceitos');
      }

      // Para organizações que não são prefeitura, validar nome
      if (formData.organizacao.tipo !== 'prefeitura' && !formData.organizacao.nome) {
        throw new Error('Nome da organização é obrigatório');
      }

      await criarSolicitacaoAcesso(formData);
      setSucesso(true);
      
      // Redirecionar após 3 segundos
      setTimeout(() => {
        router.push('/login');
      }, 3000);

    } catch (error: any) {
      setErro(error.message);
    } finally {
      setLoading(false);
    }
  };



  // Página de sucesso
  if (sucesso) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden flex items-center justify-center p-4">
        {/* Space background */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/3 to-cyan-500/3 rounded-full blur-3xl"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl shadow-xl p-8 max-w-md w-full text-center"
        >
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-white mb-4">
            Solicitação Enviada!
          </h2>
          <p className="text-gray-300 mb-6">
            Sua solicitação de acesso foi enviada com sucesso. Você receberá uma resposta em breve.
          </p>
          <p className="text-sm text-gray-400">
            Redirecionando para o login...
          </p>
        </motion.div>
      </div>
    );
  }

  // Página principal do formulário
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-black relative overflow-hidden py-8 px-4">
      {/* Space background */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/3 to-cyan-500/3 rounded-full blur-3xl"></div>

        {/* Floating stars */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
        <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-cyan-300 rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping"></div>

        {/* Moving asteroids */}
        <motion.div
          className="absolute top-10 right-10 w-3 h-3 bg-gray-600 rounded-full opacity-30"
          animate={{
            x: [-100, 100, -100],
            y: [0, -50, 0],
            rotate: [0, 360, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="max-w-2xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl shadow-xl p-8"
        >
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Solicitar Acesso
            </h1>
            <p className="text-gray-300">
              Apenas 3 campos essenciais
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="space-y-6">

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Nome Completo *
                </label>
                <input
                  type="text"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 transition-all duration-200"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email (Gmail) *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 transition-all duration-200"
                  placeholder="<EMAIL>"
                  required
                />
              </div>


            </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Organização *
                </label>
                <select
                  value={formData.organizacao.tipo}
                  onChange={(e) => handleTipoOrganizacaoChange(e.target.value as TipoOrganizacao)}
                  className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800/50 backdrop-blur-sm text-white transition-all duration-200"
                  required
                >
                  <option value="" className="bg-gray-800 text-gray-400">Selecione sua organização</option>
                  {Object.entries(CONFIGURACOES_ORGANIZACAO).map(([key, config]) => (
                    <option key={key} value={key} className="bg-gray-800 text-white">{config.nome}</option>
                  ))}
                </select>
              </div>

              {formData.organizacao.tipo && formData.organizacao.tipo !== 'prefeitura' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Nome da Organização *
                  </label>
                  <input
                    type="text"
                    value={formData.organizacao.nome}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      organizacao: { ...prev.organizacao, nome: e.target.value }
                    }))}
                    className="w-full px-4 py-3 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 transition-all duration-200"
                    required
                  />
                </div>
              )}
            </div>







            {/* Erro */}
            {erro && (
              <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 backdrop-blur-sm">
                <p className="text-red-300 text-sm">{erro}</p>
              </div>
            )}

            {/* Botões */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="button"
                onClick={() => router.push('/login')}
                className="flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700/50 transition-colors backdrop-blur-sm"
              >
                Voltar ao Login
              </button>

              <button
                type="submit"
                disabled={loading}
                className="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors flex items-center justify-center backdrop-blur-sm"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Enviando...
                  </>
                ) : (
                  'Enviar Solicitação'
                )}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  );
}
