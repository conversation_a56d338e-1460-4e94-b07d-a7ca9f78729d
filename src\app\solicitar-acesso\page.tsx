'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  TipoOrganizacao, 
  NivelAcesso, 
  CONFIGURACOES_ORGANIZACAO, 
  OPCOES_PREFEITURA_MAUA,
  PERMISSOES_NIVEL 
} from '@/types/organizacao';
import { criarSolicitacaoAcesso } from '@/lib/solicitacaoAcesso';

export default function SolicitarAcesso() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const [sucesso, setSucesso] = useState(false);

  // Estados do formulário
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefone: '',
    organizacao: {
      nome: '',
      tipo: '' as TipoOrganizacao,
      estrutura: {}
    },
    nivel_acesso_solicitado: 'operador' as NivelAcesso,
    justificativa: ''
  });

  const handleTipoOrganizacaoChange = (tipo: TipoOrganizacao) => {
    setFormData(prev => ({
      ...prev,
      organizacao: {
        ...prev.organizacao,
        tipo,
        nome: tipo === 'prefeitura' ? 'Prefeitura Municipal de Mauá' : '',
        estrutura: {}
      }
    }));
  };

  const handleEstruturaChange = (campo: string, valor: string) => {
    setFormData(prev => ({
      ...prev,
      organizacao: {
        ...prev.organizacao,
        estrutura: {
          ...prev.organizacao.estrutura,
          [campo]: valor
        }
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErro(null);

    try {
      // Validações
      if (!formData.nome || !formData.email || !formData.organizacao.tipo) {
        throw new Error('Preencha todos os campos obrigatórios');
      }

      if (!formData.email.endsWith('@gmail.com')) {
        throw new Error('Apenas emails do Gmail são aceitos');
      }

      const config = CONFIGURACOES_ORGANIZACAO[formData.organizacao.tipo];
      for (const campo of config.campos_obrigatorios) {
        if (!formData.organizacao.estrutura[campo as keyof typeof formData.organizacao.estrutura]) {
          throw new Error(`Campo ${campo} é obrigatório para ${config.nome}`);
        }
      }

      await criarSolicitacaoAcesso(formData);
      setSucesso(true);
      
      // Redirecionar após 3 segundos
      setTimeout(() => {
        router.push('/login');
      }, 3000);

    } catch (error: any) {
      setErro(error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderCamposEstrutura = () => {
    if (!formData.organizacao.tipo) return null;

    const config = CONFIGURACOES_ORGANIZACAO[formData.organizacao.tipo];
    
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Estrutura Organizacional
        </h3>
        
        {formData.organizacao.tipo === 'prefeitura' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Secretaria *
              </label>
              <select
                value={formData.organizacao.estrutura.secretaria || ''}
                onChange={(e) => handleEstruturaChange('secretaria', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="">Selecione a Secretaria</option>
                {OPCOES_PREFEITURA_MAUA.secretarias.map(secretaria => (
                  <option key={secretaria} value={secretaria}>{secretaria}</option>
                ))}
              </select>
            </div>

            {formData.organizacao.estrutura.secretaria === 'Governo' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Setor *
                </label>
                <select
                  value={formData.organizacao.estrutura.setor || ''}
                  onChange={(e) => handleEstruturaChange('setor', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                >
                  <option value="">Selecione o Setor</option>
                  {OPCOES_PREFEITURA_MAUA.setores_governo.map(setor => (
                    <option key={setor} value={setor}>{setor}</option>
                  ))}
                </select>
              </div>
            )}

            {formData.organizacao.estrutura.setor === 'CLMP' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Função *
                </label>
                <select
                  value={formData.organizacao.estrutura.funcao || ''}
                  onChange={(e) => handleEstruturaChange('funcao', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                >
                  <option value="">Selecione a Função</option>
                  {OPCOES_PREFEITURA_MAUA.funcoes_clmp.map(funcao => (
                    <option key={funcao} value={funcao}>{funcao}</option>
                  ))}
                </select>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Matrícula
              </label>
              <input
                type="text"
                value={formData.organizacao.estrutura.matricula || ''}
                onChange={(e) => handleEstruturaChange('matricula', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Número da matrícula (opcional)"
              />
            </div>
          </>
        )}

        {/* Campos genéricos para outros tipos */}
        {formData.organizacao.tipo !== 'prefeitura' && (
          <>
            {config.campos_obrigatorios.includes('departamento') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Departamento *
                </label>
                <input
                  type="text"
                  value={formData.organizacao.estrutura.departamento || ''}
                  onChange={(e) => handleEstruturaChange('departamento', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>
            )}

            {config.campos_obrigatorios.includes('cargo') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cargo *
                </label>
                <input
                  type="text"
                  value={formData.organizacao.estrutura.cargo || ''}
                  onChange={(e) => handleEstruturaChange('cargo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  if (sucesso) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Solicitação Enviada!
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Sua solicitação de acesso foi enviada com sucesso. Você receberá uma resposta em breve.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Redirecionando para o login...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Solicitar Acesso
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Preencha os dados para solicitar acesso ao sistema
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Dados Pessoais */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Dados Pessoais
              </h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Nome Completo *
                </label>
                <input
                  type="text"
                  value={formData.nome}
                  onChange={(e) => setFormData(prev => ({ ...prev, nome: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email (Gmail) *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Telefone
                </label>
                <input
                  type="tel"
                  value={formData.telefone}
                  onChange={(e) => setFormData(prev => ({ ...prev, telefone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="(11) 99999-9999"
                />
              </div>
            </div>

            {/* Tipo de Organização */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Organização
              </h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Organização *
                </label>
                <select
                  value={formData.organizacao.tipo}
                  onChange={(e) => handleTipoOrganizacaoChange(e.target.value as TipoOrganizacao)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                >
                  <option value="">Selecione o tipo</option>
                  {Object.entries(CONFIGURACOES_ORGANIZACAO).map(([key, config]) => (
                    <option key={key} value={key}>{config.nome}</option>
                  ))}
                </select>
              </div>

              {formData.organizacao.tipo && formData.organizacao.tipo !== 'prefeitura' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome da Organização *
                  </label>
                  <input
                    type="text"
                    value={formData.organizacao.nome}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      organizacao: { ...prev.organizacao, nome: e.target.value }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    required
                  />
                </div>
              )}
            </div>

            {/* Campos dinâmicos da estrutura */}
            {renderCamposEstrutura()}

            {/* Nível de Acesso */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Nível de Acesso Solicitado
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(PERMISSOES_NIVEL).map(([nivel, config]) => (
                  <label key={nivel} className="flex items-start space-x-3 p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                    <input
                      type="radio"
                      name="nivel_acesso"
                      value={nivel}
                      checked={formData.nivel_acesso_solicitado === nivel}
                      onChange={(e) => setFormData(prev => ({ ...prev, nivel_acesso_solicitado: e.target.value as NivelAcesso }))}
                      className="mt-1"
                    />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {config.nome}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {config.descricao}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Justificativa */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Justificativa *
              </label>
              <textarea
                value={formData.justificativa}
                onChange={(e) => setFormData(prev => ({ ...prev, justificativa: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Explique por que precisa de acesso ao sistema..."
                required
              />
            </div>

            {/* Erro */}
            {erro && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p className="text-red-600 dark:text-red-400 text-sm">{erro}</p>
              </div>
            )}

            {/* Botões */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="button"
                onClick={() => router.push('/login')}
                className="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Voltar ao Login
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Enviando...
                  </>
                ) : (
                  'Enviar Solicitação'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
