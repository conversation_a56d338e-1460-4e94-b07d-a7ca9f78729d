'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

export default function SolicitarAcesso() {
  const router = useRouter();

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-black overflow-hidden">
      {/* Space background - IGUAL À SUSPENSE */}
      <div className="absolute inset-0">
        {/* Nebula effects */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/3 to-cyan-500/3 rounded-full blur-3xl"></div>

        {/* Floating stars */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
        <div className="absolute top-3/4 left-3/4 w-1 h-1 bg-cyan-300 rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping"></div>
        <div className="absolute bottom-1/3 left-1/3 w-1 h-1 bg-cyan-400 rounded-full animate-pulse"></div>
      </div>

      {/* Floating text elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[
          'INOVAÇÃO', 'EFICIÊNCIA', 'GESTÃO', 'ESTRATÉGIA', 'ANALYTICS', 'PERFORMANCE',
          'OTIMIZAÇÃO', 'INTELIGÊNCIA', 'DADOS', 'PROCESSOS', 'RESULTADOS', 'TECNOLOGIA',
          'AUTOMAÇÃO', 'CONTROLE', 'MONITORAMENTO', 'INSIGHTS', 'MÉTRICAS', 'QUALIDADE',
          'PRODUTIVIDADE', 'EXCELÊNCIA', 'TRANSFORMAÇÃO', 'SOLUÇÕES', 'COMPETITIVIDADE',
          'CRESCIMENTO', 'SUSTENTABILIDADE', 'GOVERNANÇA', 'COMPLIANCE', 'AUDITORIA',
          'TRANSPARÊNCIA', 'ACCOUNTABILITY', 'BENCHMARKING', 'DASHBOARD', 'RELATÓRIOS',
          'INDICADORES', 'METAS', 'OBJETIVOS', 'PLANEJAMENTO', 'EXECUÇÃO', 'AVALIAÇÃO',
          'MELHORIA', 'CONTINUOUS', 'IMPROVEMENT', 'LEAN', 'AGILE', 'SCRUM', 'KANBAN',
          'WORKFLOW', 'PIPELINE', 'INTEGRATION', 'SCALABILITY', 'RELIABILITY', 'SECURITY',
          'INTELLIGENCE', 'STRATEGIC', 'VISION', 'FUTURE', 'INNOVATION', 'EXCELLENCE'
        ].map((word, index) => (
          <div
            key={index}
            className="absolute text-white/10 text-sm font-light animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 10}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          >
            {word}
          </div>
        ))}
      </div>

      {/* Grid pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.02)_1px,transparent_1px)] bg-[size:80px_80px] opacity-30"></div>

      {/* Questionário centralizado */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <h1 className="text-3xl font-bold text-white mb-4">
            Solicitar Acesso
          </h1>
          <p className="text-gray-300 mb-6">
            Preencha os dados para solicitar acesso ao sistema
          </p>

          <div className="space-y-4 mb-6">
            <input
              type="text"
              placeholder="Nome completo"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
            <input
              type="email"
              placeholder="Email corporativo"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
            <input
              type="text"
              placeholder="Organização"
              className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => router.push('/login')}
              className="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Solicitar Acesso
            </button>
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              Voltar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
