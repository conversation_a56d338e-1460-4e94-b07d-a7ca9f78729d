'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { LucideIcon, TrendingUp, TrendingDown, Info, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedMetricCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  tooltipContent?: string;
  breakdown?: Array<{ label: string; value: number | string; percentage?: number }>;
  showChart?: boolean;
  previousValue?: number;
}

export function EnhancedMetricCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  variant = 'default',
  tooltipContent,
  breakdown,
  showChart = false,
  previousValue
}: EnhancedMetricCardProps) {
  const variantStyles = {
    default: 'border-slate-200/60 bg-gradient-to-br from-slate-50/80 via-gray-50/70 to-slate-100/80 dark:border-slate-500/70 dark:from-slate-600/30 dark:via-slate-500/20 dark:to-slate-400/15 hover:border-slate-300 hover:shadow-lg hover:shadow-slate-200/50 dark:hover:shadow-slate-600/60 hover:scale-[1.02] transition-all duration-300',
    success: 'border-emerald-300/60 bg-gradient-to-br from-emerald-200/70 via-green-200/60 to-teal-200/70 dark:border-emerald-500/70 dark:from-emerald-700/30 dark:via-green-600/20 dark:to-teal-600/15 hover:border-emerald-400 hover:shadow-lg hover:shadow-emerald-300/50 dark:hover:shadow-emerald-600/60 hover:scale-[1.02] transition-all duration-300',
    warning: 'border-amber-300/60 bg-gradient-to-br from-amber-200/70 via-yellow-200/60 to-orange-200/70 dark:border-amber-500/70 dark:from-amber-700/30 dark:via-yellow-600/20 dark:to-orange-600/15 hover:border-amber-400 hover:shadow-lg hover:shadow-amber-300/50 dark:hover:shadow-amber-600/60 hover:scale-[1.02] transition-all duration-300',
    destructive: 'border-rose-300/60 bg-gradient-to-br from-rose-200/70 via-red-200/60 to-pink-200/70 dark:border-rose-500/70 dark:from-rose-700/30 dark:via-red-600/20 dark:to-pink-600/15 hover:border-rose-400 hover:shadow-lg hover:shadow-rose-300/50 dark:hover:shadow-rose-600/60 hover:scale-[1.02] transition-all duration-300'
  };

  const iconStyles = {
    default: 'text-muted-foreground',
    success: 'text-green-600 dark:text-green-300',
    warning: 'text-yellow-600 dark:text-yellow-300',
    destructive: 'text-red-600 dark:text-red-300'
  };

  const gradientStyles = {
    default: 'from-slate-600 to-slate-700 dark:from-slate-200 dark:to-slate-50',
    success: 'from-emerald-600 to-green-700 dark:from-emerald-300 dark:to-green-200',
    warning: 'from-amber-600 to-orange-700 dark:from-amber-300 dark:to-orange-200',
    destructive: 'from-rose-600 to-red-700 dark:from-rose-300 dark:to-red-200'
  };

  const CardWrapper = ({ children }: { children: React.ReactNode }) => {
    if (tooltipContent || breakdown) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {children}
            </TooltipTrigger>
            <TooltipContent side="top" align="start" className="max-w-2xl p-4" sideOffset={10} avoidCollisions={false}>
              {tooltipContent && (
                <div className="mb-2">
                  <p className="text-sm">{tooltipContent}</p>
                </div>
              )}
              {breakdown && (
                <div className="space-y-1">
                  {breakdown.slice(0, 5).map((item, index) => {
                    // Regra para reduzir o objeto automaticamente
                    const objetoReduzido = typeof item.value === 'string' && item.value.length > 50
                      ? item.value.substring(0, 50) + '...'
                      : item.value;

                    return (
                      <div key={index} className="text-xs text-muted-foreground font-mono">
                        {item.label} - {objetoReduzido}
                      </div>
                    );
                  })}
                </div>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return <>{children}</>;
  };

  return (
    <CardWrapper>
      <Card className={cn(
        variantStyles[variant], 
        'transition-all duration-300 cursor-pointer group hover:scale-105',
        className
      )}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors">
            {title}
            {(tooltipContent || breakdown) && (
              <Info className="inline-block w-3 h-3 ml-1 opacity-50 group-hover:opacity-100" />
            )}
          </CardTitle>
          <div className="flex items-center space-x-1">
            {showChart && (
              <BarChart3 className="h-4 w-4 text-muted-foreground group-hover:text-blue-500 transition-colors" />
            )}
            <Icon className={cn('h-5 w-5 group-hover:scale-110 transition-transform', iconStyles[variant])} />
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="relative">
            {/* Valor principal com gradiente */}
            <div className={cn(
              "text-3xl font-bold mb-1 bg-gradient-to-r bg-clip-text text-transparent",
              gradientStyles[variant]
            )}>
              {typeof value === 'number' ? value.toLocaleString('pt-BR') : value}
            </div>
            
            {/* Barra de progresso visual */}
            {typeof value === 'number' && breakdown && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-2 overflow-hidden">
                <div
                  className={cn("h-1 rounded-full bg-gradient-to-r transition-all duration-1000", gradientStyles[variant])}
                  style={{ width: `${Math.min((value / 150) * 100, 100)}%` }}
                />
              </div>
            )}
            
            {description && (
              <p className="text-sm text-muted-foreground mb-2 group-hover:text-foreground transition-colors">
                {description}
              </p>
            )}

            {trend && (
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={trend.isPositive ? "default" : "destructive"}
                  className="text-xs flex items-center space-x-1"
                >
                  {trend.isPositive ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  <span>{trend.value > 0 ? '+' : ''}{trend.value}%</span>
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {trend.label}
                </span>
              </div>
            )}

            {/* Mini breakdown visual */}
            {breakdown && breakdown.length <= 3 && (
              <div className="mt-3 space-y-1">
                {breakdown.slice(0, 3).map((item, index) => (
                  <div key={index} className="flex justify-between text-xs opacity-70 group-hover:opacity-100 transition-opacity">
                    <span className="truncate max-w-24 text-muted-foreground">{item.label}</span>
                    <span className="font-medium text-foreground">{item.value}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {previousValue !== undefined && (
            <div className="flex justify-end mt-2">
              <span className="text-xs text-muted-foreground">{previousValue}</span>
            </div>
          )}
        </CardContent>
      </Card>
    </CardWrapper>
  );
}
