const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const path = require('path');
const fs = require('fs');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

// Inicializa o app Next.js
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    const { pathname } = parsedUrl;
    
    // Manipulação especial para favicon.ico e favicon.png
    if (pathname === '/favicon.ico' || pathname === '/favicon.png') {
      const iconPath = path.join(__dirname, 'public', pathname);
      if (fs.existsSync(iconPath)) {
        const contentType = pathname.endsWith('.ico') ? 'image/x-icon' : 'image/png';
        res.setHeader('Content-Type', contentType);
        fs.createReadStream(iconPath).pipe(res);
        return;
      }
    }
    
    // Deixa o Next.js lidar com a requisição
    handle(req, res, parsedUrl);
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(`> Servidor pronto em http://${hostname}:${port}`);
    console.log(`> Modo: ${dev ? 'desenvolvimento' : 'produção'}`);
    console.log('> Compilação concluída com sucesso! ✅');
    console.log('> Pressione CTRL+C para parar o servidor');
  });
}).catch(err => {
  console.error('Erro ao iniciar o servidor:', err);
  process.exit(1);
});


