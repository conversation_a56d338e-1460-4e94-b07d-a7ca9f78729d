const fs = require('fs');
const path = require('path');

// 🔒 SCRIPT DE VALIDAÇÃO DA TRAVA DO BANCO DE DADOS
// PONTO BASE: 05/06/2025 - 133 PROCESSOS

function validateDatabaseLock() {
  console.log('🔒 VALIDANDO TRAVA DO BANCO DE DADOS...');
  
  const dataDir = path.join(__dirname, '../../data');
  const baseFile = path.join(dataDir, 'BCO-DADOS-05-06-25.csv');
  const lockFile = path.join(dataDir, 'TRAVA-BCO-DADOS.md');
  
  // Verificar se arquivos de trava existem
  if (!fs.existsSync(baseFile)) {
    console.error('🚨 VIOLAÇÃO: Arquivo base BCO-DADOS-05-06-25.csv não encontrado!');
    return false;
  }
  
  if (!fs.existsSync(lockFile)) {
    console.error('🚨 VIOLAÇÃO: Arquivo de trava TRAVA-BCO-DADOS.md não encontrado!');
    return false;
  }
  
  try {
    // Ler e validar arquivo base
    const content = fs.readFileSync(baseFile, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim() !== '');
    
    console.log('📊 Validando integridade dos dados...');
    console.log(`📄 Total de linhas: ${lines.length}`);
    
    // Validações obrigatórias
    const expectedLines = 134; // 133 processos + 1 header
    const expectedProcesses = 133;
    
    if (lines.length !== expectedLines) {
      console.error(`🚨 VIOLAÇÃO: Esperado ${expectedLines} linhas, encontrado ${lines.length}`);
      return false;
    }
    
    // Verificar header
    const header = lines[0];
    if (!header.includes('ITEM') || !header.includes('PROCESSO')) {
      console.error('🚨 VIOLAÇÃO: Header do arquivo corrompido!');
      return false;
    }
    
    // Verificar último processo
    const lastLine = lines[lines.length - 1];
    if (!lastLine.startsWith('"133"')) {
      console.error('🚨 VIOLAÇÃO: Último processo não é ITEM 133!');
      console.error('Última linha:', lastLine.substring(0, 50) + '...');
      return false;
    }
    
    // Verificar se não há linhas vazias
    const emptyLines = lines.filter(line => {
      const fields = line.split(',');
      return fields.length > 5 && fields.slice(1).every(field => 
        field.trim() === '' || field.trim() === '""' || field.trim() === '-'
      );
    });
    
    if (emptyLines.length > 0) {
      console.error(`🚨 VIOLAÇÃO: Encontradas ${emptyLines.length} linhas vazias!`);
      return false;
    }
    
    // Verificar numeração sequencial
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      const expectedItem = i.toString();
      if (!line.startsWith(`"${expectedItem}"`)) {
        console.error(`🚨 VIOLAÇÃO: ITEM ${expectedItem} não encontrado na linha ${i + 1}`);
        return false;
      }
    }
    
    console.log('✅ VALIDAÇÃO APROVADA!');
    console.log('📊 Dados íntegros:');
    console.log(`   - ${expectedProcesses} processos validados`);
    console.log(`   - Numeração sequencial 1-${expectedProcesses}`);
    console.log(`   - Data base: 05/06/2025`);
    console.log(`   - Sem linhas vazias ou duplicados`);
    
    return true;
    
  } catch (error) {
    console.error('🚨 ERRO na validação:', error.message);
    return false;
  }
}

// Função para verificar se sistema está usando dados corretos
function validateCurrentSystem() {
  console.log('\n🔍 VERIFICANDO SISTEMA ATUAL...');
  
  // Esta função pode ser expandida para verificar se a API está usando o arquivo correto
  console.log('✅ Sistema deve usar: data/BCO-DADOS-05-06-25.csv');
  console.log('✅ Total esperado: 133 processos');
  console.log('✅ Data base: 05/06/2025');
}

// Executar validação
if (require.main === module) {
  console.log('🔒 INICIANDO VALIDAÇÃO DA TRAVA DO BANCO DE DADOS');
  console.log('📅 Data: ' + new Date().toLocaleDateString('pt-BR'));
  console.log('=' .repeat(60));
  
  const isValid = validateDatabaseLock();
  
  if (isValid) {
    console.log('\n🎉 TRAVA VALIDADA COM SUCESSO!');
    console.log('🔒 Banco de dados protegido e íntegro');
    validateCurrentSystem();
  } else {
    console.log('\n🚨 FALHA NA VALIDAÇÃO DA TRAVA!');
    console.log('⚠️  Sistema pode estar comprometido');
    process.exit(1);
  }
}

module.exports = { validateDatabaseLock, validateCurrentSystem };
