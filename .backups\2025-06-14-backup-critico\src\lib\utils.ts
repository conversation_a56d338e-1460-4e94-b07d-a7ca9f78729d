import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: string | number): string {
  if (!value || value === '-' || value === '') return 'Não informado';

  // Se já é string, tenta converter
  const numValue = typeof value === 'string' ?
    parseFloat(value.replace(/[^\d,.-]/g, '').replace(',', '.')) :
    value;

  if (isNaN(numValue)) return value.toString();

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(numValue);
}

export function formatDate(dateString: string): string {
  if (!dateString || dateString === '-') return 'Não informado';

  // Se já está no formato brasileiro, retorna como está
  if (dateString.includes('/')) {
    return dateString;
  }

  // Tenta converter outros formatos
  try {
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString('pt-BR');
    }
  } catch {
    // Se falhar, retorna o valor original
  }

  return dateString;
}

export function getStatusColor(status: string): string {
  const statusLower = status.toLowerCase();

  if (statusLower.includes('concluído') || statusLower.includes('finalizado')) {
    return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800';
  }
  if (statusLower.includes('andamento') || statusLower.includes('aberta')) {
    return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800';
  }
  if (statusLower.includes('cancelado') || statusLower.includes('suspenso')) {
    return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800';
  }
  if (statusLower.includes('aguardando') || statusLower.includes('análise')) {
    return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800';
  }

  return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700';
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

