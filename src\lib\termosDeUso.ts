import { db } from './firebase';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';

export interface TermosDeUso {
  id: string;
  versao: string;
  dataCriacao: Date;
  dataAtualizacao: Date;
  ativo: boolean;
  titulo: string;
  conteudo: string;
  aceitacoes: AceitacaoTermos[];
}

export interface AceitacaoTermos {
  userId: string;
  userEmail: string;
  dataAceitacao: Date;
  versao: string;
  ip?: string;
  userAgent?: string;
}

// Termos de uso padrão conforme LGPD
const TERMOS_PADRAO = {
  titulo: "Termos de Uso e Política de Privacidade - InovaProcess",
  conteudo: `
Termos de Uso do Sistema InovaProcess

1. Aceitação dos Termos
Ao utilizar o sistema InovaProcess, você concorda em cumprir estes termos de uso e todas as políticas aplicáveis.

2. Uso Autorizado
O sistema destina-se ao gerenciamento de processos administrativos. O uso deve ser profissional, ético e conforme a legislação vigente.

3. Responsabilidades do Usuário
- Manter a confidencialidade de suas credenciais
- Ser responsável por todas as atividades realizadas em sua conta
- Não compartilhar credenciais com terceiros
- Reportar uso não autorizado imediatamente

4. Proteção de Dados
Todos os dados inseridos no sistema são tratados com confidencialidade e segurança, conforme a LGPD.

Política de Privacidade

Coleta de Dados
Coletamos apenas informações necessárias para o funcionamento do sistema:
- Dados de autenticação (email, nome)
- Informações de processos administrativos
- Logs de acesso para segurança

Uso dos Dados
Os dados são utilizados exclusivamente para:
- Gerenciamento de processos administrativos
- Controle de acesso e segurança
- Melhoria do sistema

Compartilhamento
Dados não são compartilhados com terceiros, exceto quando exigido por lei.

Segurança
Implementamos medidas técnicas e organizacionais para proteger seus dados contra acesso não autorizado.

Conformidade Legal

LGPD
Este sistema está em conformidade com a Lei Geral de Proteção de Dados (Lei nº 13.709/2018).

Direitos do Titular
Você tem direito ao:
- Acesso aos seus dados pessoais
- Correção de dados incorretos
- Exclusão de dados desnecessários
- Portabilidade dos dados
`.trim()
};

// Função para obter termos de uso ativos
export const getTermosDeUsoAtivos = async (): Promise<TermosDeUso | null> => {
  try {
    const termosRef = doc(db, 'configuracoes', 'termos_de_uso');
    const termosSnap = await getDoc(termosRef);

    if (termosSnap.exists()) {
      const data = termosSnap.data();
      return {
        id: termosSnap.id,
        versao: data.versao || '1.0',
        dataCriacao: data.dataCriacao?.toDate() || new Date(),
        dataAtualizacao: data.dataAtualizacao?.toDate() || new Date(),
        ativo: data.ativo !== false,
        titulo: data.titulo || TERMOS_PADRAO.titulo,
        conteudo: data.conteudo || TERMOS_PADRAO.conteudo,
        aceitacoes: data.aceitacoes || []
      };
    }

    // Se não existir, criar termos padrão
    return await criarTermosPadrao();
  } catch (error) {
    console.error('❌ Erro ao obter termos de uso:', error);
    // Retornar termos padrão em caso de erro
    return {
      id: 'padrao',
      versao: '1.0',
      dataCriacao: new Date(),
      dataAtualizacao: new Date(),
      ativo: true,
      titulo: TERMOS_PADRAO.titulo,
      conteudo: TERMOS_PADRAO.conteudo,
      aceitacoes: []
    };
  }
};

// Função para criar termos padrão
export const criarTermosPadrao = async (): Promise<TermosDeUso> => {
  try {
    const termosRef = doc(db, 'configuracoes', 'termos_de_uso');
    const termosData = {
      versao: '1.0',
      dataCriacao: serverTimestamp(),
      dataAtualizacao: serverTimestamp(),
      ativo: true,
      titulo: TERMOS_PADRAO.titulo,
      conteudo: TERMOS_PADRAO.conteudo,
      aceitacoes: []
    };

    await setDoc(termosRef, termosData);
    console.log('✅ Termos de uso padrão criados');

    return {
      id: 'termos_de_uso',
      versao: '1.0',
      dataCriacao: new Date(),
      dataAtualizacao: new Date(),
      ativo: true,
      titulo: TERMOS_PADRAO.titulo,
      conteudo: TERMOS_PADRAO.conteudo,
      aceitacoes: []
    };
  } catch (error) {
    console.error('❌ Erro ao criar termos padrão:', error);
    throw new Error('Falha ao criar termos de uso');
  }
};

// Função para registrar aceitação dos termos
export const registrarAceitacaoTermos = async (
  userId: string,
  userEmail: string,
  versao: string,
  ip?: string,
  userAgent?: string
): Promise<void> => {
  try {
    const termosRef = doc(db, 'configuracoes', 'termos_de_uso');
    const termosAtuais = await getTermosDeUsoAtivos();
    
    if (!termosAtuais) {
      throw new Error('Termos de uso não encontrados');
    }

    const aceitacao: AceitacaoTermos = {
      userId,
      userEmail,
      dataAceitacao: new Date(),
      versao,
      ...(ip && { ip }), // Só incluir se não for undefined
      ...(userAgent && { userAgent }) // Só incluir se não for undefined
    };

    const aceitacoesArray = Array.isArray(termosAtuais.aceitacoes) ? termosAtuais.aceitacoes : [];
    console.log('DEBUG aceitação:', JSON.stringify(aceitacao, null, 2));
    console.log('DEBUG aceitacoesArray:', JSON.stringify(aceitacoesArray, null, 2));
    console.log('DEBUG updateDoc payload:', JSON.stringify({
      aceitacoes: [...aceitacoesArray, aceitacao],
      dataAtualizacao: 'serverTimestamp()'
    }, null, 2));
    await updateDoc(termosRef, {
      aceitacoes: [...aceitacoesArray, aceitacao],
      dataAtualizacao: serverTimestamp()
    });

    console.log('✅ Aceitação dos termos registrada para:', userEmail);
  } catch (error) {
    console.error('❌ Erro ao registrar aceitação:', error);
    throw new Error('Falha ao registrar aceitação dos termos');
  }
};

// Função para verificar se usuário aceitou os termos
export const verificarAceitacaoTermos = async (
  userId: string,
  versao: string
): Promise<boolean> => {
  try {
    const termos = await getTermosDeUsoAtivos();
    if (!termos) {
      return false;
    }
    return termos.aceitacoes.some(
      aceitacao => aceitacao.userId === userId && aceitacao.versao === versao
    );
  } catch (error) {
    console.error('❌ Erro ao verificar aceitação:', error);
    return false;
  }
};

// Função para atualizar termos de uso (apenas admin)
export const atualizarTermosDeUso = async (
  titulo: string,
  conteudo: string,
  novaVersao: string,
  adminEmail: string
): Promise<void> => {
  try {
    const termosRef = doc(db, 'configuracoes', 'termos_de_uso');
    const termosAtuais = await getTermosDeUsoAtivos();
    
    if (!termosAtuais) {
      throw new Error('Termos de uso não encontrados');
    }

    await updateDoc(termosRef, {
      titulo,
      conteudo,
      versao: novaVersao,
      dataAtualizacao: serverTimestamp(),
      ativo: true,
      atualizadoPor: adminEmail,
      historico: [...(termosAtuais.aceitacoes || []), {
        versao: novaVersao,
        dataAtualizacao: new Date(),
        atualizadoPor: adminEmail
      }]
    });

    console.log('✅ Termos de uso atualizados para versão:', novaVersao);
  } catch (error) {
    console.error('❌ Erro ao atualizar termos:', error);
    throw new Error('Falha ao atualizar termos de uso');
  }
}; 