# 📋 CONFIGURAÇÕES COMPLETAS DO SISTEMA - INOVAPROCESS

## 🔥 TABELA MASTER DE CONFIGURAÇÕES

| **CATEGORIA** | **VARIÁVEL/CONFIG** | **VALOR ATUAL** | **ONDE ESTÁ** | **STATUS** |
|---------------|---------------------|-----------------|---------------|------------|
| **FIREBASE** | API_KEY | AIzaSyDoYZw5Her1f1q22jtTyWyv4ytgwyYXLpA | .env.local | ✅ |
| **FIREBASE** | AUTH_DOMAIN | inovaprocess.app | .env.local | ✅ |
| **FIREBASE** | AUTH_DOMAIN_REAL | inovaprocess-novo.firebaseapp.com | Firebase Console | ⚠️ |
| **FIREBASE** | PROJECT_ID | inovaprocess-novo | .env.local | ✅ |
| **FIREBASE** | PROJECT_NUMBER | ************ | Firebase Console | ✅ |
| **FIREBASE** | MESSAGING_SENDER_ID | ************ | .env.local | ✅ |
| **FIREBASE** | STORAGE_BUCKET | inovaprocess-novo.firebasestorage.app | .env.local | ✅ |
| **FIREBASE** | APP_ID | 1:************:web:33e5d45d9ea9dbb9bd66cd | .env.local | ✅ |
| **GOOGLE OAUTH** | CLIENT_ID (.env.local) | ************-2c9v4556lr9eepti8hom4cq09g9h3ngq.apps.googleusercontent.com | .env.local | ❌ |
| **GOOGLE OAUTH** | CLIENT_ID (Google Cloud) | ************-e7rs... | Google Console | ❌ |
| **GOOGLE OAUTH** | Nome do Cliente | Web client (auto created by Google Service) | Google Console | ✅ |
| **GOOGLE OAUTH** | Authorized Origins ATUAL | http://localhost, http://localhost:5000, https://inovaprocess-novo.firebaseapp.com | Google Console | ❌ |
| **GOOGLE OAUTH** | Authorized Redirects ATUAL | https://inovaprocess-novo.firebaseapp.com/__/auth/handler | Google Console | ❌ |
| **GOOGLE OAUTH** | FALTA Origins | http://localhost:3000, https://inovaprocess.app | Google Console | ❌ |
| **GOOGLE OAUTH** | FALTA Redirects | https://inovaprocess.app/__/auth/handler | Google Console | ❌ |
| **FIREBASE AUTH** | Authorized Domains | localhost, inovaprocess.app | Firebase Console | ❌ |
| **FIREBASE AUTH** | Google Provider | Ativado | Firebase Console | ✅ |
| **FIREBASE AUTH** | SMS MFA | Disponível | Firebase Console | ✅ |
| **FIREBASE AUTH** | Usuario Ativo | <EMAIL> | Firebase Console | ✅ |
| **FIREBASE AUTH** | Usuario UID | oJV8mPjHmESwMYEl9yp8xYppP743 | Firebase Console | ✅ |
| **FIREBASE AUTH** | Ultimo Login | 29 de jun. de 2025 | Firebase Console | ✅ |
| **FIREBASE AUTH** | Dominios Autorizados | localhost, inovaprocess-novo.firebaseapp.com, inovaprocess-novo.web.app, inovaprocess.app, inovaprocess.com | Firebase Console | ✅ |
| **GOOGLE CLOUD** | Projeto Atual | InovaProcess (************) | Google Cloud Console | ✅ |
| **GOOGLE CLOUD** | Projeto ID | inovaprocess-novo | Google Cloud Console | ✅ |
| **GOOGLE CLOUD** | APIs Ativadas | Identity Toolkit API, Cloud Firestore API, Gemini API, Token Service API, Analytics Hub API | Google Cloud Console | ✅ |
| **GOOGLE CLOUD** | Browser Key | Auto created by Firebase (28 jun 2025) | Google Cloud Console | ✅ |
| **GOOGLE CLOUD** | OAuth Client | Web client (auto created by Google Service) | Google Cloud Console | ✅ |
| **GOOGLE CLOUD** | OAuth Client ID | ************-e7rs... | Google Cloud Console | ❌ |
| **GOOGLE CLOUD** | Service Account | <EMAIL> | Google Cloud Console | ✅ |
| **FIREBASE** | APP_NAME | inovaprocess-app | Firebase Console | ✅ |
| **FIREBASE** | APP_TYPE | App da Web | Firebase Console | ✅ |
| **FIREBASE** | APP_ID_REAL | 1:************:web:33e5d45d9ea9dbb9bd66cd | Firebase Console | ✅ |
| **SISTEMA** | NODE_ENV | production | .env.local | ✅ |
| **SISTEMA** | NEXT_PUBLIC_ENV | production | .env.local | ✅ |
| **SISTEMA** | DEV_MODE | false | .env.local | ✅ |
| **SISTEMA** | MOCK_DATA | false | .env.local | ✅ |
| **DOMÍNIO** | DOMAIN | inovaprocess.app | .env.local | ✅ |
| **DOMÍNIO** | APP_URL | https://inovaprocess.app | .env.local | ✅ |
| **SERVIDOR** | PORT | 3000 (forçado) | package.json | ✅ |

## 🚨 CONFIGURAÇÕES QUE PRECISAM SER VERIFICADAS

### 1. **GOOGLE OAUTH CONSOLE** ❌ PROBLEMA IDENTIFICADO!
- URL: https://console.developers.google.com/apis/credentials/oauthclient/************-2c9v4556lr9eepti8hom4cq09g9h3ngq.apps.googleusercontent.com
- **Authorized JavaScript origins** ATUAL:
  - ✅ `https://inovaprocess.app`
  - ✅ `https://inovaprocess-auyypqau3-mrisystems-projects.vercel.app`
  - ❌ **FALTA:** `http://localhost:3000`
- **Authorized redirect URIs** ATUAL:
  - ✅ `https://inovaprocess.app/__/auth/handler`
  - ✅ `https://inovaprocess-auyypqau3-mrisystems-projects.vercel.app/__/auth/handler`
  - ❌ **FALTA:** `http://localhost:3000`

### 2. **FIREBASE CONSOLE** (PROJETO INOVAPROCESS-NOVO)
- URL: https://console.firebase.google.com/project/inovaprocess-novo/authentication/settings
- **Authorized domains** DEVE TER:
  - `localhost`
  - `inovaprocess.app`

### 3. **FIRESTORE RULES**
- Arquivo: `firestore.rules`
- Email autorizado: `<EMAIL>`

## 📁 ARQUIVOS DE CONFIGURAÇÃO

### **.env.local** (ATUALIZADO - PROJETO INOVAPROCESS-NOVO)
```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDoYZw5Her1f1q22jtTyWyv4ytgwyYXLpA
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=inovaprocess.app
NEXT_PUBLIC_FIREBASE_PROJECT_ID=inovaprocess-novo
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=inovaprocess-novo.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:33e5d45d9ea9dbb9bd66cd
NEXT_PUBLIC_GOOGLE_CLIENT_ID=************-2c9v4556lr9eepti8hom4cq09g9h3ngq.apps.googleusercontent.com
NODE_ENV=production
NEXT_PUBLIC_ENV=production
NEXT_PUBLIC_DOMAIN=inovaprocess.app
NEXT_PUBLIC_APP_URL=https://inovaprocess.app
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_MOCK_DATA=false
```

### **package.json** (SCRIPT DEV)
```json
"dev": "next dev -p 3000"
```

## 🚨 PROBLEMA IDENTIFICADO!

### **ERRO ATUAL: localhost:3000 NÃO AUTORIZADO**
- **CAUSA**: Google OAuth não tem `http://localhost:3000` configurado
- **SINTOMA**: Login falha em desenvolvimento local
- **SOLUÇÃO URGENTE**: Adicionar `http://localhost:3000` no Google OAuth

### **CONFIGURAÇÃO INCORRETA DETECTADA:**
- ❌ Google OAuth usa redirects `/__/auth/handler` (Firebase)
- ❌ Mas nosso código usa `signInWithPopup` (direto)
- ❌ Falta localhost:3000 nas origens autorizadas

## 🔍 DIAGNÓSTICO DE PROBLEMAS

### **ERRO COMUM: auth/unauthorized-domain**
- **CAUSA**: Domínio não autorizado no Firebase ou Google OAuth
- **SOLUÇÃO**: Verificar configurações nos consoles

### **ERRO COMUM: 400 Bad Request**
- **CAUSA**: Client ID inválido ou mal configurado
- **SOLUÇÃO**: Verificar Google OAuth Console

### **ERRO COMUM: CORS**
- **CAUSA**: Domínios não autorizados
- **SOLUÇÃO**: Adicionar domínios corretos

## ⚠️ REGRAS IMPORTANTES

1. **NUNCA** alterar configurações sem consultar esta tabela
2. **SEMPRE** verificar os 3 consoles antes de fazer mudanças
3. **JAMAIS** misturar configurações de projetos diferentes
4. **OBRIGATÓRIO** testar após qualquer alteração

## 🎯 PRÓXIMOS PASSOS PARA RESOLVER

1. ✅ Configurações locais (.env.local) - CORRETAS
2. ❓ Verificar Google OAuth Console
3. ❓ Verificar Firebase Console
4. ❓ Testar login

---
**📅 ÚLTIMA ATUALIZAÇÃO:** 30/06/2025
**👤 RESPONSÁVEL:** Marcos Isidoro
**🔄 VERSÃO:** 1.0
