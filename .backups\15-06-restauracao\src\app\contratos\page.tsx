'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Search, 
  Filter, 
  Download, 
  Calendar,
  Building,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Plus
} from 'lucide-react';

interface Contrato {
  id: string;
  numero: string;
  objeto: string;
  contratada: string;
  valor: number;
  dataInicio: string;
  dataVencimento: string;
  status: 'VIGENTE' | 'VENCIDO' | 'SUSPENSO' | 'ENCERRADO';
  diasRestantes: number;
  secretaria: string;
  modalidade: string;
  processoOrigem: string;
}

export default function ContratosPage() {
  const [contratos, setContratos] = useState<Contrato[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // Mock data - Período: 01/05/25 até 06/06/25
  const mockContratos: Contrato[] = [
    {
      id: '1',
      numero: '013/2025',
      objeto: 'Fornecimento de medicamentos básicos',
      contratada: 'Farmácia Central Ltda',
      valor: 125000.00,
      dataInicio: '15/05/25',
      dataVencimento: '15/11/25',
      status: 'VIGENTE',
      diasRestantes: 163,
      secretaria: 'SESAU',
      modalidade: 'Pregão Eletrônico',
      processoOrigem: '9078/2025'
    },
    {
      id: '2',
      numero: '014/2025',
      objeto: 'Serviços de limpeza urbana',
      contratada: 'Limpa Cidade S.A.',
      valor: 850000.00,
      dataInicio: '01/05/25',
      dataVencimento: '30/04/26',
      status: 'VIGENTE',
      diasRestantes: 328,
      secretaria: 'SEMAM',
      modalidade: 'Concorrência',
      processoOrigem: '8956/2025'
    },
    {
      id: '3',
      numero: '012/2025',
      objeto: 'Material de escritório',
      contratada: 'Papelaria Moderna',
      valor: 45000.00,
      dataInicio: '10/04/25',
      dataVencimento: '09/05/25',
      status: 'VENCIDO',
      diasRestantes: -28,
      secretaria: 'SSDAN',
      modalidade: 'Pregão Presencial',
      processoOrigem: '8745/2025'
    }
  ];

  useEffect(() => {
    // Simular carregamento
    setTimeout(() => {
      setContratos(mockContratos);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status: string, diasRestantes: number) => {
    switch (status) {
      case 'VIGENTE':
        if (diasRestantes <= 30) {
          return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Vence em {diasRestantes} dias
          </Badge>;
        }
        return <Badge className="bg-green-100 text-green-800 border-green-300">
          <CheckCircle className="w-3 h-3 mr-1" />
          Vigente
        </Badge>;
      case 'VENCIDO':
        return <Badge className="bg-red-100 text-red-800 border-red-300">
          <XCircle className="w-3 h-3 mr-1" />
          Vencido há {Math.abs(diasRestantes)} dias
        </Badge>;
      case 'SUSPENSO':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-300">
          <Clock className="w-3 h-3 mr-1" />
          Suspenso
        </Badge>;
      case 'ENCERRADO':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">
          <CheckCircle className="w-3 h-3 mr-1" />
          Encerrado
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const filteredContratos = contratos.filter(contrato => {
    const matchesSearch = searchTerm === '' || 
      contrato.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contrato.objeto.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contrato.contratada.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === '' || contrato.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: contratos.length,
    vigentes: contratos.filter(c => c.status === 'VIGENTE').length,
    vencidos: contratos.filter(c => c.status === 'VENCIDO').length,
    vencendoEm30Dias: contratos.filter(c => c.status === 'VIGENTE' && c.diasRestantes <= 30).length,
    valorTotal: contratos.reduce((sum, c) => sum + c.valor, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestão de Contratos</h1>
          <p className="text-muted-foreground mt-2">
            Controle e monitoramento de contratos vigentes
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar Relatório
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Novo Contrato
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <FileText className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Vigentes</p>
                <p className="text-2xl font-bold text-green-600">{stats.vigentes}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Vencidos</p>
                <p className="text-2xl font-bold text-red-600">{stats.vencidos}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Vencem em 30 dias</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.vencendoEm30Dias}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="text-lg font-bold text-blue-600">{formatCurrency(stats.valorTotal)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Input
                placeholder="Buscar por número, objeto ou contratada..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="">Todos os status</option>
              <option value="VIGENTE">Vigentes</option>
              <option value="VENCIDO">Vencidos</option>
              <option value="SUSPENSO">Suspensos</option>
              <option value="ENCERRADO">Encerrados</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Contratos */}
      <Card>
        <CardHeader>
          <CardTitle>Contratos ({filteredContratos.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <Clock className="mx-auto h-12 w-12 text-muted-foreground mb-4 animate-spin" />
              <p className="text-muted-foreground">Carregando contratos...</p>
            </div>
          ) : filteredContratos.length > 0 ? (
            <div className="space-y-4">
              {filteredContratos.map((contrato) => (
                <div key={contrato.id} className="border border-border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-semibold text-foreground">Contrato {contrato.numero}</h3>
                        {getStatusBadge(contrato.status, contrato.diasRestantes)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{contrato.objeto}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <Building className="w-3 h-3 mr-1" />
                          {contrato.contratada}
                        </div>
                        <div className="flex items-center">
                          <DollarSign className="w-3 h-3 mr-1" />
                          {formatCurrency(contrato.valor)}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          {contrato.dataVencimento}
                        </div>
                        <div className="flex items-center">
                          <FileText className="w-3 h-3 mr-1" />
                          {contrato.secretaria}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-3 h-3 mr-1" />
                        Ver
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-3 h-3 mr-1" />
                        Editar
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                Nenhum contrato encontrado
              </h3>
              <p className="text-muted-foreground">
                Ajuste os filtros ou cadastre um novo contrato
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
