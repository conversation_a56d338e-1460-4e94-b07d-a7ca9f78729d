# ========================================
# VARIÁVEIS DE AMBIENTE PARA O VERCEL
# ========================================
# COPIE E COLE ESTAS CONFIGURAÇÕES NO VERCEL

# Firebase Configuration - PRODUÇÃO
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyC6A5OAOPoadEFloAohOlFr19Y7mddNDOw
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=inovaprocess.app
NEXT_PUBLIC_FIREBASE_PROJECT_ID=inovaprocess-prod
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=inovaprocess-prod.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=680146328118
NEXT_PUBLIC_FIREBASE_APP_ID=1:680146328118:web:6844f90d16e14051291697

# Google OAuth
NEXT_PUBLIC_GOOGLE_CLIENT_ID=541085692326-2c9v4556lr9eepti8hom4cq09g9h3ngq.apps.googleusercontent.com

# Sistema
NODE_ENV=production
NEXT_PUBLIC_ENV=production
NEXT_PUBLIC_DOMAIN=inovaprocess.app
NEXT_PUBLIC_APP_URL=https://inovaprocess.app
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_MOCK_DATA=false

# ========================================
# INSTRUÇÕES:
# ========================================
# 1. Acesse: https://vercel.com/dashboard
# 2. Selecione seu projeto
# 3. Vá em Settings > Environment Variables
# 4. Atualize TODAS as variáveis acima
# 5. Faça um novo deploy
# ========================================
