'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { registrarUsuario } from '@/lib/auth';
import { PerfilUsuario } from '@/types/usuario';
import {
  UserPlus,
  Mail,
  User,
  Phone,
  Building,
  Shield,
  AlertCircle,
  CheckCircle,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

export default function RegistroPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefone: '',
    secretaria: '',
    cargo: '',
    perfil: 'operador' as PerfilUsuario
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleRegistro = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const novoUsuario = await registrarUsuario({
        ...formData,
        status: 'ativo'
      });
      
      setSuccess(`Usuário ${novoUsuario.nome} registrado com sucesso!`);
      
      // Limpar formulário após sucesso
      setTimeout(() => {
        setFormData({
          nome: '',
          email: '',
          telefone: '',
          secretaria: '',
          cargo: '',
          perfil: 'operador'
        });
        setSuccess('');
      }, 3000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao registrar usuário');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = formData.nome && formData.email && formData.secretaria && formData.cargo;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/usuarios">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-foreground">Registrar Usuário</h1>
              <p className="text-muted-foreground">
                Cadastre um novo usuário no sistema
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs">
            <UserPlus className="mr-1 h-3 w-3" />
            Novo Cadastro
          </Badge>
        </div>

        {/* Formulário de Registro */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserPlus className="mr-2 h-5 w-5" />
              Informações do Usuário
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleRegistro} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800 dark:text-green-200">
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              {/* Informações Pessoais */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Nome Completo *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Nome completo do usuário"
                      value={formData.nome}
                      onChange={(e) => handleInputChange('nome', e.target.value)}
                      className="pl-10"
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Gmail *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="pl-10"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Telefone
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="(11) 99999-9999"
                      value={formData.telefone}
                      onChange={(e) => handleInputChange('telefone', e.target.value)}
                      className="pl-10"
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Cargo *
                  </label>
                  <Input
                    placeholder="Ex: Analista, Coordenador, Secretário"
                    value={formData.cargo}
                    onChange={(e) => handleInputChange('cargo', e.target.value)}
                    disabled={loading}
                  />
                </div>
              </div>

              {/* Informações Organizacionais */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Secretaria *
                  </label>
                  <div className="relative">
                    <Building className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <select
                      className="w-full pl-10 pr-3 py-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                      value={formData.secretaria}
                      onChange={(e) => handleInputChange('secretaria', e.target.value)}
                      disabled={loading}
                    >
                      <option value="" className="dark:bg-gray-800 dark:text-white">Selecione...</option>
                      <option value="CLMP" className="dark:bg-gray-800 dark:text-white">CLMP - Licitações, Materiais e Patrimônio</option>
                      <option value="SE" className="dark:bg-gray-800 dark:text-white">SE - Secretaria de Educação</option>
                      <option value="SMS" className="dark:bg-gray-800 dark:text-white">SMS - Secretaria Municipal de Saúde</option>
                      <option value="SMMA" className="dark:bg-gray-800 dark:text-white">SMMA - Secretaria de Meio Ambiente</option>
                      <option value="SMDU" className="dark:bg-gray-800 dark:text-white">SMDU - Secretaria de Desenvolvimento Urbano</option>
                      <option value="SGM" className="dark:bg-gray-800 dark:text-white">SGM - Secretaria de Governo Municipal</option>
                      <option value="SF" className="dark:bg-gray-800 dark:text-white">SF - Secretaria de Finanças</option>
                      <option value="SAJ" className="dark:bg-gray-800 dark:text-white">SAJ - Secretaria de Assuntos Jurídicos</option>
                      <option value="SMCAS" className="dark:bg-gray-800 dark:text-white">SMCAS - Secretaria de Cultura, Esporte e Lazer</option>
                      <option value="SMTRANS" className="dark:bg-gray-800 dark:text-white">SMTRANS - Secretaria de Transportes</option>
                      <option value="GABINETE" className="dark:bg-gray-800 dark:text-white">Gabinete do Prefeito</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Perfil de Acesso *
                  </label>
                  <div className="relative">
                    <Shield className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <select
                      className="w-full pl-10 pr-3 py-2 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                      value={formData.perfil}
                      onChange={(e) => handleInputChange('perfil', e.target.value as PerfilUsuario)}
                      disabled={loading}
                    >
                      <option value="consulta" className="dark:bg-gray-800 dark:text-white">Consulta - Apenas visualização</option>
                      <option value="operador" className="dark:bg-gray-800 dark:text-white">Operador - Acesso básico</option>
                      <option value="gestor" className="dark:bg-gray-800 dark:text-white">Gestor - Acesso avançado</option>
                      <option value="admin" className="dark:bg-gray-800 dark:text-white">Admin - Acesso total</option>
                      <option value="expediente" className="dark:bg-gray-800 dark:text-white">Expediente - Perfil específico</option>
                    </select>
                  </div>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={!isFormValid || loading}
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                    Registrando...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Registrar Usuário
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Informações sobre Perfis */}
        <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Perfis de Acesso
                </span>
              </div>
              
              <div className="space-y-2 text-xs text-blue-700 dark:text-blue-300">
                <div><strong>Admin:</strong> Acesso total ao sistema, gerenciamento de usuários</div>
                <div><strong>Gestor:</strong> Acesso a sua secretaria + relatórios gerenciais</div>
                <div><strong>Operador:</strong> Acesso básico a processos e contratos</div>
                <div><strong>Consulta:</strong> Apenas visualização de dados</div>
                <div><strong>Expediente:</strong> Perfil específico para cadastro de processos</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
