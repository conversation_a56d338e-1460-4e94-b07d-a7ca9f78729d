/**
 * 🔥 GERENCIADOR DO FIRESTORE
 * Sistema completo para gerenciar dados no Firestore
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  setDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';
import type { Usuario } from '@/types/usuario';
import type { Processo } from '@/types/processo';
import type { Contrato } from '@/types/contrato';

// 👤 GERENCIAMENTO DE USUÁRIOS
export const usuariosFirestore = {
  // Buscar usuário por ID
  async buscarPorId(uid: string): Promise<Usuario | null> {
    if (!db) return null;
    
    try {
      const docRef = doc(db, 'users', uid);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Usuario;
      }
      return null;
    } catch (error) {
      console.error('❌ Erro ao buscar usuário:', error);
      return null;
    }
  },

  // Buscar usuário por email
  async buscarPorEmail(email: string): Promise<Usuario | null> {
    if (!db) return null;
    
    try {
      const q = query(collection(db, 'users'), where('email', '==', email));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        return { id: doc.id, ...doc.data() } as Usuario;
      }
      return null;
    } catch (error) {
      console.error('❌ Erro ao buscar usuário por email:', error);
      return null;
    }
  },

  // Listar todos os usuários
  async listarTodos(): Promise<Usuario[]> {
    if (!db) return [];
    
    try {
      const querySnapshot = await getDocs(collection(db, 'users'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Usuario[];
    } catch (error) {
      console.error('❌ Erro ao listar usuários:', error);
      return [];
    }
  },

  // Criar/atualizar usuário
  async salvar(usuario: Partial<Usuario>): Promise<string> {
    if (!db) throw new Error('Firestore não configurado');
    
    try {
      if (usuario.id) {
        // Atualizar existente
        const docRef = doc(db, 'users', usuario.id);
        await updateDoc(docRef, {
          ...usuario,
          atualizadoEm: serverTimestamp()
        });
        return usuario.id;
      } else {
        // Criar novo
        const docRef = await addDoc(collection(db, 'users'), {
          ...usuario,
          dataCriacao: serverTimestamp(),
          atualizadoEm: serverTimestamp()
        });
        return docRef.id;
      }
    } catch (error) {
      console.error('❌ Erro ao salvar usuário:', error);
      throw error;
    }
  }
};

// 📋 GERENCIAMENTO DE PROCESSOS
export const processosFirestore = {
  // Listar processos
  async listar(filtros?: { status?: string; secretaria?: string }): Promise<Processo[]> {
    if (!db) return [];
    
    try {
      let q = query(collection(db, 'processos'), orderBy('dataInicio', 'desc'));
      
      if (filtros?.status) {
        q = query(q, where('status', '==', filtros.status));
      }
      
      if (filtros?.secretaria) {
        q = query(q, where('secretaria', '==', filtros.secretaria));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Processo[];
    } catch (error) {
      console.error('❌ Erro ao listar processos:', error);
      return [];
    }
  },

  // Buscar processo por ID
  async buscarPorId(id: string): Promise<Processo | null> {
    if (!db) return null;
    
    try {
      const docRef = doc(db, 'processos', id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Processo;
      }
      return null;
    } catch (error) {
      console.error('❌ Erro ao buscar processo:', error);
      return null;
    }
  },

  // Salvar processo
  async salvar(processo: Partial<Processo>): Promise<string> {
    if (!db) throw new Error('Firestore não configurado');
    
    try {
      if (processo.id) {
        // Atualizar existente
        const docRef = doc(db, 'processos', processo.id);
        await updateDoc(docRef, {
          ...processo,
          atualizadoEm: serverTimestamp()
        });
        return processo.id;
      } else {
        // Criar novo
        const docRef = await addDoc(collection(db, 'processos'), {
          ...processo,
          dataCriacao: serverTimestamp(),
          atualizadoEm: serverTimestamp()
        });
        return docRef.id;
      }
    } catch (error) {
      console.error('❌ Erro ao salvar processo:', error);
      throw error;
    }
  }
};

// 📄 GERENCIAMENTO DE CONTRATOS
export const contratosFirestore = {
  // Listar contratos
  async listar(): Promise<Contrato[]> {
    if (!db) return [];
    
    try {
      const querySnapshot = await getDocs(collection(db, 'contratos'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Contrato[];
    } catch (error) {
      console.error('❌ Erro ao listar contratos:', error);
      return [];
    }
  },

  // Salvar contrato
  async salvar(contrato: Partial<Contrato>): Promise<string> {
    if (!db) throw new Error('Firestore não configurado');
    
    try {
      if (contrato.id) {
        // Atualizar existente
        const docRef = doc(db, 'contratos', contrato.id);
        await updateDoc(docRef, {
          ...contrato,
          atualizadoEm: serverTimestamp()
        });
        return contrato.id;
      } else {
        // Criar novo
        const docRef = await addDoc(collection(db, 'contratos'), {
          ...contrato,
          dataCriacao: serverTimestamp(),
          atualizadoEm: serverTimestamp()
        });
        return docRef.id;
      }
    } catch (error) {
      console.error('❌ Erro ao salvar contrato:', error);
      throw error;
    }
  }
};

// ⚙️ CONFIGURAÇÕES DO SISTEMA
export const configuracoesFirestore = {
  // Buscar configurações
  async buscar(): Promise<any> {
    if (!db) return null;
    
    try {
      const docRef = doc(db, 'configuracoes', 'sistema');
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return docSnap.data();
      }
      return null;
    } catch (error) {
      console.error('❌ Erro ao buscar configurações:', error);
      return null;
    }
  },

  // Salvar configurações
  async salvar(config: any): Promise<void> {
    if (!db) throw new Error('Firestore não configurado');
    
    try {
      const docRef = doc(db, 'configuracoes', 'sistema');
      await setDoc(docRef, {
        ...config,
        atualizadoEm: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error('❌ Erro ao salvar configurações:', error);
      throw error;
    }
  }
};

// 🔧 UTILITÁRIOS
export const firestoreUtils = {
  // Converter timestamp do Firestore para string
  timestampToString(timestamp: any): string {
    if (!timestamp) return '';
    
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate().toISOString();
    }
    
    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000).toISOString();
    }
    
    return timestamp.toString();
  },

  // Verificar se Firestore está disponível
  isAvailable(): boolean {
    return !!db;
  }
};
