'use client';

import Link from 'next/link';
import { Processo } from '@/types/processo';
import { Calendar, Building, User, FileText, DollarSign } from 'lucide-react';
import AlertaPrioridade from '@/components/AlertaPrioridade';
import { classificarStatus } from '@/lib/processoUtils';

interface ProcessoCardProps {
  processo: Processo;
}

export default function ProcessoCard({ processo }: ProcessoCardProps) {
  // Usa a classificação estratégica baseada nas regras de negócio da CLMP
  const classificacao = classificarStatus(processo);

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = () => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  // Função para calcular tempo total do processo
  const calcularTempoTotalProcesso = () => {
    if (!processo['DATA DE INÍCIO DO PROCESSO']) return null;

    try {
      const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
      const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      // Se processo finalizado, usar data de finalização
      const isProcessoFinalizado = processo.STATUS?.toLowerCase().includes('finalizado');
      let dataFim = new Date();

      if (isProcessoFinalizado && processo.DATA) {
        const [diaFim, mesFim, anoFim] = processo.DATA.split('/');
        dataFim = new Date(parseInt(anoFim), parseInt(mesFim) - 1, parseInt(diaFim));
      }

      const diffMs = dataFim.getTime() - dataInicio.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
    } catch (error) {
      return null;
    }
  };

  const tempoNoSetor = calcularTempoNoSetor();
  const tempoTotalProcesso = calcularTempoTotalProcesso();

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    
    // Remove caracteres não numéricos exceto vírgula e ponto
    const cleanValue = value.replace(/[^\d,.-]/g, '');
    
    if (!cleanValue) return 'Não informado';
    
    return value; // Retorna o valor original formatado do CSV
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    
    // Se já está no formato brasileiro, retorna como está
    if (dateString.includes('/')) {
      return dateString;
    }
    
    return dateString;
  };

  // Função para extrair sigla da secretaria
  const extrairSiglaSecretaria = (requisitante: string) => {
    if (!requisitante) return 'N/A';

    // Extrair siglas comuns
    const siglas = {
      'SECRETARIA DE SAÚDE': 'SESAU',
      'SECRETARIA DE EDUCAÇÃO': 'SE',
      'SECRETARIA DE MEIO AMBIENTE': 'SMA',
      'SECRETARIA DE ASSISTÊNCIA SOCIAL': 'SSDAN',
      'SECRETARIA DE OBRAS': 'SO',
      'SECRETARIA DE TRANSPORTE': 'ST',
      'SECRETARIA DE CULTURA': 'SC',
      'SECRETARIA DE ESPORTES': 'SESP',
      'GABINETE DO PREFEITO': 'GAB'
    };

    // Buscar por sigla conhecida
    for (const [nome, sigla] of Object.entries(siglas)) {
      if (requisitante.toUpperCase().includes(nome)) {
        return sigla;
      }
    }

    // Se já é uma sigla (2-6 caracteres maiúsculos)
    if (requisitante.length <= 6 && requisitante === requisitante.toUpperCase()) {
      return requisitante;
    }

    // Extrair primeiras letras das palavras principais
    const palavras = requisitante.split(' ').filter(p => p.length > 2);
    if (palavras.length >= 2) {
      return palavras.slice(0, 3).map(p => p[0]).join('').toUpperCase();
    }

    return requisitante.substring(0, 4).toUpperCase();
  };

  // Criar URL segura para o processo
  const processoUrl = `/processos/${encodeURIComponent(processo.PROCESSO || '')}`;

  return (
    <div className="group relative">
      {/* Card Compacto - Linha Grossa - SEM HOVER EXPANSIVO */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-blue-500/20 hover:border-l-blue-500 h-16 flex items-center">
        <div className="p-3 flex-1 flex items-center justify-between">
          <div className="flex-1 min-w-0 flex items-center space-x-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100 truncate">
                  {processo.PROCESSO || 'Sem número'}
                </h3>
                <span className="font-bold text-xs text-blue-600 dark:text-blue-400">
                  {extrairSiglaSecretaria(processo.REQUISITANTE || '')}
                </span>
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                {processo.OBJETO || 'Objeto não informado'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2 ml-2">
            {/* Localização simplificada */}
            <span className="text-xs font-medium px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
              {processo.LOCAL === 'CLMP' ? 'CLMP' : 'EXTERNO'}
            </span>

            {/* Botão Ver Detalhes - POSIÇÃO MELHORADA */}
            <Link
              href={processoUrl}
              className="inline-flex items-center justify-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors duration-200"
            >
              Ver detalhes
            </Link>

            <AlertaPrioridade processo={processo} compact={true} />
          </div>
        </div>
      </div>

      {/* BANNER DETALHADO REMOVIDO - VISUALIZAÇÃO LIMPA */}
    </div>
  );
}
