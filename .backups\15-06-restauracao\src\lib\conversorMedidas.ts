/**
 * 🧠 CONVERSOR DE MEDIDAS INTELIGENTE
 * IA JavaScript pura para detectar e converter unidades automaticamente
 */

export interface UnidadeMedida {
  valor: number;
  unidade: string;
  unidadeOriginal: string;
  tipo: 'volume' | 'peso' | 'papel' | 'comprimento' | 'area' | 'tempo' | 'unidade';
  fatorConversao: number;
}

export interface AlertaUnidade {
  tipo: 'conversao' | 'incompatibilidade' | 'atencao';
  fornecedor: string;
  itemDesejado: string;
  unidadeDesejada: string;
  unidadeFornecedor: string;
  conversaoNecessaria: string;
  fatorMultiplicacao: number;
  observacao: string;
}

/**
 * 🎯 PADRÕES DE DETECÇÃO DE UNIDADES
 */
const PADROES_UNIDADES = {
  // Volume
  volume: {
    'ml|mililitro|mililitros': { fator: 1, unidadePadrao: 'ml' },
    'l|litro|litros': { fator: 1000, unidadePadrao: 'ml' },
    'cl|centilitro|centilitros': { fator: 10, unidadePadrao: 'ml' },
    'dl|decilitro|decilitros': { fator: 100, unidadePadrao: 'ml' },
    'cm³|cm3|centimetro cubico': { fator: 1, unidadePadrao: 'ml' },
    'galao|galões': { fator: 3785, unidadePadrao: 'ml' }
  },
  
  // Peso
  peso: {
    'g|grama|gramas': { fator: 1, unidadePadrao: 'g' },
    'kg|quilo|quilos|quilograma|quilogramas': { fator: 1000, unidadePadrao: 'g' },
    'mg|miligrama|miligramas': { fator: 0.001, unidadePadrao: 'g' },
    'ton|tonelada|toneladas': { fator: 1000000, unidadePadrao: 'g' },
    'lb|libra|libras': { fator: 453.592, unidadePadrao: 'g' },
    'oz|onça|onças': { fator: 28.3495, unidadePadrao: 'g' }
  },
  
  // Papel
  papel: {
    'folha|folhas': { fator: 1, unidadePadrao: 'folha' },
    'resma|resmas': { fator: 500, unidadePadrao: 'folha' },
    'pacote|pacotes': { fator: 100, unidadePadrao: 'folha' },
    'caixa|caixas': { fator: 2500, unidadePadrao: 'folha' }
  },
  
  // Comprimento
  comprimento: {
    'mm|milimetro|milimetros': { fator: 1, unidadePadrao: 'mm' },
    'cm|centimetro|centimetros': { fator: 10, unidadePadrao: 'mm' },
    'm|metro|metros': { fator: 1000, unidadePadrao: 'mm' },
    'km|quilometro|quilometros': { fator: 1000000, unidadePadrao: 'mm' },
    'pol|polegada|polegadas': { fator: 25.4, unidadePadrao: 'mm' },
    'pe|pé|pés': { fator: 304.8, unidadePadrao: 'mm' }
  },
  
  // Área
  area: {
    'mm²|mm2': { fator: 1, unidadePadrao: 'mm²' },
    'cm²|cm2': { fator: 100, unidadePadrao: 'mm²' },
    'm²|m2|metro quadrado': { fator: 1000000, unidadePadrao: 'mm²' },
    'ha|hectare|hectares': { fator: 10000000000, unidadePadrao: 'mm²' }
  },
  
  // Unidade simples
  unidade: {
    'un|unidade|unidades|pç|peça|peças|item|itens': { fator: 1, unidadePadrao: 'un' },
    'par|pares': { fator: 2, unidadePadrao: 'un' },
    'duzia|duzias': { fator: 12, unidadePadrao: 'un' },
    'centena|centenas': { fator: 100, unidadePadrao: 'un' },
    'milheiro|milheiros': { fator: 1000, unidadePadrao: 'un' }
  }
};

/**
 * 🧠 IA PARA DETECTAR UNIDADES NO TEXTO
 */
export class IAConversorMedidas {
  
  /**
   * Detecta unidade de medida em um texto
   */
  static detectarUnidade(texto: string): UnidadeMedida | null {
    const textoLimpo = texto.toLowerCase()
      .replace(/[^\w\s²³]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    // Busca por padrão: número + unidade
    const regexNumero = /(\d+(?:[.,]\d+)?)\s*([a-zA-Z²³]+)/g;
    const matches = Array.from(textoLimpo.matchAll(regexNumero));
    
    for (const match of matches) {
      const valor = parseFloat(match[1].replace(',', '.'));
      const unidadeTexto = match[2];
      
      // Tenta encontrar a unidade nos padrões
      for (const [tipo, padroes] of Object.entries(PADROES_UNIDADES)) {
        for (const [padrao, config] of Object.entries(padroes)) {
          const regex = new RegExp(`\\b(${padrao})\\b`, 'i');
          if (regex.test(unidadeTexto)) {
            return {
              valor: valor * config.fator,
              unidade: config.unidadePadrao,
              unidadeOriginal: unidadeTexto,
              tipo: tipo as any,
              fatorConversao: config.fator
            };
          }
        }
      }
    }
    
    return null;
  }
  
  /**
   * Converte entre unidades do mesmo tipo
   */
  static converter(valorOrigem: number, unidadeOrigem: string, unidadeDestino: string): number | null {
    const origem = this.detectarUnidade(`${valorOrigem} ${unidadeOrigem}`);
    const destino = this.detectarUnidade(`1 ${unidadeDestino}`);
    
    if (!origem || !destino || origem.tipo !== destino.tipo) {
      return null;
    }
    
    // Converte para unidade padrão e depois para destino
    const valorPadrao = origem.valor;
    const valorDestino = valorPadrao / destino.fatorConversao;
    
    return valorDestino;
  }
  
  /**
   * Gera alertas de incompatibilidade de unidades
   */
  static gerarAlertas(itemDesejado: string, fornecedores: Array<{
    nome: string;
    descricao: string;
    preco: number;
  }>): AlertaUnidade[] {
    const alertas: AlertaUnidade[] = [];
    const unidadeDesejada = this.detectarUnidade(itemDesejado);
    
    if (!unidadeDesejada) return alertas;
    
    fornecedores.forEach(fornecedor => {
      const unidadeFornecedor = this.detectarUnidade(fornecedor.descricao);
      
      if (!unidadeFornecedor) return;
      
      // Mesmo tipo mas unidades diferentes
      if (unidadeDesejada.tipo === unidadeFornecedor.tipo && 
          unidadeDesejada.unidade !== unidadeFornecedor.unidade) {
        
        const fatorConversao = unidadeDesejada.valor / unidadeFornecedor.valor;
        
        alertas.push({
          tipo: 'conversao',
          fornecedor: fornecedor.nome,
          itemDesejado,
          unidadeDesejada: unidadeDesejada.unidadeOriginal,
          unidadeFornecedor: unidadeFornecedor.unidadeOriginal,
          conversaoNecessaria: this.gerarTextoConversao(unidadeDesejada, unidadeFornecedor),
          fatorMultiplicacao: fatorConversao,
          observacao: this.gerarObservacao(unidadeDesejada, unidadeFornecedor, fatorConversao)
        });
      }
      
      // Tipos incompatíveis
      else if (unidadeDesejada.tipo !== unidadeFornecedor.tipo) {
        alertas.push({
          tipo: 'incompatibilidade',
          fornecedor: fornecedor.nome,
          itemDesejado,
          unidadeDesejada: unidadeDesejada.unidadeOriginal,
          unidadeFornecedor: unidadeFornecedor.unidadeOriginal,
          conversaoNecessaria: 'Incompatível',
          fatorMultiplicacao: 0,
          observacao: `Unidades incompatíveis: ${unidadeDesejada.tipo} vs ${unidadeFornecedor.tipo}`
        });
      }
    });
    
    return alertas;
  }
  
  /**
   * Gera texto explicativo da conversão
   */
  private static gerarTextoConversao(desejada: UnidadeMedida, fornecedor: UnidadeMedida): string {
    const fator = desejada.valor / fornecedor.valor;
    
    if (fator > 1) {
      return `${fator.toFixed(2)} ${fornecedor.unidadeOriginal} = 1 ${desejada.unidadeOriginal}`;
    } else {
      return `1 ${fornecedor.unidadeOriginal} = ${(1/fator).toFixed(2)} ${desejada.unidadeOriginal}`;
    }
  }
  
  /**
   * Gera observação inteligente
   */
  private static gerarObservacao(desejada: UnidadeMedida, fornecedor: UnidadeMedida, fator: number): string {
    if (fator > 1) {
      return `⚠️ Fornecedor vende em unidade MENOR. Precisa comprar ${fator.toFixed(2)}x mais unidades.`;
    } else if (fator < 1) {
      return `✅ Fornecedor vende em unidade MAIOR. Cada unidade equivale a ${(1/fator).toFixed(2)} do desejado.`;
    } else {
      return `✅ Unidades equivalentes.`;
    }
  }
  
  /**
   * Normaliza descrição para comparação
   */
  static normalizarDescricao(descricao: string): string {
    const unidade = this.detectarUnidade(descricao);
    if (!unidade) return descricao;
    
    // Remove a unidade original e adiciona a normalizada
    const textoSemUnidade = descricao.replace(new RegExp(unidade.unidadeOriginal, 'gi'), '').trim();
    return `${textoSemUnidade} ${unidade.unidade}`;
  }
}
