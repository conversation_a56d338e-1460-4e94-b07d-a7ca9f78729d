'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 🔒 IMPORTAÇÃO DINÂMICA ROBUSTA - PROTEGE CONTRA PROBLEMAS SSR/HIDRATAÇÃO
// Esta abordagem evita os bugs que aconteciam antes com Recharts + Next.js
const ChartComponents = dynamic(
  () => import('recharts').catch(error => {
    console.error('🚨 Erro ao carregar Recharts:', error);
    // Retorna componentes mock em caso de erro
    return {
      ResponsiveContainer: ({ children }: any) => <div className="w-full h-full">{children}</div>,
      BarChart: () => <div className="text-center text-red-500">Erro ao carregar gráfico de barras</div>,
      PieChart: () => <div className="text-center text-red-500">Erro ao carregar gráfico de pizza</div>,
      Bar: () => null,
      Pie: () => null,
      XAxis: () => null,
      YAxis: () => null,
      CartesianGrid: () => null,
      Tooltip: () => null,
      Cell: () => null
    };
  }),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-64 animate-pulse">
        <div className="text-muted-foreground">🔄 Carregando gráfico...</div>
      </div>
    )
  }
);

interface SimpleChartProps {
  data: Array<{ name: string; value: number }>;
  type: 'bar' | 'pie';
  title: string;
  height?: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function SimpleChart({ data, type, title, height = 300 }: SimpleChartProps) {
  const [mounted, setMounted] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [chartsReady, setChartsReady] = useState(false);

  useEffect(() => {
    // 🔒 TRIPLA PROTEÇÃO CONTRA HIDRATAÇÃO
    setIsClient(true);
    setMounted(true);

    // Verificar se os componentes de gráfico estão prontos
    const timer = setTimeout(() => {
      setChartsReady(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  console.log('🎯 SimpleChart renderizando:', {
    mounted,
    isClient,
    chartsReady,
    dataLength: data?.length,
    type,
    title
  });

  // 🔒 PROTEÇÃO 1: Aguardar montagem completa do componente
  if (!mounted || !isClient || !chartsReady) {
    return (
      <div className="flex items-center justify-center animate-pulse" style={{ height }}>
        <div className="text-muted-foreground">🔄 Inicializando gráfico...</div>
      </div>
    );
  }

  // 🔒 PROTEÇÃO 2: Validar dados
  if (!data || data.length === 0) {
    console.log('⚠️ Sem dados para o gráfico:', title);
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <div className="text-muted-foreground">📊 Sem dados para exibir</div>
      </div>
    );
  }

  console.log('📊 Dados do gráfico:', title, data);

  // 🔒 PROTEÇÃO 3: Verificar se ChartComponents está disponível
  if (!ChartComponents || typeof ChartComponents !== 'object') {
    console.log('⚠️ ChartComponents não disponível, usando fallback');
    return renderFallbackChart();
  }

  // 🔒 PROTEÇÃO 4: Renderização segura com try/catch
  try {
    const { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, PieChart, Pie, Cell } = ChartComponents;

    if (!ResponsiveContainer || !BarChart || !PieChart) {
      console.log('⚠️ Componentes Recharts não disponíveis, usando fallback');
      return renderFallbackChart();
    }

    if (type === 'bar') {
      return (
        <ResponsiveContainer width="100%" height={height}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="value" fill="#0088FE" />
          </BarChart>
        </ResponsiveContainer>
      );
    }

    if (type === 'pie') {
      return (
        <ResponsiveContainer width="100%" height={height}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    return renderFallbackChart();
  } catch (error) {
    console.error('🚨 Erro ao renderizar gráfico:', error);
    return renderFallbackChart();
  }

  // 🔒 FUNÇÃO FALLBACK ROBUSTA - NUNCA FALHA - VERSÃO MELHORADA
  function renderFallbackChart() {
    const maxValue = Math.max(...data.map(d => d.value));
    const total = data.reduce((sum, item) => sum + item.value, 0);

    if (type === 'bar') {
      return (
        <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg" style={{ height }}>
          <h4 className="text-sm font-semibold mb-4 text-gray-800 flex items-center">
            📊 {title}
            <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
              {data.length} itens
            </span>
          </h4>
          <div className="space-y-3 overflow-y-auto max-h-64">
            {data.slice(0, 10).map((item, index) => {
              const percentage = (item.value / maxValue) * 100;
              const gradientColors = [
                'from-blue-400 to-blue-600',
                'from-green-400 to-green-600',
                'from-purple-400 to-purple-600',
                'from-orange-400 to-orange-600',
                'from-red-400 to-red-600',
                'from-teal-400 to-teal-600',
                'from-pink-400 to-pink-600',
                'from-indigo-400 to-indigo-600',
                'from-yellow-400 to-yellow-600',
                'from-cyan-400 to-cyan-600'
              ];

              return (
                <div
                  key={index}
                  className="group hover:bg-white/50 p-2 rounded-lg transition-all duration-200 cursor-pointer"
                  title={`${item.name}: ${item.value} processos (${percentage.toFixed(1)}%)`}
                >
                  <div className="flex justify-between text-sm mb-2">
                    <span className="font-medium text-gray-700 truncate max-w-40" title={item.name}>
                      {item.name.length > 25 ? item.name.substring(0, 25) + '...' : item.name}
                    </span>
                    <span className="font-bold text-gray-900 bg-white px-2 py-1 rounded-md shadow-sm">
                      {item.value}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                    <div
                      className={`bg-gradient-to-r ${gradientColors[index % gradientColors.length]} h-3 rounded-full transition-all duration-500 shadow-sm group-hover:shadow-md`}
                      style={{ width: `${Math.max(percentage, 5)}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {percentage.toFixed(1)}%
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }

    if (type === 'pie') {
      return (
        <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg" style={{ height }}>
          <h4 className="text-sm font-semibold mb-4 text-gray-800 flex items-center">
            🥧 {title}
            <span className="ml-2 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
              Total: {total}
            </span>
          </h4>

          {/* Gráfico de Pizza Visual CSS */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative w-32 h-32 mx-auto mb-4">
                {data.slice(0, 6).map((item, index) => {
                  const percentage = (item.value / total) * 100;
                  const cumulativePercentage = data.slice(0, index).reduce((sum, d) => sum + (d.value / total) * 100, 0);
                  const rotation = (cumulativePercentage * 3.6) - 90;
                  const nextRotation = ((cumulativePercentage + percentage) * 3.6) - 90;

                  return (
                    <div
                      key={index}
                      className="absolute inset-0 rounded-full border-8 transition-all duration-300 hover:scale-105"
                      style={{
                        borderColor: COLORS[index % COLORS.length],
                        clipPath: `polygon(50% 50%, 50% 0%, ${50 + 50 * Math.cos((rotation * Math.PI) / 180)}% ${50 + 50 * Math.sin((rotation * Math.PI) / 180)}%, ${50 + 50 * Math.cos((nextRotation * Math.PI) / 180)}% ${50 + 50 * Math.sin((nextRotation * Math.PI) / 180)}%)`,
                        transform: `rotate(${rotation}deg)`
                      }}
                      title={`${item.name}: ${item.value} (${percentage.toFixed(1)}%)`}
                    />
                  );
                })}
              </div>
            </div>

            <div className="flex-1 space-y-2 overflow-y-auto max-h-48">
              {data.slice(0, 8).map((item, index) => {
                const percentage = ((item.value / total) * 100).toFixed(1);
                const color = COLORS[index % COLORS.length];

                return (
                  <div
                    key={index}
                    className="group flex items-center space-x-3 p-2 rounded-lg hover:bg-white/60 transition-all duration-200 cursor-pointer"
                    title={`${item.name}: ${item.value} processos (${percentage}%)`}
                  >
                    <div
                      className="w-5 h-5 rounded-full flex-shrink-0 shadow-md group-hover:scale-110 transition-transform"
                      style={{ backgroundColor: color }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700 truncate" title={item.name}>
                          {item.name.length > 20 ? item.name.substring(0, 20) + '...' : item.name}
                        </span>
                        <div className="text-right ml-2">
                          <div className="text-sm font-bold text-gray-900">{item.value}</div>
                          <div className="text-xs text-gray-500">{percentage}%</div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg" style={{ height }}>
        <div className="text-center">
          <div className="text-4xl mb-2">📊</div>
          <div className="text-gray-600 font-medium">Gráfico em modo compatibilidade</div>
          <div className="text-xs text-gray-500 mt-1">Tipo: {type}</div>
        </div>
      </div>
    );
  }
}
