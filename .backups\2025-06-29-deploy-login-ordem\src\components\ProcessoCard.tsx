'use client';

import Link from 'next/link';
import { Processo } from '@/types/processo';
import { Calendar, Building, User, FileText, DollarSign } from 'lucide-react';
import AlertaPrioridade from '@/components/AlertaPrioridade';
import { classificarStatus } from '@/lib/processoUtils';

interface ProcessoCardProps {
  processo: Processo;
}

export default function ProcessoCard({ processo }: ProcessoCardProps) {
  // Usa a classificação estratégica baseada nas regras de negócio da CLMP
  const classificacao = classificarStatus(processo);

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = () => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  // Função para calcular tempo total do processo
  const calcularTempoTotalProcesso = () => {
    if (!processo['DATA DE INÍCIO DO PROCESSO']) return null;

    try {
      const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
      const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      // Se processo finalizado, usar data de finalização
      const isProcessoFinalizado = processo.STATUS?.toLowerCase().includes('finalizado');
      let dataFim = new Date();

      if (isProcessoFinalizado && processo.DATA) {
        const [diaFim, mesFim, anoFim] = processo.DATA.split('/');
        dataFim = new Date(parseInt(anoFim), parseInt(mesFim) - 1, parseInt(diaFim));
      }

      const diffMs = dataFim.getTime() - dataInicio.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
    } catch (error) {
      return null;
    }
  };

  const tempoNoSetor = calcularTempoNoSetor();
  const tempoTotalProcesso = calcularTempoTotalProcesso();

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    
    // Remove caracteres não numéricos exceto vírgula e ponto
    const cleanValue = value.replace(/[^\d,.-]/g, '');
    
    if (!cleanValue) return 'Não informado';
    
    return value; // Retorna o valor original formatado do CSV
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    
    // Se já está no formato brasileiro, retorna como está
    if (dateString.includes('/')) {
      return dateString;
    }
    
    return dateString;
  };

  // Função para extrair sigla da secretaria - DADOS REAIS DO CSV
  const extrairSiglaSecretaria = (requisitante: string) => {
    if (!requisitante) return 'N/A';

    // Mapeamento das secretarias REAIS do CSV
    const secretarias: { [key: string]: string } = {
      'SE': 'SE',
      'SMA': 'SMA',
      'SS': 'SS',
      'SG': 'SG',
      'SSP': 'SSP',
      'SEL': 'SEL',
      'SAM': 'SAM',
      'SSU': 'SSU',
      'SSAN': 'SSAN',
      'SF': 'SF',
      'SAS': 'SAS',
      'STRE': 'STRE',
      'SPDC': 'SPDC',
      'SMU': 'SMU',
      'SAJ': 'SAJ',
      'SO': 'SO',
      'SPDPD': 'SPDPD'
    };

    // Busca direta pela sigla (dados reais do CSV)
    if (secretarias[requisitante]) {
      return secretarias[requisitante];
    }

    // Se não encontrou, retorna como está
    return requisitante;
  };

  // Verificar se processo está em pesquisa de preços (pesquisa ainda não foi feita)
  const estaEmPesquisaPrecos = () => {
    const status = processo.STATUS || '';
    const statusLower = status.toLowerCase();

    // Casos específicos de pesquisa de preços
    return statusLower.includes('para pesquisa de preços') ||
           statusLower.includes('para pesquisa') ||
           statusLower.includes('pesquisa de preços') ||
           statusLower.includes('em pesquisa') ||
           (statusLower.includes('pesquisa') &&
            !statusLower.includes('ratificação') &&
            !statusLower.includes('ratificar') &&
            !statusLower.includes('após pesquisa'));
  };

  // Verificar se está aguardando ratificação (pesquisa já foi feita, aguardando aprovação)
  const estaAguardandoRatificacao = () => {
    const status = processo.STATUS || '';
    const statusLower = status.toLowerCase();

    return statusLower.includes('ratificação da pesquisa') ||
           statusLower.includes('ratificar') ||
           statusLower.includes('para ratificação') ||
           statusLower.includes('aguardando ratificação') ||
           statusLower.includes('encaminhado para ratificação');
  };

  // Verificar se processo está em instrução
  const estaEmInstrucao = () => {
    const status = processo.STATUS || '';
    const statusLower = status.toLowerCase();

    return statusLower.includes('em instrução') ||
           statusLower.includes('para análise') ||
           statusLower.includes('análise após adequações') ||
           statusLower.includes('para adequações');
  };

  // Determinar valor estimado a ser exibido
  const obterValorEstimado = () => {
    const valorEstimado = processo['VALOR ESTIMADO'];

    // Se tem valor estimado válido (COLUNA I DO CSV), retorna ele
    if (valorEstimado && valorEstimado !== '-' && valorEstimado !== '' && valorEstimado !== 'R$ 0,00') {
      return valorEstimado;
    }

    // Se não tem valor, verificar o status para determinar a situação
    const status = processo.STATUS || '';

    // Se não tem valor e está aguardando ratificação (pesquisa feita, aguardando aprovação)
    if (estaAguardandoRatificacao()) {
      return 'aguardando ratificação';
    }

    // Se não tem valor e está em pesquisa de preços (pesquisa ainda não foi feita)
    if (estaEmPesquisaPrecos()) {
      return 'em pesquisa';
    }

    // Se não tem valor e está em instrução
    if (estaEmInstrucao()) {
      return 'em instrução';
    }

    // Se não tem valor e não está em nenhum dos casos acima, mostrar status atual
    if (status && status !== '-') {
      return 'sem valor estimado';
    }

    // Último caso: realmente não tem informação
    return 'valor não informado';
  };

  // Determinar localização baseada nos dados reais do CSV
  const determinarLocalizacao = () => {
    const local = processo.LOCAL || '';
    const status = processo.STATUS || '';

    // USAR DADOS REAIS DO CSV - Campo LOCAL
    if (local && local !== '-' && local !== '' && local.trim() !== '') {
      return local.trim();
    }

    // Se LOCAL não está preenchido, tentar inferir pelo STATUS
    const statusLower = status.toLowerCase();

    // Verificar se está em secretarias específicas baseado no status
    if (statusLower.includes('encaminhado para saj') || statusLower.includes('parecer jurídico')) {
      return 'SAJ';
    }

    if (statusLower.includes('encaminhado para sf') || statusLower.includes('análise orçamentária')) {
      return 'SF';
    }

    if (statusLower.includes('encaminhado à secretaria') || statusLower.includes('secretaria para gerenciamento')) {
      // Tentar identificar a secretaria pelo requisitante
      const requisitante = processo.REQUISITANTE || '';
      if (requisitante && requisitante !== '-') {
        return requisitante;
      }
    }

    // Se LOCAL não está preenchido e não conseguiu inferir, assumir CLMP como padrão
    return 'CLMP';
  };

  const valorParaExibir = obterValorEstimado();
  const localizacao = determinarLocalizacao();

  // Criar URL segura para o processo
  const processoUrl = `/processos/${encodeURIComponent(processo.PROCESSO || '')}`;

  return (
    <div className="group relative">
      {/* Card Compacto - Linha Grossa - CLICÁVEL EM TODA A ÁREA */}
      <Link href={processoUrl} className="block">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-300 dark:border-gray-600 hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-blue-500/30 hover:border-l-blue-500 h-16 flex items-center">
          <div className="p-3 flex-1 flex items-center justify-between">
            <div className="flex-1 min-w-0 flex items-center space-x-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold text-sm text-gray-800 dark:text-gray-100 truncate">
                    {processo.PROCESSO || 'Sem número'}
                  </h3>
                  <span className="font-semibold text-sm text-gray-800 dark:text-gray-100">
                    {extrairSiglaSecretaria(processo.REQUISITANTE || '')}
                  </span>
                  {/* Valor Estimado - SEMPRE MOSTRAR ALGUMA INFORMAÇÃO */}
                  <span className={`text-xs font-medium px-2 py-1 rounded ${
                    valorParaExibir && valorParaExibir.startsWith('R$')
                      ? 'text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20'
                      : valorParaExibir === 'em pesquisa'
                        ? 'text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20'
                        : valorParaExibir === 'aguardando ratificação'
                          ? 'text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20'
                          : 'text-gray-700 dark:text-gray-200 bg-gray-50 dark:bg-gray-700/50'
                  }`}>
                    {valorParaExibir || 'valor não informado'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-200 truncate">
                  {processo.OBJETO && processo.OBJETO.length > 80
                    ? `${processo.OBJETO.substring(0, 80)}...`
                    : processo.OBJETO || 'Objeto não informado'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2 ml-2">
              {/* Localização baseada nos dados reais do CSV */}
              {localizacao && (
                <span className="text-xs font-medium px-2 py-1 rounded bg-gray-200 dark:bg-gray-700/70 text-gray-800 dark:text-gray-200">
                  {localizacao}
                </span>
              )}

              <AlertaPrioridade processo={processo} compact={true} />
            </div>
          </div>
        </div>
      </Link>

      {/* BANNER DETALHADO REMOVIDO - VISUALIZAÇÃO LIMPA */}
    </div>
  );
}
