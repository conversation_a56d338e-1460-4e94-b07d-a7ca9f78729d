'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  FileText,
  BarChart3,
  Settings,
  Users,
  Building,
  FileSearch,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Search,
  Brain
} from 'lucide-react';
// import { LogoIcon, LogoButton } from '@/components/ui/logo';

const menuItems = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: <LayoutDashboard size={18} />,
  },
  {
    title: 'Processos',
    path: '/processos',
    icon: <FileText size={18} />,
    submenu: [
      { title: 'Todos os Processos', path: '/processos' },
      { title: 'Novo Processo', path: '/processos/novo' },
      { title: 'Em Andamento', path: '/processos/andamento' },
    ],
  },
  {
    title: 'Análise de Editais',
    path: '/analise-editais',
    icon: <Brain size={18} />,
    submenu: [
      { title: '<PERSON> Análise', path: '/analise-editais' },
      { title: 'Treinamento ML', path: '/analise-editais/treinamento' },
      { title: 'Histórico', path: '/analise-editais/historico' },
      { title: 'IA Assistant', path: '/analise-editais/ia' },
    ],
  },
  {
    title: 'Pesquisa de Preços',
    path: '/pesquisa-precos',
    icon: <Search size={18} />,
    submenu: [
      { title: 'Nova Pesquisa', path: '/pesquisa-precos' },
      { title: 'Histórico', path: '/pesquisa-precos/historico' },
    ],
  },
  {
    title: 'Contratos',
    path: '/contratos',
    icon: <FileSearch size={18} />,
    submenu: [
      { title: 'Todos os Contratos', path: '/contratos' },
      { title: 'Vigentes', path: '/contratos/vigentes' },
      { title: 'Vencidos', path: '/contratos/vencidos' },
    ],
  },
  {
    title: 'Relatórios',
    path: '/relatorios',
    icon: <BarChart3 size={18} />,
    submenu: [
      { title: 'Central de Relatórios', path: '/relatorios' },
      { title: 'Desempenho', path: '/relatorios/desempenho' },
      { title: 'Tempo Médio', path: '/relatorios/tempo-medio' },
      { title: 'Por Secretaria', path: '/relatorios/secretarias' },
    ],
  },
  {
    title: 'Secretarias',
    path: '/secretarias',
    icon: <Building size={18} />,
  },
  {
    title: 'Usuários',
    path: '/usuarios',
    icon: <Users size={18} />,
  },
  {
    title: 'Configurações',
    path: '/configuracoes',
    icon: <Settings size={18} />,
  },
];

export default function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      setOpenSubmenu(null);
    }
  };

  const toggleSubmenu = (title: string) => {
    setOpenSubmenu(openSubmenu === title ? null : title);
  };

  return (
    <div
      className={`bg-card border-r border-border transition-all duration-300 ${
        isCollapsed ? 'w-16' : 'w-56'
      } flex flex-col h-full`}
    >
      <div className="flex items-center justify-between px-4 py-5 border-b border-border">
        {!isCollapsed && (
          <Link href="/" className="hover:opacity-80 transition-opacity">
            <h2 className="text-lg font-bold text-foreground">InovaProcess</h2>
          </Link>
        )}
        <button
          onClick={toggleSidebar}
          className="p-1 rounded-md hover:bg-accent text-muted-foreground hover:text-foreground transition-colors"
        >
          {isCollapsed ? <Menu size={18} /> : <X size={18} />}
        </button>
      </div>

      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-3">
          {menuItems.map((item) => (
            <li key={item.title}>
              {item.submenu ? (
                <div>
                  {isCollapsed ? (
                    <button
                      onClick={() => toggleSubmenu(item.title)}
                      title={item.title}
                      className={`flex items-center w-full p-2.5 rounded-lg text-sm font-medium transition-colors ${
                        pathname.startsWith(item.path)
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                      } justify-center`}
                    >
                      <span>{item.icon}</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => toggleSubmenu(item.title)}
                      className={`flex items-center w-full p-2.5 rounded-lg text-sm font-medium transition-colors ${
                        pathname.startsWith(item.path)
                          ? 'bg-primary/10 text-primary'
                          : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                      } justify-between`}
                    >
                      <div className="flex items-center">
                        <span className="mr-3">{item.icon}</span>
                        <span>{item.title}</span>
                      </div>
                      <span>
                        {openSubmenu === item.title ? (
                          <ChevronDown size={14} />
                        ) : (
                          <ChevronRight size={14} />
                        )}
                      </span>
                    </button>
                  )}
                  {!isCollapsed && openSubmenu === item.title && (
                    <ul className="pl-8 mt-1 space-y-1">
                      {item.submenu.map((subItem) => (
                        <li key={subItem.title}>
                          <Link
                            href={subItem.path}
                            className={`block p-2 rounded-md text-sm transition-colors ${
                              pathname === subItem.path
                                ? 'bg-primary/10 text-primary font-medium'
                                : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                            }`}
                          >
                            {subItem.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                isCollapsed ? (
                  <Link
                    href={item.path}
                    title={item.title}
                    className={`flex items-center p-2.5 rounded-lg text-sm font-medium transition-colors ${
                      pathname === item.path
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                    } justify-center`}
                  >
                    <span>{item.icon}</span>
                  </Link>
                ) : (
                  <Link
                    href={item.path}
                    className={`flex items-center p-2.5 rounded-lg text-sm font-medium transition-colors ${
                      pathname === item.path
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                    }`}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.title}</span>
                  </Link>
                )
              )}
            </li>
          ))}
        </ul>
      </nav>

      <div className="p-4 border-t border-border">
        <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>
          <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold text-sm">
            A
          </div>
          {!isCollapsed && (
            <div>
              <p className="text-sm font-medium text-foreground">Admin</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}