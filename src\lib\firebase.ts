import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged, User } from 'firebase/auth';
import { getFirestore, doc, getDoc, setDoc, collection, getDocs, serverTimestamp, enableNetwork, disableNetwork, connectFirestoreEmulator } from 'firebase/firestore';

// Configuração do Firebase - APENAS PRODUÇÃO COM CHAVES REAIS
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Verificar se todas as configurações estão presentes
if (!firebaseConfig.apiKey || !firebaseConfig.authDomain || !firebaseConfig.projectId) {
  console.error('❌ Configurações do Firebase não encontradas. Configure as variáveis de ambiente no .env.local');
  console.error('Configurações necessárias:', {
    apiKey: !!firebaseConfig.apiKey,
    authDomain: !!firebaseConfig.authDomain,
    projectId: !!firebaseConfig.projectId
  });
  throw new Error('Firebase não configurado - configure as variáveis de ambiente');
}

// Inicializar Firebase - SEM MOCKS, APENAS FIREBASE REAL
let app: any = null;
try {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase inicializado com configuração de produção');
  console.log('🔗 AuthDomain:', firebaseConfig.authDomain);
  console.log('🆔 ProjectID:', firebaseConfig.projectId);
} catch (error) {
  console.error('❌ Erro ao inicializar Firebase:', error);
  console.error('🔧 Configuração:', firebaseConfig);
  throw new Error('Falha ao inicializar Firebase');
}

// Inicializar Auth e Firestore
export const auth = getAuth(app);
export const db = getFirestore(app);

// Provider do Google com configurações otimizadas
const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');
googleProvider.setCustomParameters({
  prompt: 'select_account',
  hd: 'gmail.com' // Força apenas contas Gmail
});

// Função auxiliar para processar login do usuário
const processUserLogin = async (user: User) => {
  console.log('✅ Login realizado com sucesso:', user.email);

  // 🚀 Inicializar Firestore automaticamente
  await initializeFirestoreCollections(user);

  return user;
};

// Função para login com Google - VERSÃO POPUP
export const signInWithGoogle = async () => {
  try {
    console.log('🔄 Iniciando login com Google...');

    // Usar popup que está funcionando
    const { signInWithPopup } = await import('firebase/auth');

    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;

    console.log('✅ Login via popup bem-sucedido:', user.email);

    // Verificar se é Gmail
    if (!user.email?.endsWith('@gmail.com')) {
      await signOut(auth);
      throw new Error('Apenas contas Gmail são permitidas');
    }

    return await processUserLogin(user);
  } catch (error: any) {
    console.error('❌ Erro no login:', error);

    // Tratamento específico de erros
    if (error.code === 'auth/popup-closed-by-user') {
      throw new Error('Login cancelado pelo usuário');
    } else if (error.code === 'auth/popup-blocked') {
      throw new Error('Popup bloqueado pelo navegador. Permita popups para este site.');
    } else if (error.code === 'auth/network-request-failed') {
      throw new Error('Erro de conexão. Verifique sua internet.');
    } else if (error.code === 'auth/unauthorized-domain') {
      throw new Error('Domínio não autorizado. Configure o domínio no Firebase Console.');
    } else {
      throw new Error(error.message || 'Erro desconhecido no login');
    }
  }
};

// Função para logout
export const logoutUser = async () => {
  try {
    await signOut(auth);
    console.log('✅ Logout realizado');
  } catch (error) {
    console.error('❌ Erro no logout:', error);
    throw error;
  }
};

// Hook para monitorar estado de autenticação
export const useAuthState = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// Verificar se usuário está autenticado
export const getCurrentUser = () => {
  return auth?.currentUser || null;
};

// Verificar se é Gmail válido
export const isValidGmailUser = (user: User | null): boolean => {
  return user?.email?.endsWith('@gmail.com') || false;
};

// 🚀 INICIALIZAÇÃO OTIMIZADA DO FIRESTORE (UMA VEZ SÓ)
export const initializeFirestoreCollections = async (user: User) => {
  if (!db) {
    console.warn('⚠️ Firestore não configurado');
    return;
  }

  try {
    // 1. VERIFICAR FLAG DE INICIALIZAÇÃO (SUPER RÁPIDO)
    const configRef = doc(db, 'configuracoes', 'sistema');
    const configSnap = await getDoc(configRef);

    const isAlreadyInitialized = configSnap.exists() && configSnap.data()?.firestore_inicializado === true;

    if (isAlreadyInitialized) {
      console.log('⚡ Firestore já inicializado - login instantâneo!');

      // Apenas atualizar último acesso do usuário (operação leve)
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        nome: user.displayName || user.email?.split('@')[0] || 'Usuário',
        ultimoAcesso: serverTimestamp()
      }, { merge: true });

      return;
    }

    // 2. PRIMEIRA INICIALIZAÇÃO COMPLETA (SÓ UMA VEZ)
    console.log('🔄 Primeira inicialização do Firestore - criando estrutura...');

    // Criar coleção de usuários
    const userRef = doc(db, 'users', user.uid);
    await setDoc(userRef, {
      uid: user.uid,
      email: user.email,
      nome: user.displayName || user.email?.split('@')[0] || 'Usuário',
      telefone: '',
      secretaria: 'CLMP',
      cargo: 'Administrador',
      perfil: 'admin',
      status: 'ativo',
      ultimoAcesso: serverTimestamp(),
      dataCriacao: serverTimestamp(),
      permissoes: ['dashboard.view', 'processos.view', 'contratos.view', 'usuarios.manage'],
      configuracoes: {
        tema: 'light',
        notificacoes: true,
        idioma: 'pt-BR'
      }
    });

    // Criar todas as coleções necessárias
    const collections = ['processos', 'contratos', 'pesquisas', 'usuarios'];

    for (const collectionName of collections) {
      const initialDoc = doc(db, collectionName, '_init');
      await setDoc(initialDoc, {
        _created: serverTimestamp(),
        _createdBy: user.email,
        _description: `Coleção ${collectionName} inicializada automaticamente`,
        _version: '2.0.0'
      });
      console.log(`✅ Coleção '${collectionName}' criada`);
    }

    // 3. SALVAR FLAG DE INICIALIZAÇÃO (DEFINITIVO)
    await setDoc(configRef, {
      firestore_inicializado: true,
      versao: '2.0.0',
      dataInicializacao: serverTimestamp(),
      inicializadoPor: user.email,
      ultimaAtualizacao: serverTimestamp(),
      mantenedores: [user.email],
      configuracoes: {
        antiResetProtection: true,
        devMode: false,
        backupAutomatico: true
      }
    });

    console.log('🎉 Firestore inicializado DEFINITIVAMENTE! Próximos logins serão instantâneos!');

  } catch (error) {
    console.error('❌ Erro ao inicializar Firestore:', error);
  }
};

// ⚡ VERIFICAÇÃO RÁPIDA DE STATUS DO FIRESTORE
export const checkFirestoreStatus = async (): Promise<{
  isInitialized: boolean;
  version?: string;
  initializedBy?: string;
  initDate?: string;
}> => {
  if (!db) {
    return { isInitialized: false };
  }

  try {
    const configRef = doc(db, 'configuracoes', 'sistema');
    const configSnap = await getDoc(configRef);

    if (configSnap.exists()) {
      const data = configSnap.data();
      return {
        isInitialized: data.firestore_inicializado === true,
        version: data.versao,
        initializedBy: data.inicializadoPor,
        initDate: data.dataInicializacao?.toDate?.()?.toISOString()
      };
    }

    return { isInitialized: false };
  } catch (error) {
    console.error('❌ Erro ao verificar status do Firestore:', error);
    return { isInitialized: false };
  }
};
