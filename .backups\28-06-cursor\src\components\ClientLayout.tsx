'use client';

import { Suspense, useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';


// Loading components
const SidebarLoading = () => (
  <div className="w-56 bg-card border-r border-border animate-pulse">
    <div className="p-4 border-b border-border">
      <div className="h-6 bg-muted rounded"></div>
    </div>
    <div className="p-4 space-y-3">
      {[...Array(8)].map((_, i) => (
        <div key={i} className="h-8 bg-muted rounded"></div>
      ))}
    </div>
  </div>
);

const HeaderLoading = () => (
  <div className="h-16 bg-card border-b border-border animate-pulse">
    <div className="flex items-center justify-between h-full px-6">
      <div className="h-6 w-32 bg-muted rounded"></div>
      <div className="h-8 w-64 bg-muted rounded"></div>
      <div className="flex space-x-2">
        <div className="h-8 w-8 bg-muted rounded-full"></div>
        <div className="h-8 w-8 bg-muted rounded-full"></div>
      </div>
    </div>
  </div>
);

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Não renderizar nada até estar montado
  if (!mounted) {
    return (
      <div className="flex h-screen bg-background items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Páginas que não precisam de autenticação (home e login)
  const publicRoutes = ['/', '/login'];
  const isPublicRoute = publicRoutes.includes(pathname);

  // Se está em página pública (home ou login), renderizar apenas o children (sem sidebar/header)
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // Layout normal com sidebar e header - protegido por autenticação
  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-background">
        <Suspense fallback={<SidebarLoading />}>
          <Sidebar />
        </Suspense>

        <div className="flex-1 flex flex-col overflow-hidden">
          <Suspense fallback={<HeaderLoading />}>
            <Header />
          </Suspense>

          <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 bg-background">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>

          <footer className="bg-card border-t p-3 text-center text-sm text-muted-foreground">
            InovaProcess © {new Date().getFullYear()}
          </footer>
        </div>


      </div>
    </ProtectedRoute>
  );
}
