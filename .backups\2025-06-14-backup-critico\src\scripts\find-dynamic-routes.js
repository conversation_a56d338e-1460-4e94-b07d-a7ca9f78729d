const fs = require('fs');
const path = require('path');

// Função para verificar se um diretório é uma rota dinâmica
function isDynamicRoute(dirName) {
  return dirName.startsWith('[') && dirName.endsWith(']');
}

// Função para listar todos os diretórios em um caminho
function listDirectories(dirPath) {
  if (!fs.existsSync(dirPath)) return [];
  
  return fs.readdirSync(dirPath, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);
}

// Função para encontrar todas as rotas dinâmicas
function findDynamicRoutes(basePath, prefix = '') {
  const appDir = path.join(process.cwd(), basePath);
  let routes = [];
  
  // Verifica se o diretório existe
  if (!fs.existsSync(appDir)) return routes;
  
  // Lista todos os diretórios
  const dirs = listDirectories(appDir);
  
  for (const dir of dirs) {
    const fullPath = path.join(appDir, dir);
    const relativePath = prefix ? `${prefix}/${dir}` : dir;
    
    if (isDynamicRoute(dir)) {
      routes.push({
        path: relativePath,
        fullPath: fullPath
      });
    }
    
    // Recursivamente procura em subdiretórios
    const subRoutes = findDynamicRoutes(fullPath, relativePath);
    routes = routes.concat(subRoutes);
  }
  
  return routes;
}

// Encontra todas as rotas dinâmicas
const dynamicRoutes = findDynamicRoutes('src/app');

console.log('Rotas dinâmicas encontradas:');
dynamicRoutes.forEach(route => {
  console.log(`- ${route.path} (${route.fullPath})`);
});

// Pergunta se deseja remover todas as rotas dinâmicas
if (dynamicRoutes.length > 0) {
  console.log('\nRemovendo todas as rotas dinâmicas...');
  dynamicRoutes.forEach(route => {
    fs.rmSync(route.fullPath, { recursive: true, force: true });
    console.log(`Removida: ${route.path}`);
  });
  console.log('Todas as rotas dinâmicas foram removidas.');
} else {
  console.log('Nenhuma rota dinâmica encontrada.');
}