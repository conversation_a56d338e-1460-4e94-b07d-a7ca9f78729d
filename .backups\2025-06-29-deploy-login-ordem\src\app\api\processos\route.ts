import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';
import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parâmetros de busca e filtros
    const search = searchParams.get('search') || undefined;
    const status = searchParams.get('status') || undefined;
    const modalidade = searchParams.get('modalidade') || undefined;
    const responsavel = searchParams.get('responsavel') || undefined;
    const requisitante = searchParams.get('requisitante') || undefined;

    console.log('🔍 API - Parâmetros recebidos:', {
      search, status, modalidade, responsavel, requisitante
    });
    
    // Parâmetros de paginação
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Buscar processos com filtros
    const processos = await CSVReader.searchProcessos({
      search,
      status,
      modalidade,
      responsavel,
      requisitante,
    });

    console.log('🔍 API - Processos encontrados:', processos.length);

    // Aplicar paginação
    const totalProcessos = processos.length;
    const processosPaginados = processos.slice(offset, offset + limit);

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalProcessos / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        processos: processosPaginados,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalProcessos,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error('Erro na API de processos:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { numeroProcesso, origem, responsavel, dataRecebimento, horaRecebimento, observacoes, prioridade } = body;

    // Validar dados obrigatórios
    if (!numeroProcesso || !origem || !responsavel) {
      return NextResponse.json(
        {
          success: false,
          error: 'Dados obrigatórios não fornecidos (numeroProcesso, origem, responsavel)',
        },
        { status: 400 }
      );
    }

    // Verificar se o processo já existe
    const processosExistentes = await CSVReader.getAllProcessos();
    const processoExistente = processosExistentes.find(p => p.PROCESSO === numeroProcesso);

    if (processoExistente) {
      return NextResponse.json(
        {
          success: false,
          error: 'Processo já existe no sistema',
        },
        { status: 409 }
      );
    }

    // Criar novo processo
    const novoProcesso = {
      ITEM: processosExistentes.length + 1,
      PROCESSO: numeroProcesso,
      REQUISITANTE: origem,
      OBJETO: 'Processo recebido via sistema',
      MODALIDADE: 'A definir',
      PRIORIDADE: prioridade === 'alta' || prioridade === 'urgente' ? 'Alta' : 'Normal',
      'DATA DE INÍCIO DO PROCESSO': dataRecebimento,
      'DATA ENTRADA NA CLMP': dataRecebimento,
      'VALOR ESTIMADO': 'A definir',
      'Fonte 0001 (TESOURO)': '',
      'Fonte 0002 (ESTADUAL)': '',
      'Fonte 0003 (FUNDO)': '',
      'Fonte 0005 (FEDERAL)': '',
      'Fonte 0007 (FINISA)': '',
      'Nº DO CERTAME': '',
      'DATA PUBLICAÇÃO': '',
      'DATA ABERTURA': '',
      'VALOR CONTRATADO': '',
      'CONTRATO NÚMERO': '',
      VENCIMENTO: '',
      'PROCESSO DE GERENCIAMENTO': '',
      STATUS: 'Recebido - aguardando análise inicial',
      DATA: dataRecebimento,
      LOCAL: 'CLMP',
      'RESPONSÁVEL': responsavel,
      OBSERVACOES: observacoes || ''
    };

    // Adicionar ao CSV (simulação - em produção seria salvo no arquivo)
    console.log('📥 Novo processo recebido:', novoProcesso);

    // Log da ação
    console.log(`✅ Processo ${numeroProcesso} recebido com sucesso por ${responsavel} em ${dataRecebimento}`);

    return NextResponse.json({
      success: true,
      data: {
        processo: novoProcesso,
        message: 'Processo recebido com sucesso'
      },
    });
  } catch (error) {
    console.error('Erro ao receber processo:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
