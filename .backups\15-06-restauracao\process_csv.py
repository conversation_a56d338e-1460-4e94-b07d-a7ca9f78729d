import csv
import sys

def process_csv(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.reader(infile)
        rows = list(reader)
    
    # Remove quotes and clean data
    cleaned_rows = []
    for row in rows:
        cleaned_row = [cell.strip('"') for cell in row]
        cleaned_rows.append(cleaned_row)
    
    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerows(cleaned_rows)
    
    print(f"Processed {len(cleaned_rows)} rows")
    print(f"Total processes: {len(cleaned_rows) - 1}")  # Subtract header

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python process_csv.py input_file output_file")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    process_csv(input_file, output_file)
