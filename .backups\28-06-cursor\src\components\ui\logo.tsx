'use client';

import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
}

export default function Logo({ size = 'md', className = '', showText = true }: LogoProps) {
  const sizes = {
    sm: { logo: 24, text: 'text-sm' },
    md: { logo: 32, text: 'text-lg' },
    lg: { logo: 40, text: 'text-xl' },
    xl: { logo: 48, text: 'text-2xl' }
  };

  const currentSize = sizes[size];

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Logo SVG - i+P fusionados */}
      <div className="relative">
        <svg
          width={currentSize.logo}
          height={currentSize.logo}
          viewBox="0 0 48 48"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="drop-shadow-lg"
        >
          {/* Gradientes modernos */}
          <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="50%" stopColor="#1D4ED8" />
              <stop offset="100%" stopColor="#1E40AF" />
            </linearGradient>
            <linearGradient id="logoGradientDark" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#60A5FA" />
              <stop offset="50%" stopColor="#3B82F6" />
              <stop offset="100%" stopColor="#2563EB" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {/* Logo moderno - Círculo com "IP" estilizado */}
          <g className="dark:hidden">
            {/* Círculo de fundo com gradiente */}
            <circle
              cx="24"
              cy="24"
              r="20"
              fill="url(#logoGradient)"
              stroke="#1E40AF"
              strokeWidth="1"
              filter="url(#glow)"
            />

            {/* Letra "I" moderna */}
            <rect
              x="16"
              y="16"
              width="4"
              height="20"
              rx="2"
              fill="white"
            />
            <circle
              cx="18"
              cy="12"
              r="2.5"
              fill="white"
            />

            {/* Letra "P" moderna */}
            <path
              d="M26 16 L26 36 L30 36 L30 26 L36 26 C38.5 26 40 24.5 40 22 C40 19.5 38.5 18 36 18 L26 18 Z M30 20 L36 20 C37 20 37.5 20.5 37.5 22 C37.5 23.5 37 24 36 24 L30 24 L30 20 Z"
              fill="white"
            />
          </g>

          {/* Versão dark mode */}
          <g className="hidden dark:block">
            {/* Círculo de fundo com gradiente */}
            <circle
              cx="24"
              cy="24"
              r="20"
              fill="url(#logoGradientDark)"
              stroke="#60A5FA"
              strokeWidth="1"
              filter="url(#glow)"
            />

            {/* Letra "I" moderna */}
            <rect
              x="16"
              y="16"
              width="4"
              height="20"
              rx="2"
              fill="#0F172A"
            />
            <circle
              cx="18"
              cy="12"
              r="2.5"
              fill="#0F172A"
            />

            {/* Letra "P" moderna */}
            <path
              d="M26 16 L26 36 L30 36 L30 26 L36 26 C38.5 26 40 24.5 40 22 C40 19.5 38.5 18 36 18 L26 18 Z M30 20 L36 20 C37 20 37.5 20.5 37.5 22 C37.5 23.5 37 24 36 24 L30 24 L30 20 Z"
              fill="#0F172A"
            />
          </g>
        </svg>
      </div>

      {/* Texto do logo */}
      {showText && (
        <div className="flex flex-col">
          <span className={`font-bold text-foreground ${currentSize.text} leading-tight`}>
            InovaProcess
          </span>
        </div>
      )}
    </div>
  );
}

// Componente apenas do ícone
export function LogoIcon({ size = 'md', className = '' }: Omit<LogoProps, 'showText'>) {
  return <Logo size={size} className={className} showText={false} />;
}

// Componente para uso em botões/links
export function LogoButton({ size = 'md', className = '', href = '/dashboard' }: LogoProps & { href?: string }) {
  return (
    <a href={href} className={`flex items-center hover:opacity-80 transition-opacity ${className}`}>
      <Logo size={size} showText={true} />
    </a>
  );
}
