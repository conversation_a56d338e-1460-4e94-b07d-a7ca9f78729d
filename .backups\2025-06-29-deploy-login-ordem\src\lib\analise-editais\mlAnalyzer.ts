// Analisador com Machine Learning para documentos de licitação

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface RiscoIA {
  tipo: string;
  descricao: string;
  probabilidade: number;
  confianca: number;
  evidencias: string[];
  impacto: string;
}

interface SugestaoIA {
  categoria: string;
  titulo: string;
  descricao: string;
  prioridade: 'BAIXA' | 'MEDIA' | 'ALTA';
  confianca: number;
  acoes: string[];
}

interface AnaliseIA {
  scoreIA: number;
  riscosIdentificados: RiscoIA[];
  sugestoes: SugestaoIA[];
  padroes: any[];
  anomalias: any[];
  confiancaGeral: number;
}

export async function analisarComIA(documentos: DocumentoAnalise[]): Promise<AnaliseIA> {
  // Simular análise de ML (em produção, integrar com modelo real)
  
  const riscosIdentificados: RiscoIA[] = [];
  const sugestoes: SugestaoIA[] = [];
  const padroes: any[] = [];
  const anomalias: any[] = [];

  // Análise de padrões textuais
  for (const doc of documentos) {
    riscosIdentificados.push(...identificarRiscosML(doc));
    sugestoes.push(...gerarSugestoesML(doc));
    padroes.push(...identificarPadroes(doc));
    anomalias.push(...detectarAnomalias(doc));
  }

  // Análise cruzada entre documentos
  const analiseCoerencia = analisarCoerenciaML(documentos);
  riscosIdentificados.push(...analiseCoerencia.riscos);
  sugestoes.push(...analiseCoerencia.sugestoes);

  const scoreIA = calcularScoreIA(riscosIdentificados, sugestoes);
  const confiancaGeral = calcularConfiancaGeral(riscosIdentificados, sugestoes);

  return {
    scoreIA,
    riscosIdentificados,
    sugestoes,
    padroes,
    anomalias,
    confiancaGeral
  };
}

function identificarRiscosML(doc: DocumentoAnalise): RiscoIA[] {
  const riscos: RiscoIA[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Análise de linguagem vaga ou ambígua
  const termosVagos = ['aproximadamente', 'cerca de', 'mais ou menos', 'similar', 'parecido'];
  const termosEncontrados = termosVagos.filter(termo => conteudo.includes(termo));
  
  if (termosEncontrados.length > 0) {
    riscos.push({
      tipo: 'LINGUAGEM_VAGA',
      descricao: 'Linguagem vaga ou ambígua detectada',
      probabilidade: Math.min(0.9, termosEncontrados.length * 0.3),
      confianca: 0.85,
      evidencias: termosEncontrados,
      impacto: 'Pode gerar questionamentos sobre especificações'
    });
  }

  // Análise de valores inconsistentes
  const valoresRegex = /r\$\s*[\d.,]+/gi;
  const valores = conteudo.match(valoresRegex);
  
  if (valores && valores.length > 1) {
    const valoresNumericos = valores.map(v => parseFloat(v.replace(/[^\d,]/g, '').replace(',', '.')));
    const variacao = Math.max(...valoresNumericos) / Math.min(...valoresNumericos);
    
    if (variacao > 2) {
      riscos.push({
        tipo: 'VALORES_INCONSISTENTES',
        descricao: 'Valores com grande variação detectados',
        probabilidade: Math.min(0.8, variacao / 10),
        confianca: 0.75,
        evidencias: valores,
        impacto: 'Possível erro de estimativa ou especificação'
      });
    }
  }

  // Análise de prazos irreais
  const prazosRegex = /(\d+)\s*(dia|semana|mês|ano)/gi;
  const prazos = conteudo.match(prazosRegex);
  
  if (prazos) {
    prazos.forEach(prazo => {
      const numero = parseInt(prazo.match(/\d+/)?.[0] || '0');
      const unidade = prazo.match(/(dia|semana|mês|ano)/i)?.[0];
      
      if ((unidade === 'dia' && numero < 5) || (unidade === 'semana' && numero < 1)) {
        riscos.push({
          tipo: 'PRAZO_IRREAL',
          descricao: 'Prazo potencialmente inadequado detectado',
          probabilidade: 0.7,
          confianca: 0.8,
          evidencias: [prazo],
          impacto: 'Prazo pode ser insuficiente para execução adequada'
        });
      }
    });
  }

  return riscos;
}

function gerarSugestoesML(doc: DocumentoAnalise): SugestaoIA[] {
  const sugestoes: SugestaoIA[] = [];
  const conteudo = doc.conteudo.toLowerCase();

  // Sugestão para melhorar especificações técnicas
  if (doc.tipo === 'tr' && !conteudo.includes('norma') && !conteudo.includes('abnt')) {
    sugestoes.push({
      categoria: 'ESPECIFICACAO_TECNICA',
      titulo: 'Incluir referências normativas',
      descricao: 'Recomenda-se incluir normas técnicas aplicáveis',
      prioridade: 'MEDIA',
      confianca: 0.8,
      acoes: [
        'Incluir normas ABNT aplicáveis',
        'Referenciar padrões técnicos específicos',
        'Definir critérios de aceitação baseados em normas'
      ]
    });
  }

  // Remover sugestão de sustentabilidade - não há lei específica

  // Sugestão para garantias
  if (doc.tipo === 'edital' && !conteudo.includes('garantia') && !conteudo.includes('caução')) {
    sugestoes.push({
      categoria: 'GARANTIAS',
      titulo: 'Definir garantias contratuais',
      descricao: 'Especificar garantias necessárias para a contratação',
      prioridade: 'ALTA',
      confianca: 0.9,
      acoes: [
        'Definir percentual de garantia de execução',
        'Especificar modalidades de garantia aceitas',
        'Estabelecer prazo de garantia dos produtos/serviços'
      ]
    });
  }

  return sugestoes;
}

function identificarPadroes(doc: DocumentoAnalise): any[] {
  const padroes = [];
  const conteudo = doc.conteudo;

  // Padrão de estrutura de documento
  const secoes = ['justificativa', 'objeto', 'especificação', 'prazo', 'valor'];
  const secoesEncontradas = secoes.filter(secao => 
    conteudo.toLowerCase().includes(secao)
  );

  padroes.push({
    tipo: 'ESTRUTURA_DOCUMENTO',
    completude: (secoesEncontradas.length / secoes.length) * 100,
    secoesPresentes: secoesEncontradas,
    secoesFaltantes: secoes.filter(s => !secoesEncontradas.includes(s))
  });

  return padroes;
}

function detectarAnomalias(doc: DocumentoAnalise): any[] {
  const anomalias = [];
  const conteudo = doc.conteudo;

  // Detectar texto muito curto
  if (conteudo.length < 500) {
    anomalias.push({
      tipo: 'DOCUMENTO_MUITO_CURTO',
      severidade: 'MEDIA',
      descricao: 'Documento pode estar incompleto',
      valor: conteudo.length
    });
  }

  // Detectar repetições excessivas
  const palavras = conteudo.toLowerCase().split(/\s+/);
  const contadorPalavras = palavras.reduce((acc: any, palavra) => {
    acc[palavra] = (acc[palavra] || 0) + 1;
    return acc;
  }, {});

  const palavrasRepetidas = Object.entries(contadorPalavras)
    .filter(([palavra, count]: [string, any]) => count > 10 && palavra.length > 3)
    .map(([palavra]) => palavra);

  if (palavrasRepetidas.length > 0) {
    anomalias.push({
      tipo: 'REPETICAO_EXCESSIVA',
      severidade: 'BAIXA',
      descricao: 'Palavras com repetição excessiva detectadas',
      palavras: palavrasRepetidas
    });
  }

  return anomalias;
}

function analisarCoerenciaML(documentos: DocumentoAnalise[]): { riscos: RiscoIA[], sugestoes: SugestaoIA[] } {
  const riscos: RiscoIA[] = [];
  const sugestoes: SugestaoIA[] = [];

  // Verificar coerência de valores entre documentos
  const valoresETP = extrairValores(documentos.find(d => d.tipo === 'etp')?.conteudo || '');
  const valoresEdital = extrairValores(documentos.find(d => d.tipo === 'edital')?.conteudo || '');

  if (valoresETP.length > 0 && valoresEdital.length > 0) {
    const diferencaPercentual = Math.abs(valoresETP[0] - valoresEdital[0]) / valoresETP[0] * 100;
    
    if (diferencaPercentual > 10) {
      riscos.push({
        tipo: 'INCOERENCIA_VALORES',
        descricao: 'Valores divergentes entre ETP e Edital',
        probabilidade: 0.8,
        confianca: 0.9,
        evidencias: [`ETP: R$ ${valoresETP[0]}`, `Edital: R$ ${valoresEdital[0]}`],
        impacto: 'Pode indicar erro de estimativa ou atualização'
      });
    }
  }

  return { riscos, sugestoes };
}

function extrairValores(conteudo: string): number[] {
  const valoresRegex = /r\$\s*([\d.,]+)/gi;
  const matches = conteudo.match(valoresRegex) || [];
  return matches.map(v => parseFloat(v.replace(/[^\d,]/g, '').replace(',', '.')));
}

function calcularScoreIA(riscos: RiscoIA[], sugestoes: SugestaoIA[]): number {
  let penalizacao = 0;
  
  riscos.forEach(risco => {
    penalizacao += risco.probabilidade * 20;
  });

  sugestoes.forEach(sugestao => {
    if (sugestao.prioridade === 'ALTA') penalizacao += 5;
    else if (sugestao.prioridade === 'MEDIA') penalizacao += 3;
  });

  return Math.max(0, Math.round(100 - penalizacao));
}

function calcularConfiancaGeral(riscos: RiscoIA[], sugestoes: SugestaoIA[]): number {
  const todasConfiancas = [
    ...riscos.map(r => r.confianca),
    ...sugestoes.map(s => s.confianca)
  ];

  if (todasConfiancas.length === 0) return 0.5;

  return todasConfiancas.reduce((acc, conf) => acc + conf, 0) / todasConfiancas.length;
}
