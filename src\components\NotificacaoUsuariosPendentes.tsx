'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Users, 
  Clock, 
  AlertTriangle, 
  ArrowRight,
  Bell,
  CheckCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface UsuarioPendente {
  uid: string;
  email: string;
  nome: string;
  dataCadastro: any;
}

export default function NotificacaoUsuariosPendentes() {
  const { userProfile } = useAuth();
  const router = useRouter();
  const [usuariosPendentes, setUsuariosPendentes] = useState<UsuarioPendente[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userProfile?.perfil === 'admin') {
      carregarUsuariosPendentes();
    }
  }, [userProfile]);

  const carregarUsuariosPendentes = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/usuarios?status=pendente');
      if (!response.ok) {
        throw new Error('Erro ao carregar usuários pendentes');
      }

      const usuarios = await response.json();
      setUsuariosPendentes(usuarios);
    } catch (error) {
      console.error('Erro ao carregar usuários pendentes:', error);
      setError('Erro ao carregar usuários pendentes');
    } finally {
      setLoading(false);
    }
  };

  const formatarData = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleString('pt-BR');
    } catch {
      return 'N/A';
    }
  };

  // Só mostrar para admins
  if (userProfile?.perfil !== 'admin') {
    return null;
  }

  // Se não há usuários pendentes, não mostrar nada
  if (!loading && usuariosPendentes.length === 0) {
    return null;
  }

  return (
    <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-800">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-yellow-800 dark:text-yellow-200">
          <AlertTriangle className="mr-2 h-5 w-5" />
          Usuários Pendentes de Aprovação
          <Badge variant="secondary" className="ml-2 bg-yellow-200 text-yellow-800">
            {usuariosPendentes.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error ? (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : loading ? (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
            <span className="text-sm text-yellow-700 dark:text-yellow-300">
              Carregando usuários pendentes...
            </span>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Existem <strong>{usuariosPendentes.length}</strong> usuário(s) aguardando sua aprovação.
            </p>
            
            {/* Lista resumida dos usuários */}
            <div className="space-y-2">
              {usuariosPendentes.slice(0, 3).map((usuario) => (
                <div
                  key={usuario.uid}
                  className="flex items-center justify-between p-2 bg-white dark:bg-yellow-900/40 rounded border border-yellow-200 dark:border-yellow-700"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                      <Users className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {usuario.nome}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-300">
                        {usuario.email}
                      </p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatarData(usuario.dataCadastro)}
                  </div>
                </div>
              ))}
              
              {usuariosPendentes.length > 3 && (
                <div className="text-center py-2">
                  <p className="text-xs text-yellow-600 dark:text-yellow-400">
                    +{usuariosPendentes.length - 3} usuário(s) adicional(is)
                  </p>
                </div>
              )}
            </div>

            {/* Botão para gerenciar */}
            <Button
              onClick={() => router.push('/admin/usuarios')}
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-white"
              size="sm"
            >
              <Bell className="mr-2 h-4 w-4" />
              Gerenciar Usuários
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>

            {/* Dica */}
            <div className="text-xs text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/40 p-2 rounded">
              💡 <strong>Dica:</strong> Você receberá notificações por email quando houver novos usuários pendentes.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 