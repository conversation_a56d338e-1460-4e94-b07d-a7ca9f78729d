import { NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET() {
  try {
    // Obter estatísticas dos processos
    const stats = await CSVReader.getEstatisticas();
    
    // Obter opções para filtros
    const filterOptions = await CSVReader.getFilterOptions();

    return NextResponse.json({
      success: true,
      data: {
        estatisticas: stats,
        filtros: filterOptions,
      },
    });
  } catch (error) {
    console.error('Erro na API de estatísticas:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
