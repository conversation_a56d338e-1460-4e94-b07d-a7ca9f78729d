const fs = require('fs');
const path = require('path');

// Script para processar o arquivo CSV de junho aplicando o mesmo tratamento do abril V1

function processJunhoCSV() {
  console.log('🔄 Iniciando processamento do arquivo de junho...');
  
  const inputPath = path.join(__dirname, '../../data/databackup-junho-v1/Acompanhamento de Processos - CLMP - 05-06.csv');
  const outputPath = path.join(__dirname, '../../data/databackup-junho-v1/Acompanhamento de Processos - CLMP - 05-06-processado.csv');
  
  try {
    // Ler arquivo original
    const content = fs.readFileSync(inputPath, 'utf-8');
    console.log('📄 Arquivo lido:', inputPath);
    
    // Dividir em linhas
    const lines = content.split('\n');
    console.log('📊 Total de linhas:', lines.length);
    
    // Processar cada linha
    const processedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Pular linhas vazias
      if (!line || line === ',,,,,,,,,,,,,,,,,,,,,,,') {
        continue;
      }
      
      // Processar linha
      let processedLine = line;
      
      // Substituir campos vazios por aspas vazias
      processedLine = processedLine.replace(/,-,/g, ',,');
      processedLine = processedLine.replace(/^-,/, ',');
      processedLine = processedLine.replace(/,-$/, ',');
      
      // Adicionar aspas duplas em todos os campos se não tiver
      if (i === 0) {
        // Header - adicionar aspas
        const fields = processedLine.split(',');
        const quotedFields = fields.map(field => {
          field = field.trim();
          if (!field.startsWith('"')) {
            return `"${field}"`;
          }
          return field;
        });
        processedLine = quotedFields.join(',');
      } else {
        // Dados - processar campos
        const fields = [];
        let currentField = '';
        let inQuotes = false;
        
        for (let j = 0; j < processedLine.length; j++) {
          const char = processedLine[j];
          
          if (char === '"') {
            inQuotes = !inQuotes;
            currentField += char;
          } else if (char === ',' && !inQuotes) {
            // Fim do campo
            let field = currentField.trim();
            
            // Se campo não tem aspas, adicionar
            if (!field.startsWith('"') && field !== '') {
              field = `"${field}"`;
            } else if (field === '' || field === '-') {
              field = '';
            }
            
            fields.push(field);
            currentField = '';
          } else {
            currentField += char;
          }
        }
        
        // Último campo
        let field = currentField.trim();
        if (!field.startsWith('"') && field !== '') {
          field = `"${field}"`;
        } else if (field === '' || field === '-') {
          field = '';
        }
        fields.push(field);
        
        processedLine = fields.join(',');
      }
      
      processedLines.push(processedLine);
    }
    
    console.log('✅ Linhas processadas:', processedLines.length);
    
    // Escrever arquivo processado
    const finalContent = processedLines.join('\n');
    fs.writeFileSync(outputPath, finalContent, 'utf-8');
    
    console.log('✅ Arquivo processado salvo:', outputPath);
    console.log('📊 Total de registros válidos:', processedLines.length - 1); // -1 para header
    
  } catch (error) {
    console.error('❌ Erro ao processar arquivo:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  processJunhoCSV();
}

module.exports = { processJunhoCSV };
