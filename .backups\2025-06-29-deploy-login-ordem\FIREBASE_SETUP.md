# 🔐 Configuração da Autenticação Google (Firebase)

## 📋 Passo a Passo

### 1. Criar Projeto Firebase
1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. Nome: `inovaprocess-auth`
4. Desabilite Google Analytics (opcional)
5. Clique em "Criar projeto"

### 2. Configurar Authentication
1. No painel lateral, clique em "Authentication"
2. Clique em "Começar"
3. Vá para a aba "Sign-in method"
4. Clique em "Google"
5. Ative o provedor Google
6. Configure:
   - **Nome público do projeto**: InovaProcess
   - **Email de suporte**: <EMAIL>
7. Clique em "Salvar"

### 3. Configurar Domínios Autorizados
1. Na aba "Settings" > "Authorized domains"
2. Adicione os domínios:
   - `localhost` (para desenvolvimento)
   - `seu-dominio.com` (para produção)

### 4. Obter Configurações
1. Clique no ícone de engrenagem ⚙️ > "Configurações do projeto"
2. Role até "Seus aplicativos"
3. Clique em "Adicionar app" > ícone da web `</>`
4. Nome do app: `InovaProcess Web`
5. Copie as configurações que aparecem

### 5. Configurar Variáveis de Ambiente
1. Copie `.env.local.example` para `.env.local`
2. Preencha com as configurações do Firebase:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSy...
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=inovaprocess-auth.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=inovaprocess-auth
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=inovaprocess-auth.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abc123
```

### 6. Testar Autenticação
1. Execute `npm run dev`
2. Acesse `http://localhost:3001`
3. Tente fazer login com sua conta Gmail
4. Verifique se o login funciona corretamente

## 🔒 Segurança

- ✅ Apenas contas Gmail são aceitas
- ✅ Verificação automática de domínio
- ✅ Logout automático para contas inválidas
- ✅ Proteção de rotas implementada

## 🚀 Deploy

Para produção, configure os domínios autorizados no Firebase:
- Adicione seu domínio de produção
- Configure CORS se necessário
- Teste a autenticação em produção

## 📞 Suporte

Se tiver problemas:
1. Verifique se todas as variáveis estão configuradas
2. Confirme que o domínio está autorizado
3. Verifique o console do navegador para erros
4. Teste com uma conta Gmail válida
