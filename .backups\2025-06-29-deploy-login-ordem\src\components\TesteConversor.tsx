/**
 * 🧪 COMPONENTE DE TESTE PARA CONVERSOR DE MEDIDAS
 * Demonstra o funcionamento da IA de conversão
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { IAConversorMedidas, AlertaUnidade } from '@/lib/conversorMedidas';

export default function TesteConversor() {
  const [itemDesejado, setItemDesejado] = useState('Álcool gel 1 litro');
  const [alertas, setAlertas] = useState<AlertaUnidade[]>([]);
  const [testesRealizados, setTestesRealizados] = useState(false);

  // DADOS REAIS DE FORNECEDORES - SEM MOCK DATA
  const [fornecedoresReais, setFornecedoresReais] = useState<any[]>([]);

  // Carregar fornecedores reais do banco de dados
  useEffect(() => {
    const loadFornecedoresReais = async () => {
      try {
        const response = await fetch('/api/fornecedores');
        const data = await response.json();

        if (data.success) {
          setFornecedoresReais(data.data.fornecedores || []);
        }
      } catch (error) {
        console.error('Erro ao carregar fornecedores reais:', error);
        setFornecedoresReais([]);
      }
    };

    loadFornecedoresReais();
  }, []);

  const testarConversor = () => {
    const alertasGerados = IAConversorMedidas.gerarAlertas(itemDesejado, fornecedoresExemplo);
    setAlertas(alertasGerados);
    setTestesRealizados(true);
  };

  const exemplosTeste = [
    'Álcool gel 1 litro',
    'Papel A4 1 resma',
    'Detergente 500ml',
    'Açúcar 1 kg'
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            🧪 Teste do Conversor de Medidas IA
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Input
              placeholder="Digite o item desejado (ex: Álcool gel 1 litro)"
              value={itemDesejado}
              onChange={(e) => setItemDesejado(e.target.value)}
              className="flex-1"
            />
            <Button onClick={testarConversor}>
              🔍 Testar Conversão
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-muted-foreground">Exemplos:</span>
            {exemplosTeste.map((exemplo, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setItemDesejado(exemplo)}
              >
                {exemplo}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Fornecedores de Exemplo */}
      <Card>
        <CardHeader>
          <CardTitle>Fornecedores Simulados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {fornecedoresExemplo.map((fornecedor, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="font-medium">{fornecedor.nome}</div>
                <div className="text-sm text-muted-foreground">{fornecedor.descricao}</div>
                <div className="text-sm font-medium">R$ {fornecedor.preco.toFixed(2)}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Resultados dos Alertas */}
      {testesRealizados && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              🚨 Alertas de Conversão Detectados
              <Badge className="ml-2">{alertas.length} alertas</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {alertas.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                ✅ Nenhum alerta de conversão detectado!
                <br />
                <span className="text-sm">Todas as unidades são compatíveis.</span>
              </div>
            ) : (
              <div className="space-y-4">
                {alertas.map((alerta, index) => (
                  <div 
                    key={index} 
                    className={`p-4 rounded-lg border ${
                      alerta.tipo === 'incompatibilidade' 
                        ? 'bg-red-50 border-red-200' 
                        : 'bg-yellow-50 border-yellow-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="font-medium">{alerta.fornecedor}</div>
                      <Badge 
                        variant={alerta.tipo === 'incompatibilidade' ? 'destructive' : 'warning'}
                      >
                        {alerta.tipo === 'conversao' ? '🔄 Conversão' : '🚫 Incompatível'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Você quer:</span>
                        <p className="font-medium">{alerta.unidadeDesejada}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Fornecedor vende:</span>
                        <p className="font-medium">{alerta.unidadeFornecedor}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Conversão:</span>
                        <p className="font-medium">{alerta.conversaoNecessaria}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Fator:</span>
                        <p className="font-medium">
                          {alerta.fatorMultiplicacao > 0 ? `${alerta.fatorMultiplicacao.toFixed(2)}x` : 'N/A'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-3 p-2 bg-white rounded border">
                      <span className="text-xs text-muted-foreground">Observação:</span>
                      <p className="text-sm">{alerta.observacao}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Demonstração de Detecção */}
      {testesRealizados && (
        <Card>
          <CardHeader>
            <CardTitle>🧠 Análise IA da Detecção</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <span className="text-sm text-muted-foreground">Item desejado analisado:</span>
                <p className="font-medium">{itemDesejado}</p>
                <div className="text-xs text-muted-foreground mt-1">
                  Unidade detectada: {IAConversorMedidas.detectarUnidade(itemDesejado)?.unidadeOriginal || 'Não detectada'}
                  {IAConversorMedidas.detectarUnidade(itemDesejado) && (
                    <span className="ml-2">
                      (Tipo: {IAConversorMedidas.detectarUnidade(itemDesejado)?.tipo})
                    </span>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fornecedoresExemplo.map((fornecedor, index) => {
                  const unidade = IAConversorMedidas.detectarUnidade(fornecedor.descricao);
                  return (
                    <div key={index} className="p-3 bg-gray-50 rounded border">
                      <div className="font-medium text-sm">{fornecedor.nome}</div>
                      <div className="text-xs text-muted-foreground">{fornecedor.descricao}</div>
                      <div className="text-xs mt-1">
                        Unidade: {unidade?.unidadeOriginal || 'Não detectada'}
                        {unidade && <span className="ml-2">(Tipo: {unidade.tipo})</span>}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
