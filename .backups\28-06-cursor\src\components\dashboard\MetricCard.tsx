'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MetricCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
}

export function MetricCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  variant = 'default'
}: MetricCardProps) {
  const variantStyles = {
    default: 'border-border',
    success: 'border-green-300/60 bg-gradient-to-br from-green-200/70 via-green-100/60 to-green-200/70 dark:border-green-500/70 dark:from-green-700/30 dark:via-green-600/20 dark:to-green-500/15',
    warning: 'border-yellow-300/60 bg-gradient-to-br from-yellow-200/70 via-yellow-100/60 to-yellow-200/70 dark:border-yellow-500/70 dark:from-yellow-700/30 dark:via-yellow-600/20 dark:to-yellow-500/15',
    destructive: 'border-red-300/60 bg-gradient-to-br from-red-200/70 via-red-100/60 to-red-200/70 dark:border-red-500/70 dark:from-red-700/30 dark:via-red-600/20 dark:to-red-500/15'
  };

  const iconStyles = {
    default: 'text-muted-foreground',
    success: 'text-green-600 dark:text-green-300',
    warning: 'text-yellow-600 dark:text-yellow-300',
    destructive: 'text-red-600 dark:text-red-300'
  };

  return (
    <Card className={cn(variantStyles[variant], className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={cn('h-5 w-5', iconStyles[variant])} />
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold text-foreground mb-1">
          {typeof value === 'number' ? value.toLocaleString('pt-BR') : value}
        </div>
        
        {description && (
          <p className="text-sm text-muted-foreground mb-2">
            {description}
          </p>
        )}
        
        {trend && (
          <div className="flex items-center space-x-2">
            <Badge 
              variant={trend.isPositive ? 'success' : 'destructive'}
              className="text-xs"
            >
              {trend.isPositive ? '+' : ''}{trend.value}%
            </Badge>
            <span className="text-xs text-muted-foreground">
              {trend.label}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
