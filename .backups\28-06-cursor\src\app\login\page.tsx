'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { signInWithGoogle } from '@/lib/auth';
import { useTheme } from 'next-themes';
import {
  LogIn,
  Mail,
  Lock,
  Building,
  Shield,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Sun,
  Moon,
  FileText,
  X
} from 'lucide-react';

export default function LoginPage() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    senha: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Limpar erro ao digitar
  };

  const handleGoogleLogin = async () => {
    // Verificar se os termos foram aceitos
    if (!termsAccepted) {
      setShowTerms(true);
      return;
    }

    setLoading(true);
    setError('');

    try {
      // LOGIN REAL COM GOOGLE FIREBASE
      await signInWithGoogle();
      console.log('✅ Login realizado com sucesso');
      router.push('/dashboard');
    } catch (err: any) {
      console.error('❌ Erro no login:', err);
      setError(err.message || 'Erro ao fazer login com Google');
      setLoading(false);
    }
  };

  const isFormValid = formData.email && formData.senha;

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-black flex items-center justify-center p-4 relative">
      {/* Botão Dark Mode */}
      {mounted && (
        <div className="absolute top-4 right-4 z-50">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="bg-white/80 border-slate-200 hover:bg-white text-slate-700 dark:bg-slate-800/80 dark:border-slate-600 dark:hover:bg-slate-700 dark:text-white"
          >
            {theme === 'dark' ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Moon className="h-4 w-4" />
            )}
          </Button>
        </div>
      )}

      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-3 bg-white rounded-xl shadow-lg">
              <Building className="h-10 w-10 text-slate-700" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-slate-800 dark:text-white">InovaProcess</h1>
              <p className="text-slate-600 dark:text-slate-300 text-sm font-medium">
                Sistema de Gestão de Processos
              </p>
            </div>
          </div>

        </div>

        {/* Modal de Termos e Condições */}
        {showTerms && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-2xl max-h-[80vh] flex flex-col shadow-2xl bg-white dark:bg-slate-800">
              <CardHeader className="bg-blue-600 dark:bg-blue-700 text-white flex-shrink-0">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Termos de Uso e Política de Privacidade
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowTerms(false)}
                    className="text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-6 overflow-y-auto flex-1 min-h-0">
                <div className="space-y-6 text-sm text-slate-700 dark:text-slate-300">

                  <section>
                    <h3 className="font-bold text-lg text-slate-800 dark:text-slate-200 mb-3">
                      📋 Termos de Uso do Sistema InovaProcess
                    </h3>
                    <div className="space-y-3">
                      <p>
                        <strong>1. Aceitação dos Termos:</strong> Ao utilizar o sistema InovaProcess, você concorda em cumprir estes termos de uso e todas as políticas aplicáveis.
                      </p>
                      <p>
                        <strong>2. Uso Autorizado:</strong> O sistema destina-se exclusivamente ao gerenciamento de processos administrativos. O uso deve ser profissional e ético.
                      </p>
                      <p>
                        <strong>3. Responsabilidades do Usuário:</strong> Você é responsável por manter a confidencialidade de suas credenciais e por todas as atividades realizadas em sua conta.
                      </p>
                      <p>
                        <strong>4. Proteção de Dados:</strong> Todos os dados inseridos no sistema são tratados com confidencialidade e segurança, conforme a LGPD.
                      </p>
                    </div>
                  </section>

                  <section>
                    <h3 className="font-bold text-lg text-slate-800 dark:text-slate-200 mb-3">
                      🔒 Política de Privacidade
                    </h3>
                    <div className="space-y-3">
                      <p>
                        <strong>Coleta de Dados:</strong> Coletamos apenas informações necessárias para o funcionamento do sistema, incluindo dados de autenticação e informações de processos.
                      </p>
                      <p>
                        <strong>Uso dos Dados:</strong> Os dados são utilizados exclusivamente para fins administrativos e gerenciamento de processos.
                      </p>
                      <p>
                        <strong>Compartilhamento:</strong> Dados não são compartilhados com terceiros, exceto quando exigido por lei.
                      </p>
                      <p>
                        <strong>Segurança:</strong> Implementamos medidas técnicas e organizacionais para proteger seus dados contra acesso não autorizado.
                      </p>
                    </div>
                  </section>

                  <section>
                    <h3 className="font-bold text-lg text-slate-800 dark:text-slate-200 mb-3">
                      ⚖️ Conformidade Legal
                    </h3>
                    <div className="space-y-3">
                      <p>
                        <strong>LGPD:</strong> Este sistema está em conformidade com a Lei Geral de Proteção de Dados (Lei nº 13.709/2018).
                      </p>
                      <p>
                        <strong>Direitos do Titular:</strong> Você tem direito ao acesso, correção, exclusão e portabilidade de seus dados pessoais.
                      </p>

                    </div>
                  </section>

                </div>
              </CardContent>
              <div className="p-6 bg-white dark:bg-slate-800 border-t border-gray-200 dark:border-slate-600 flex-shrink-0">
                <div className="flex items-center space-x-4 mb-4 p-4">
                  <label className="flex items-center space-x-3 cursor-pointer w-full">
                    <input
                      type="checkbox"
                      checked={termsAccepted}
                      onChange={(e) => setTermsAccepted(e.target.checked)}
                      className="w-5 h-5 text-blue-600 bg-white border-2 border-blue-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-slate-700 dark:border-blue-500"
                    />
                    <span className="text-base font-semibold text-slate-900 dark:text-white">
                      Li e aceito os termos de uso e política de privacidade
                    </span>
                  </label>
                </div>
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setShowTerms(false)}
                    className="px-6 py-2"
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={() => {
                      if (termsAccepted) {
                        setShowTerms(false);
                        handleGoogleLogin();
                      }
                    }}
                    disabled={!termsAccepted}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Aceitar e Continuar
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Login com Google */}
        <Card className="shadow-xl bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
          <CardHeader className="space-y-1 bg-blue-600 dark:bg-blue-700 text-white rounded-t-lg">
            <CardTitle className="text-xl text-center flex items-center justify-center">
              <LogIn className="mr-2 h-5 w-5" />
              Entrar no Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {error && (
                <Alert variant="destructive" className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                onClick={handleGoogleLogin}
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 hover:border-blue-400 shadow-md hover:shadow-lg transition-all duration-200 h-12"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
                    <span className="font-medium">Conectando...</span>
                  </>
                ) : (
                  <>
                    <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span className="font-medium">Entrar com Google</span>
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>


        {/* Footer */}
        <div className="text-center">
          <div className="bg-white/80 dark:bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-200 dark:border-white/20">
            <p className="text-slate-700 dark:text-white font-medium">InovaProcess © 2025</p>
          </div>
        </div>
      </div>
    </div>
  );
}
