export interface Usuario {
  id: string;
  nome: string;
  email: string;
  telefone?: string;
  secretaria: string;
  cargo: string;
  perfil: PerfilUsuario;
  status: 'ativo' | 'inativo' | 'bloqueado';
  ultimoAcesso?: string;
  dataCriacao: string;
  permissoes: string[]; // Usando TipoPermissao do sistema de permissões
  configuracoes?: ConfiguracaoUsuario;
  periodos?: PeriodoAfastamento[];
}

export interface PeriodoAfastamento {
  id: string;
  tipo: 'ferias' | 'licenca' | 'afastamento' | 'atestado' | 'capacitacao';
  dataInicio: string;
  dataFim: string;
  motivo?: string;
  observacoes?: string;
  status: 'agendado' | 'ativo' | 'concluido' | 'cancelado';
  criadoPor: string;
  criadoEm: string;
  atualizadoEm?: string;
  redistribuicao?: RedistribuicaoProcessos;
}

export interface RedistribuicaoProcessos {
  analisadoEm: string;
  totalProcessos: number;
  sugestoes: SugestaoRedistribuicao[];
  aprovadoPor?: string;
  aprovadoEm?: string;
  status: 'pendente' | 'aprovado' | 'executado';
}

export interface SugestaoRedistribuicao {
  processoId: string;
  processoNumero: string;
  usuarioAtual: string;
  usuarioSugerido: string;
  motivo: string;
  prioridade: 'alta' | 'media' | 'baixa';
  cargaTrabalho: number; // Número de processos que o usuário sugerido já tem
}

export type PerfilUsuario =
  | 'admin'           // Administrador do sistema
  | 'coordenador'     // Coordenador da CLMP
  | 'assessor'        // Assessor da CLMP
  | 'analista'        // Analista de processos
  | 'pesquisador'     // Pesquisador de preços
  | 'secretaria'      // Usuário de secretaria
  | 'consulta'        // Apenas consulta
  | 'estagiario';     // Estagiário

export type Permissao = 
  | 'processos.criar'
  | 'processos.editar'
  | 'processos.visualizar'
  | 'processos.excluir'
  | 'contratos.criar'
  | 'contratos.editar'
  | 'contratos.visualizar'
  | 'contratos.excluir'
  | 'usuarios.criar'
  | 'usuarios.editar'
  | 'usuarios.visualizar'
  | 'usuarios.excluir'
  | 'relatorios.gerar'
  | 'relatorios.exportar'
  | 'configuracoes.sistema'
  | 'configuracoes.secretaria'
  | 'prioridades.classificar'
  | 'alertas.gerenciar'
  | 'pesquisa.precos'
  | 'analise.editais';

export interface ConfiguracaoUsuario {
  tema: 'light' | 'dark' | 'auto';
  notificacoes: {
    email: boolean;
    sistema: boolean;
    tipos: TipoNotificacao[];
  };
  dashboard: {
    banners: string[];
    filtros: Record<string, any>;
  };
}

export type TipoNotificacao = 
  | 'novo_processo'
  | 'prazo_critico'
  | 'recurso_sem_data'
  | 'prioridade_parada'
  | 'gargalo_critico'
  | 'retrabalho';

export interface SessaoUsuario {
  usuario: Usuario;
  token: string;
  expiresAt: Date;
  permissions: Permissao[];
}

// Configurações de perfis padrão
export const PERFIS_PERMISSOES: Record<PerfilUsuario, Permissao[]> = {
  admin: [
    'processos.criar', 'processos.editar', 'processos.visualizar', 'processos.excluir',
    'contratos.criar', 'contratos.editar', 'contratos.visualizar', 'contratos.excluir',
    'usuarios.criar', 'usuarios.editar', 'usuarios.visualizar', 'usuarios.excluir',
    'relatorios.gerar', 'relatorios.exportar',
    'configuracoes.sistema', 'configuracoes.secretaria',
    'prioridades.classificar', 'alertas.gerenciar',
    'pesquisa.precos', 'analise.editais'
  ],
  gestor: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar', 'contratos.editar',
    'usuarios.visualizar',
    'relatorios.gerar', 'relatorios.exportar',
    'configuracoes.secretaria',
    'prioridades.classificar',
    'pesquisa.precos', 'analise.editais'
  ],
  pesquisador: [
    'processos.visualizar',
    'pesquisa.precos',
    'relatorios.gerar'
  ],
  consulta: [
    'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar'
  ],
  expediente: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar',
    'pesquisa.precos', 'analise.editais',
    'alertas.gerenciar' // Para receber alertas de recursos sem data
  ]
};

// Usuários que devem receber alertas específicos
export const USUARIOS_ALERTAS = {
  RECURSO_SEM_DATA: ['expediente'],
  PRIORIDADE_GOVERNO: ['admin'], // Secretário e Adjunta de Governo
  GARGALO_CRITICO: ['admin', 'gestor'],
  PRAZO_CRITICO: ['admin', 'gestor', 'analista']
} as const;
