'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Bar<PERSON><PERSON>3, <PERSON><PERSON><PERSON>, TrendingUp, Info } from 'lucide-react';

interface VisualChartProps {
  title: string;
  data: Array<{ name: string; value: number }>;
  type: 'bar' | 'pie' | 'donut';
  height?: number;
  showLegend?: boolean;
  colorScheme?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gradient';
}

const COLOR_SCHEMES = {
  blue: ['#3B82F6', '#60A5FA', '#93C5FD', '#DBEAFE', '#1E40AF', '#2563EB'],
  green: ['#10B981', '#34D399', '#6EE7B7', '#A7F3D0', '#059669', '#047857'],
  purple: ['#8B5CF6', '#A78BFA', '#C4B5FD', '#DDD6FE', '#7C3AED', '#6D28D9'],
  orange: ['#F59E0B', '#FBBF24', '#FCD34D', '#FDE68A', '#D97706', '#B45309'],
  red: ['#EF4444', '#F87171', '#FCA5A5', '#FECACA', '#DC2626', '#B91C1C'],
  gradient: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4']
};

export function VisualChart({
  title,
  data,
  type,
  height = 300,
  showLegend = true,
  colorScheme = 'gradient'
}: VisualChartProps) {
  const colors = COLOR_SCHEMES[colorScheme];
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const maxValue = Math.max(...data.map(d => d.value));

  // Debug temporário
  console.log(`[${title}] Data:`, data);
  console.log(`[${title}] Total:`, total);
  console.log(`[${title}] MaxValue:`, maxValue);

  if (!data || data.length === 0) {
    return (
      <Card className="h-full bg-card border-border">
        <CardHeader>
          <CardTitle className="flex items-center text-muted-foreground">
            <BarChart3 className="mr-2 h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center" style={{ height: height - 80 }}>
          <div className="text-center text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p>Sem dados para exibir</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderBarChart = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-lg flex items-center text-foreground">
          <BarChart3 className="mr-2 h-5 w-5 text-blue-500 dark:text-blue-400" />
          {title}
        </h3>
        <Badge variant="outline" className="text-xs">
          {data.length} itens • Max: {maxValue}
        </Badge>
      </div>

      <div className="space-y-3 max-h-64 overflow-y-auto">
        {data.slice(0, 10).map((item, index) => {
          const percentage = (item.value / maxValue) * 100;
          const color = '#3B82F6'; // Azul único para todos

          return (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="group cursor-pointer">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium truncate max-w-40 text-foreground" title={item.name}>
                        {item.name.length > 25 ? item.name.substring(0, 25) + '...' : item.name}
                      </span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-bold text-foreground">{item.value}</span>
                        <span className="text-xs text-muted-foreground">
                          ({((item.value / total) * 100).toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-300 dark:bg-gray-600 h-8 flex items-center px-2 rounded">
                      <div
                        className="bg-blue-500 h-6 rounded flex items-center justify-center text-white text-xs font-bold"
                        style={{
                          width: `${Math.max(percentage, 15)}%`,
                          backgroundColor: '#3B82F6',
                          minWidth: '50px'
                        }}
                      >
                        {item.value}
                      </div>
                    </div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-center">
                    <p className="font-semibold">{item.name}</p>
                    <p>Valor: {item.value}</p>
                    <p>Percentual: {((item.value / total) * 100).toFixed(1)}%</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    </div>
  );

  const renderPieChart = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-lg flex items-center text-foreground">
          <PieChart className="mr-2 h-5 w-5 text-purple-500 dark:text-purple-400" />
          {title}
        </h3>
        <Badge variant="outline" className="text-xs">
          Total: {total}
        </Badge>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Gráfico de Pizza SIMPLES E FUNCIONAL */}
        <div className="flex-shrink-0">
          <div className="relative w-48 h-48 mx-auto">
            {/* Gráfico de Barras Horizontais Empilhadas - SEMPRE FUNCIONA */}
            <div className="space-y-2">
              {data.slice(0, 8).map((item, index) => {
                const percentage = (item.value / total) * 100;
                const color = '#8B5CF6'; // Roxo único para todos

                return (
                  <TooltipProvider key={index}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="cursor-pointer hover:scale-105 transition-transform">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-xs font-medium text-foreground truncate" title={item.name}>
                              {item.name.length > 15 ? item.name.substring(0, 15) + '...' : item.name}
                            </span>
                            <span className="text-xs font-bold text-foreground ml-2">{item.value}</span>
                          </div>
                          <div className="w-full bg-gray-300 dark:bg-gray-600 h-5 flex items-center px-1 rounded">
                            <div
                              className="bg-purple-500 h-3 rounded flex items-center justify-center text-white text-xs"
                              style={{
                                width: `${Math.max(percentage, 12)}%`,
                                backgroundColor: '#8B5CF6',
                                minWidth: '30px'
                              }}
                            >
                              {item.value}
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-xs text-muted-foreground">{percentage.toFixed(1)}%</span>
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-semibold">{item.name}</p>
                          <p>Valor: {item.value}</p>
                          <p>Percentual: {percentage.toFixed(1)}%</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}


            </div>
          </div>
        </div>

        {/* Espaço para balanceamento visual */}
        <div className="flex-1"></div>
      </div>
    </div>
  );

  return (
    <Card className="h-full hover:shadow-lg transition-shadow duration-300 bg-card border-border">
      <CardContent className="p-6" style={{ height }}>
        {type === 'bar' && renderBarChart()}
        {(type === 'pie' || type === 'donut') && renderPieChart()}
      </CardContent>
    </Card>
  );
}
