'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Processo } from '@/types/processo';
import {
  ArrowLeft,
  Calendar,
  Building,
  User,
  FileText,
  DollarSign,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ProcessoResponse {
  success: boolean;
  data: Processo;
  error?: string;
}

export default function ProcessoDetalhesPage() {
  const params = useParams();
  const [processo, setProcesso] = useState<Processo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProcesso = async () => {
      // Aguardar params se necessário (Next.js 15)
      const resolvedParams = await Promise.resolve(params);
      if (!resolvedParams.id) return;

      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/processos/${encodeURIComponent(resolvedParams.id as string)}`);
        const data: ProcessoResponse = await response.json();

        if (data.success) {
          setProcesso(data.data);
        } else {
          setError(data.error || 'Processo não encontrado');
        }
      } catch (err) {
        setError('Erro de conexão. Tente novamente.');
        console.error('Erro ao carregar processo:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProcesso();
  }, [params]);

  // Função para determinar a cor do status
  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();

    if (statusLower.includes('concluído') || statusLower.includes('finalizado')) {
      return 'bg-green-100 text-green-800 border-green-200';
    }
    if (statusLower.includes('andamento') || statusLower.includes('aberta')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    if (statusLower.includes('cancelado') || statusLower.includes('suspenso')) {
      return 'bg-red-100 text-red-800 border-red-200';
    }
    if (statusLower.includes('aguardando') || statusLower.includes('análise')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }

    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    return value;
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    return dateString;
  };

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = (processo: Processo) => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-600 mb-4" />
          <p className="text-gray-600">Carregando detalhes do processo...</p>
        </div>
      </div>
    );
  }

  if (error || !processo) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-400 mb-4" />
          <h3 className="text-lg font-medium text-red-800 mb-2">
            Erro ao carregar processo
          </h3>
          <p className="text-red-600 mb-4">{error}</p>
          <Link
            href="/processos"
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
          >
            Voltar para lista
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>
      </div>

      {/* Título e Status */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {processo.PROCESSO || 'Processo sem número'}
            </h1>
            <p className="text-gray-600">
              Item: {processo.ITEM || 'Não informado'}
            </p>
          </div>

          {processo.STATUS && (
            <div className="mt-4 sm:mt-0">
              <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(processo.STATUS)}`}>
                {processo.STATUS}
              </span>
            </div>
          )}
        </div>

        {/* Tempo no setor atual */}
        {calcularTempoNoSetor(processo) && (
          <div className="border-t pt-4 mb-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="mr-3 text-yellow-600" size={20} />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800 mb-1">Tempo no Setor Atual</h3>
                    <p className="text-lg font-bold text-yellow-900">
                      {calcularTempoNoSetor(processo)}
                    </p>
                    <p className="text-sm text-yellow-700">
                      Local: {processo.LOCAL || 'Não informado'} | Responsável: {processo.RESPONSÁVEL || 'Não informado'}
                    </p>
                  </div>
                </div>
                {processo['DATA DE INÍCIO DO PROCESSO'] && (
                  <div className="text-right">
                    <h3 className="text-sm font-medium text-yellow-800 mb-1">Processo Aberto Há</h3>
                    <p className="text-lg font-bold text-yellow-900">
                      {(() => {
                        try {
                          const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
                          const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
                          const agora = new Date();
                          const diffMs = agora.getTime() - dataInicio.getTime();
                          const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                          return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
                        } catch {
                          return 'N/A';
                        }
                      })()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Objeto */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-500 mb-2">OBJETO</h3>
          <p className="text-gray-900 leading-relaxed">
            {processo.OBJETO || 'Objeto não informado'}
          </p>
        </div>
      </div>

      {/* Informações Principais */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informações Gerais */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FileText className="mr-2" size={20} />
            Informações Gerais
          </h2>

          <div className="space-y-4">
            <div className="flex items-center">
              <Building className="mr-3 text-gray-400" size={16} />
              <div>
                <span className="text-sm text-gray-500">Secretaria Requisitante</span>
                <p className="font-medium">{processo.REQUISITANTE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <FileText className="mr-3 text-gray-400" size={16} />
              <div>
                <span className="text-sm text-gray-500">Modalidade</span>
                <p className="font-medium">{processo.MODALIDADE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <User className="mr-3 text-gray-400" size={16} />
              <div>
                <span className="text-sm text-gray-500">Responsável Atual</span>
                <p className="font-medium">{processo.RESPONSÁVEL || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <Building className="mr-3 text-gray-400" size={16} />
              <div>
                <span className="text-sm text-gray-500">Local Atual</span>
                <p className="font-medium">{processo.LOCAL || 'Não informado'}</p>
              </div>
            </div>

            {processo.PRIORIDADE && (
              <div className="flex items-center">
                <div className="mr-3 text-gray-400">⚡</div>
                <div>
                  <span className="text-sm text-gray-500">Prioridade</span>
                  <p className="font-medium">{processo.PRIORIDADE}</p>
                </div>
              </div>
            )}

            {processo['Nº DO CERTAME'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-gray-400" size={16} />
                <div>
                  <span className="text-sm text-gray-500">Número do Certame</span>
                  <p className="font-medium">{processo['Nº DO CERTAME']}</p>
                </div>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-gray-400" size={16} />
                <div>
                  <span className="text-sm text-gray-500">Número do Contrato</span>
                  <p className="font-medium">{processo['CONTRATO NÚMERO']}</p>
                </div>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div className="flex items-center">
                <Calendar className="mr-3 text-gray-400" size={16} />
                <div>
                  <span className="text-sm text-gray-500">Vencimento</span>
                  <p className="font-medium">{formatDate(processo.VENCIMENTO)}</p>
                </div>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-gray-400" size={16} />
                <div>
                  <span className="text-sm text-gray-500">Processo de Gerenciamento</span>
                  <p className="font-medium">{processo['PROCESSO DE GERENCIAMENTO']}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Cronologia Completa */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="mr-2" size={20} />
            Cronologia e Tramitação
          </h2>

          <div className="space-y-4">
            {/* Timeline visual */}
            <div className="relative">
              <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

              {processo['DATA DE INÍCIO DO PROCESSO'] && (
                <div className="relative flex items-center pb-4">
                  <div className="absolute left-2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow"></div>
                  <div className="ml-10">
                    <span className="text-sm font-medium text-gray-900">Início do Processo</span>
                    <p className="text-sm text-gray-600">{formatDate(processo['DATA DE INÍCIO DO PROCESSO'])}</p>
                  </div>
                </div>
              )}

              {processo['DATA ENTRADA NA CLMP'] && (
                <div className="relative flex items-center pb-4">
                  <div className="absolute left-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow"></div>
                  <div className="ml-10">
                    <span className="text-sm font-medium text-gray-900">Entrada na CLMP</span>
                    <p className="text-sm text-gray-600">{formatDate(processo['DATA ENTRADA NA CLMP'])}</p>
                  </div>
                </div>
              )}

              {processo['DATA PUBLICAÇÃO'] && (
                <div className="relative flex items-center pb-4">
                  <div className="absolute left-2 w-4 h-4 bg-purple-500 rounded-full border-2 border-white shadow"></div>
                  <div className="ml-10">
                    <span className="text-sm font-medium text-gray-900">Publicação do Edital</span>
                    <p className="text-sm text-gray-600">{formatDate(processo['DATA PUBLICAÇÃO'])}</p>
                  </div>
                </div>
              )}

              {processo['DATA ABERTURA'] && (
                <div className="relative flex items-center pb-4">
                  <div className="absolute left-2 w-4 h-4 bg-orange-500 rounded-full border-2 border-white shadow"></div>
                  <div className="ml-10">
                    <span className="text-sm font-medium text-gray-900">Abertura da Licitação</span>
                    <p className="text-sm text-gray-600">{formatDate(processo['DATA ABERTURA'])}</p>
                  </div>
                </div>
              )}

              {processo.DATA && (
                <div className="relative flex items-center">
                  <div className="absolute left-2 w-4 h-4 bg-yellow-500 rounded-full border-2 border-white shadow"></div>
                  <div className="ml-10">
                    <span className="text-sm font-medium text-gray-900">Status Atual</span>
                    <p className="text-sm text-gray-600">{formatDate(processo.DATA)}</p>
                    <p className="text-xs text-gray-500 mt-1">{processo.STATUS}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Resumo de tempos */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Resumo de Tempos</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {processo['DATA DE INÍCIO DO PROCESSO'] && processo['DATA ENTRADA NA CLMP'] && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-xs text-gray-500">Tempo na Secretaria</span>
                    <p className="font-medium text-sm">
                      {(() => {
                        try {
                          const [diaI, mesI, anoI] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
                          const [diaE, mesE, anoE] = processo['DATA ENTRADA NA CLMP'].split('/');
                          const inicio = new Date(parseInt(anoI), parseInt(mesI) - 1, parseInt(diaI));
                          const entrada = new Date(parseInt(anoE), parseInt(mesE) - 1, parseInt(diaE));
                          const diff = Math.floor((entrada.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24));
                          return `${diff} dias`;
                        } catch {
                          return 'N/A';
                        }
                      })()}
                    </p>
                  </div>
                )}

                {processo['DATA ENTRADA NA CLMP'] && processo.DATA && (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-xs text-gray-500">Tempo na CLMP</span>
                    <p className="font-medium text-sm">
                      {(() => {
                        try {
                          const [diaE, mesE, anoE] = processo['DATA ENTRADA NA CLMP'].split('/');
                          const [diaA, mesA, anoA] = processo.DATA.split('/');
                          const entrada = new Date(parseInt(anoE), parseInt(mesE) - 1, parseInt(diaE));
                          const atual = new Date(parseInt(anoA), parseInt(mesA) - 1, parseInt(diaA));
                          const diff = Math.floor((atual.getTime() - entrada.getTime()) / (1000 * 60 * 60 * 24));
                          return `${diff} dias`;
                        } catch {
                          return 'N/A';
                        }
                      })()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Valores e Fontes */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <DollarSign className="mr-2" size={20} />
          Informações Financeiras
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {processo['VALOR ESTIMADO'] && processo['VALOR ESTIMADO'] !== '-' && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <span className="text-sm text-blue-600 font-medium">Valor Estimado</span>
              <p className="text-lg font-bold text-blue-900">{formatCurrency(processo['VALOR ESTIMADO'])}</p>
            </div>
          )}

          {processo['VALOR CONTRATADO'] && processo['VALOR CONTRATADO'] !== '-' && (
            <div className="p-4 bg-green-50 rounded-lg">
              <span className="text-sm text-green-600 font-medium">Valor Contratado</span>
              <p className="text-lg font-bold text-green-900">{formatCurrency(processo['VALOR CONTRATADO'])}</p>
            </div>
          )}

          {/* Fontes de Recursos */}
          <div className="md:col-span-2 lg:col-span-1">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Fontes de Recursos</h3>
            <div className="space-y-2">
              {processo['Fonte 0001 (TESOURO)'] && processo['Fonte 0001 (TESOURO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">Tesouro:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0001 (TESOURO)'])}</span>
                </div>
              )}
              {processo['Fonte 0002 (ESTADUAL)'] && processo['Fonte 0002 (ESTADUAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">Estadual:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0002 (ESTADUAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0003 (FUNDO)'] && processo['Fonte 0003 (FUNDO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">Fundo:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0003 (FUNDO)'])}</span>
                </div>
              )}
              {processo['Fonte 0005 (FEDERAL)'] && processo['Fonte 0005 (FEDERAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">Federal:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0005 (FEDERAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0007 (FINISA)'] && processo['Fonte 0007 (FINISA)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">FINISA:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0007 (FINISA)'])}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Informações do Certame e Contrato */}
      {(processo['Nº DO CERTAME'] || processo['CONTRATO NÚMERO']) && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Certame e Contrato
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {processo['Nº DO CERTAME'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Certame</span>
                <p className="font-medium text-lg">{processo['Nº DO CERTAME']}</p>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Contrato</span>
                <p className="font-medium text-lg">{processo['CONTRATO NÚMERO']}</p>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div>
                <span className="text-sm text-gray-500">Vencimento</span>
                <p className="font-medium">{formatDate(processo.VENCIMENTO)}</p>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div>
                <span className="text-sm text-gray-500">Processo de Gerenciamento</span>
                <p className="font-medium">{processo['PROCESSO DE GERENCIAMENTO']}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

