'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Search,
  Download,
  Upload,
  FileText,
  Calculator,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Trash2,
  Eye,
  BarChart3
} from 'lucide-react';
import { PNCPApi, PNCPSearchParams, PNCPSearchResult, PNCPItem } from '@/lib/pncpApi';
import { ItemPesquisa, PrecoFornecedor } from '@/types/processo';

interface PesquisaPrecosProps {
  processoId: string;
  pesquisador: string;
  onSalvar: (dados: any) => void;
}

export default function PesquisaPrecos({ processoId, pesquisador, onSalvar }: PesquisaPrecosProps) {
  const [etapa, setEtapa] = useState<'configuracao' | 'pesquisa' | 'analise' | 'finalizacao'>('configuracao');
  const [tipoPesquisa, setTipoPesquisa] = useState<'simples' | 'combinada'>('simples');
  const [termoBusca, setTermoBusca] = useState('');
  const [resultadoPNCP, setResultadoPNCP] = useState<PNCPSearchResult | null>(null);
  const [itensFinais, setItensFinais] = useState<ItemPesquisa[]>([]);
  const [loading, setLoading] = useState(false);
  const [arquivoFornecedores, setArquivoFornecedores] = useState<File | null>(null);
  const [dadosFornecedores, setDadosFornecedores] = useState<PrecoFornecedor[]>([]);
  const [mostrarResultados, setMostrarResultados] = useState(true); // ATIVAR PARA DEMO

  /**
   * PESQUISA NO PNCP
   */
  const executarPesquisaPNCP = async () => {
    if (!termoBusca.trim()) return;

    setLoading(true);
    try {
      console.log('🔍 Iniciando pesquisa PNCP:', termoBusca);

      const params: PNCPSearchParams = {
        termo: termoBusca,
        orgaoTipo: 'municipal',
        dataInicio: '2024-01-01',
        dataFim: new Date().toISOString().split('T')[0]
      };

      const resultado = await PNCPApi.buscarItens(params);

      console.log('✅ Resultado PNCP:', resultado);

      if (resultado.total === 0) {
        alert('⚠️ Nenhum resultado encontrado no PNCP para este termo. Verifique a descrição.');
      }

      setResultadoPNCP(resultado);
      setEtapa('pesquisa');
      setMostrarResultados(true);

    } catch (error) {
      console.error('❌ Erro na pesquisa PNCP:', error);
      alert('Erro ao consultar PNCP. Usando dados de fallback.');
    } finally {
      setLoading(false);
    }
  };

  // Dados reais serão carregados da API PNCP
  const resultadoMock: PNCPSearchResult = {
    itens: [],
    total: 0,
    media: 0,
    mediana: 0,
    desvio: 0,
    filtrosAplicados: []
  };

  /**
   * PROCESSAR ARQUIVO DE FORNECEDORES (IA)
   */
  const processarArquivoFornecedores = async (arquivo: File) => {
    setLoading(true);
    try {
      const texto = await arquivo.text();

      // IA real para extrair dados
      const dadosExtraidos = await extrairDadosComIA(texto);
      setDadosFornecedores(dadosExtraidos);

      alert(`✅ Arquivo processado! ${dadosExtraidos.length} preços extraídos.`);

    } catch (error) {
      console.error('Erro ao processar arquivo:', error);
      alert('Erro ao processar arquivo. Verifique o formato.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * IA REAL PARA EXTRAÇÃO DE DADOS - SEM SIMULAÇÃO
   */
  const extrairDadosComIA = async (texto: string): Promise<PrecoFornecedor[]> => {
    try {
      const response = await fetch('/api/ia/extrair-precos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ texto })
      });

      const data = await response.json();

      if (data.success) {
        return data.data.precos || [];
      }

      throw new Error(data.error || 'Erro na extração de dados');
    } catch (error) {
      console.error('Erro na IA de extração:', error);
      throw error;
    }
  };

  /**
   * APLICAR CRITÉRIO DECRETO 9337/24
   */
  const aplicarCriterioDecreto = () => {
    if (!resultadoPNCP) return;
    
    const itensComCriterio = PNCPApi.aplicarCriterioDecreto(resultadoPNCP.itens);
    setResultadoPNCP({
      ...resultadoPNCP,
      itens: itensComCriterio
    });
  };

  /**
   * GERAR MAPA DE PREÇOS COM ALERTAS DE UNIDADES
   */
  const gerarMapaPrecos = () => {
    if (!resultadoPNCP) return;

    // Verificar se há alertas de unidades
    const itensComAlertas = resultadoPNCP.itens.filter(item => item.alertaUnidade);

    if (itensComAlertas.length > 0) {
      const alertasTexto = itensComAlertas.map(item =>
        `• ${item.fornecedor}: ${item.alertaUnidade?.observacao}`
      ).join('\n');

      const confirmar = confirm(
        `⚠️ ATENÇÃO - UNIDADES DIFERENTES DETECTADAS:\n\n${alertasTexto}\n\nDeseja continuar com a geração do mapa?`
      );

      if (!confirmar) return;
    }

    setEtapa('finalizacao');
    // Aqui seria gerado o mapa de preços
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Pesquisa de Preços</h1>
          <p className="text-muted-foreground">
            Processo: {processoId} | Pesquisador: {pesquisador}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant={etapa === 'configuracao' ? 'default' : 'outline'}>
            1. Configuração
          </Badge>
          <Badge variant={etapa === 'pesquisa' ? 'default' : 'outline'}>
            2. Pesquisa
          </Badge>
          <Badge variant={etapa === 'analise' ? 'default' : 'outline'}>
            3. Análise
          </Badge>
          <Badge variant={etapa === 'finalizacao' ? 'default' : 'outline'}>
            4. Finalização
          </Badge>
        </div>
      </div>

      {/* ETAPA 1: CONFIGURAÇÃO */}
      {etapa === 'configuracao' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5" />
                Configurar Pesquisa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Tipo de Pesquisa
                </label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={tipoPesquisa}
                  onChange={(e) => setTipoPesquisa(e.target.value as 'simples' | 'combinada')}
                >
                  <option value="simples">Pesquisa Simples (PNCP)</option>
                  <option value="combinada">Pesquisa Combinada (Art. 23 LF 14133/21)</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Termo de Busca *
                </label>
                <Input
                  placeholder="Ex: álcool gel 500ml"
                  value={termoBusca}
                  onChange={(e) => setTermoBusca(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  💡 Seja específico: inclua marca, modelo, capacidade, etc.
                </p>
              </div>

              <Button 
                onClick={executarPesquisaPNCP}
                disabled={!termoBusca.trim() || loading}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Search className="mr-2 h-4 w-4 animate-spin" />
                    Pesquisando...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Iniciar Pesquisa PNCP
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-sm">
                  <Search className="mr-2 h-4 w-4" />
                  Busca Manual
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full" size="sm">
                  <Search className="mr-2 h-4 w-4" />
                  Inserir Itens
                </Button>
                <p className="text-xs text-muted-foreground">
                  • Inserir lotes manualmente<br/>
                  • Especificar itens e quantidades<br/>
                  • Para pesquisa no PNCP
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-sm">
                  <FileText className="mr-2 h-4 w-4" />
                  Buscar no TR
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Extrair do TR
                </Button>
                <p className="text-xs text-muted-foreground">
                  • Extrair lotes do TR aprovado<br/>
                  • Itens com especificações<br/>
                  • Para pesquisa no PNCP
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-sm">
                  <FileText className="mr-2 h-4 w-4" />
                  Buscar Thema
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Buscar Thema
                </Button>
                <p className="text-xs text-muted-foreground">
                  • Buscar lotes no Thema<br/>
                  • Itens do pedido de compra<br/>
                  • Para pesquisa no PNCP
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* ETAPA 2: RESULTADOS PESQUISA */}
      {(etapa === 'pesquisa' && resultadoPNCP) || mostrarResultados && (
        <div className="space-y-6">
          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{(resultadoPNCP || resultadoMock).total}</div>
                <div className="text-sm text-muted-foreground">Itens Encontrados</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">R$ {(resultadoPNCP || resultadoMock).media.toFixed(2)}</div>
                <div className="text-sm text-muted-foreground">Preço Médio</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">R$ {(resultadoPNCP || resultadoMock).mediana.toFixed(2)}</div>
                <div className="text-sm text-muted-foreground">Mediana</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">R$ {(resultadoPNCP || resultadoMock).desvio.toFixed(2)}</div>
                <div className="text-sm text-muted-foreground">Desvio Padrão</div>
              </CardContent>
            </Card>
          </div>

          {/* Ações */}
          <div className="flex items-center space-x-4">
            {tipoPesquisa === 'combinada' && (
              <Button variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Ampliar Pesquisa
              </Button>
            )}
            
            <Button variant="outline" onClick={aplicarCriterioDecreto}>
              <Filter className="mr-2 h-4 w-4" />
              Aplicar Decreto 9337/24
            </Button>
            
            <Button onClick={gerarMapaPrecos}>
              <FileText className="mr-2 h-4 w-4" />
              Gerar Mapa de Preços
            </Button>
          </div>

          {/* Lista de Itens */}
          <Card>
            <CardHeader>
              <CardTitle>Resultados da Pesquisa</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(resultadoPNCP || resultadoMock).itens.map((item) => (
                  <div 
                    key={item.id} 
                    className={`p-4 border rounded-lg ${item.excluido ? 'bg-red-50 border-red-200' : 'bg-white'}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.descricao}</h4>
                        <p className="text-sm text-muted-foreground">
                          {item.fornecedor} | {item.orgao}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="outline">R$ {item.preco.toFixed(2)}</Badge>
                          <Badge variant="secondary">Relevância: {item.relevancia}%</Badge>
                          {item.excluido && (
                            <Badge variant="destructive">{item.motivoExclusao}</Badge>
                          )}
                          {item.alertaUnidade && (
                            <Badge
                              variant={item.alertaUnidade.tipo === 'incompatibilidade' ? 'destructive' : 'warning'}
                              className="text-xs"
                            >
                              {item.alertaUnidade.tipo === 'conversao' ? '🔄' : '🚫'} {item.alertaUnidade.observacao}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {item.excluido ? (
                          <XCircle className="h-5 w-5 text-red-500" />
                        ) : (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* ETAPA 4: FINALIZAÇÃO */}
      {etapa === 'finalizacao' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Mapa de Preços</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Mapa de preços será gerado aqui</p>
                <Button className="mt-4">
                  <Download className="mr-2 h-4 w-4" />
                  Baixar Mapa
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Despacho</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Aguardando upload do despacho padrão
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  {tipoPesquisa === 'simples' ? 'Despacho para pesquisa simples' : 'Despacho para pesquisa combinada'}
                </p>
                <Button variant="outline" className="mt-4">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Despacho
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
