'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Users,
  Clock,
  DollarSign,
  Download,
  RefreshCw,
  AlertTriangle,
  Trophy,
  Target
} from 'lucide-react';

export default function RelatorioSecretariasPage() {
  const [dados, setDados] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [periodo, setPeriodo] = useState('30');

  useEffect(() => {
    carregarDados();
  }, [periodo]);

  const carregarDados = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/relatorios/secretarias?periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success) {
        setDados(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportarRelatorio = async (formato: 'pdf' | 'excel') => {
    try {
      const response = await fetch(`/api/relatorios/secretarias?formato=${formato}&periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success && data.downloadUrl) {
        window.open(data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Erro ao exportar:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!dados) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12 text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Erro ao carregar dados</h3>
            <p className="text-muted-foreground mb-4">
              Não foi possível carregar os dados do relatório.
            </p>
            <Button onClick={carregarDados}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Relatório por Secretarias</h1>
          <p className="text-muted-foreground mt-2">
            Análise de desempenho e volume por secretaria - {dados.periodo}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={periodo}
            onChange={(e) => setPeriodo(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
          >
            <option value="7">Últimos 7 dias</option>
            <option value="30">Últimos 30 dias</option>
            <option value="90">Últimos 90 dias</option>
            <option value="365">Último ano</option>
          </select>

          <Button variant="outline" onClick={() => exportarRelatorio('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>

          <Button variant="outline" onClick={() => exportarRelatorio('excel')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>

          <Button onClick={carregarDados}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Resumo Geral */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total de Secretarias</p>
                <p className="text-3xl font-bold">{dados.resumo.totalSecretarias}</p>
              </div>
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Melhor Desempenho</p>
                <p className="text-lg font-bold text-green-600">{dados.resumo.melhorDesempenho}</p>
              </div>
              <Trophy className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Eficiência Média</p>
                <p className="text-3xl font-bold text-purple-600">{dados.resumo.eficienciaMedia.toFixed(1)}%</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="text-2xl font-bold text-orange-600">
                  R$ {(dados.resumo.valorTotalGeral / 1000000).toFixed(1)}M
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Ranking de Secretarias */}
      <Card>
        <CardHeader>
          <CardTitle>Ranking de Desempenho</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dados.secretarias.slice(0, 10).map((secretaria: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-accent/50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium">{secretaria.nome}</h4>
                    <p className="text-sm text-muted-foreground">
                      {secretaria.totalProcessos} processos • {secretaria.processosFinalizados} finalizados
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold">{secretaria.eficiencia.toFixed(1)}%</p>
                  <p className="text-sm text-muted-foreground">{secretaria.tempoMedio} dias médio</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Comparativos */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Trophy className="mr-2 h-5 w-5 text-green-600" />
              Mais Eficientes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dados.comparativo.maisEficientes.map((secretaria: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="font-medium">{secretaria.nome}</span>
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    {secretaria.eficiencia.toFixed(1)}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5 text-red-600" />
              Precisam Atenção
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dados.comparativo.menosEficientes.map((secretaria: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="font-medium">{secretaria.nome}</span>
                  <Badge variant="destructive">
                    {secretaria.eficiencia.toFixed(1)}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5 text-blue-600" />
              Maior Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dados.comparativo.maiorVolume.map((secretaria: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="font-medium">{secretaria.nome}</span>
                  <Badge variant="outline">
                    {secretaria.totalProcessos} processos
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distribuição por Modalidade */}
      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Modalidade</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(dados.distribuicaoModalidades).map(([modalidade, quantidade]: [string, any]) => (
              <div key={modalidade} className="text-center p-4 border border-border rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{quantidade}</p>
                <p className="text-sm text-muted-foreground">{modalidade}</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${(quantidade / Object.values(dados.distribuicaoModalidades).reduce((a: number, b: any) => a + b, 0)) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detalhamento Completo */}
      <Card>
        <CardHeader>
          <CardTitle>Detalhamento por Secretaria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Secretaria</th>
                  <th className="text-center p-2">Total</th>
                  <th className="text-center p-2">Finalizados</th>
                  <th className="text-center p-2">Em Andamento</th>
                  <th className="text-center p-2">Eficiência</th>
                  <th className="text-center p-2">Tempo Médio</th>
                  <th className="text-right p-2">Valor Total</th>
                </tr>
              </thead>
              <tbody>
                {dados.secretarias.map((secretaria: any, index: number) => (
                  <tr key={index} className="border-b hover:bg-accent/50">
                    <td className="p-2 font-medium">{secretaria.nome}</td>
                    <td className="text-center p-2">{secretaria.totalProcessos}</td>
                    <td className="text-center p-2 text-green-600">{secretaria.processosFinalizados}</td>
                    <td className="text-center p-2 text-orange-600">{secretaria.processosEmAndamento}</td>
                    <td className="text-center p-2">
                      <Badge variant={secretaria.eficiencia >= 80 ? 'default' : 'secondary'}>
                        {secretaria.eficiencia.toFixed(1)}%
                      </Badge>
                    </td>
                    <td className="text-center p-2">{secretaria.tempoMedio} dias</td>
                    <td className="text-right p-2">
                      R$ {(secretaria.valorTotal / 1000).toFixed(0)}k
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
