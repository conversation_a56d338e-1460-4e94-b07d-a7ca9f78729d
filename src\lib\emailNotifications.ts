/**
 * 📧 SISTEMA DE NOTIFICAÇÕES POR EMAIL
 * Notifica administradores sobre usuários pendentes e outras ações importantes
 */

import { db } from './firebase';
import { collection, query, where, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';

// Configuração de emails dos administradores
const ADMIN_EMAILS = [
  '<EMAIL>',      // Email principal
  '<EMAIL>'   // Email de backup
];

// Configuração do serviço de email (usando EmailJS como exemplo)
const EMAILJS_CONFIG = {
  serviceId: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'default_service',
  templateId: process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'inovaprocess_notifications',
  userId: process.env.NEXT_PUBLIC_EMAILJS_USER_ID || 'default_user'
};

interface UsuarioPendente {
  uid: string;
  email: string;
  nome: string;
  dataCadastro: any;
}

/**
 * Verifica se há usuários pendentes e notifica os admins
 */
export const verificarEnotificarUsuariosPendentes = async () => {
  try {
    console.log('🔍 Verificando usuários pendentes...');
    
    // Buscar usuários pendentes
    const usuariosRef = collection(db, 'usuarios');
    const q = query(usuariosRef, where('status', '==', 'pendente'));
    const snapshot = await getDocs(q);
    
    const usuariosPendentes: UsuarioPendente[] = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      usuariosPendentes.push({
        uid: doc.id,
        email: data.email,
        nome: data.nome,
        dataCadastro: data.dataCadastro
      });
    });

    if (usuariosPendentes.length > 0) {
      console.log(`📧 Encontrados ${usuariosPendentes.length} usuários pendentes. Enviando notificação...`);
      
      // Enviar email para todos os admins
      for (const adminEmail of ADMIN_EMAILS) {
        await enviarEmailUsuariosPendentes(adminEmail, usuariosPendentes);
      }
      
      // Registrar notificação enviada
      await registrarNotificacaoEnviada(usuariosPendentes.length);
      
      console.log('✅ Notificação enviada com sucesso');
    } else {
      console.log('✅ Nenhum usuário pendente encontrado');
    }
    
    return usuariosPendentes.length;
  } catch (error) {
    console.error('❌ Erro ao verificar usuários pendentes:', error);
    throw error;
  }
};

/**
 * Envia email sobre usuários pendentes
 */
const enviarEmailUsuariosPendentes = async (adminEmail: string, usuarios: UsuarioPendente[]) => {
  try {
    // Usando EmailJS (você pode trocar por outro serviço)
    if (typeof window !== 'undefined' && (window as any).emailjs) {
      const templateParams = {
        to_email: adminEmail,
        subject: `InovaProcess - ${usuarios.length} usuário(s) pendente(s) de aprovação`,
        message: gerarMensagemUsuariosPendentes(usuarios),
        admin_name: 'Administrador',
        pending_count: usuarios.length,
        system_url: process.env.NEXT_PUBLIC_SITE_URL || 'https://inovaprocess.app'
      };

      await (window as any).emailjs.send(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateId,
        templateParams,
        EMAILJS_CONFIG.userId
      );
    } else {
      // Fallback: usar console.log para desenvolvimento
      console.log('📧 EMAIL (DEV MODE):', {
        to: adminEmail,
        subject: `InovaProcess - ${usuarios.length} usuário(s) pendente(s)`,
        message: gerarMensagemUsuariosPendentes(usuarios)
      });
    }
  } catch (error) {
    console.error('❌ Erro ao enviar email:', error);
  }
};

/**
 * Gera mensagem do email
 */
const gerarMensagemUsuariosPendentes = (usuarios: UsuarioPendente[]): string => {
  const formatarData = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleString('pt-BR');
    } catch {
      return 'N/A';
    }
  };

  let mensagem = `
    <h2>🚨 Usuários Pendentes de Aprovação</h2>
    <p>Existem <strong>${usuarios.length}</strong> usuário(s) aguardando aprovação no sistema InovaProcess.</p>
    
    <h3>📋 Lista de Usuários Pendentes:</h3>
    <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
      <thead>
        <tr style="background-color: #f3f4f6;">
          <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">Nome</th>
          <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">Email</th>
          <th style="border: 1px solid #d1d5db; padding: 8px; text-align: left;">Data Cadastro</th>
        </tr>
      </thead>
      <tbody>
  `;

  usuarios.forEach(usuario => {
    mensagem += `
        <tr>
          <td style="border: 1px solid #d1d5db; padding: 8px;">${usuario.nome}</td>
          <td style="border: 1px solid #d1d5db; padding: 8px;">${usuario.email}</td>
          <td style="border: 1px solid #d1d5db; padding: 8px;">${formatarData(usuario.dataCadastro)}</td>
        </tr>
    `;
  });

  mensagem += `
      </tbody>
    </table>
    
    <p><strong>🔗 Acesse o painel de administração:</strong></p>
    <p><a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://inovaprocess.app'}/admin/usuarios" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Gerenciar Usuários</a></p>
    
    <p style="margin-top: 20px; color: #6b7280; font-size: 14px;">
      Esta notificação foi enviada automaticamente pelo sistema InovaProcess.
    </p>
  `;

  return mensagem;
};

/**
 * Registra que a notificação foi enviada
 */
const registrarNotificacaoEnviada = async (quantidadeUsuarios: number) => {
  try {
    const notificacaoRef = doc(db, 'configuracoes', 'notificacoes');
    await updateDoc(notificacaoRef, {
      ultimaNotificacaoPendentes: serverTimestamp(),
      quantidadeUsuariosPendentes: quantidadeUsuarios,
      notificacoesEnviadas: serverTimestamp()
    });
  } catch (error) {
    console.error('❌ Erro ao registrar notificação:', error);
  }
};

/**
 * Envia email de aprovação para o usuário
 */
export const enviarEmailAprovacao = async (userEmail: string, userName: string, aprovadoPor: string) => {
  try {
    const mensagem = `
      <h2>✅ Sua conta foi aprovada!</h2>
      <p>Olá <strong>${userName}</strong>,</p>
      <p>Sua conta no sistema InovaProcess foi aprovada com sucesso!</p>
      
      <p><strong>🔗 Acesse o sistema:</strong></p>
      <p><a href="${process.env.NEXT_PUBLIC_SITE_URL || 'https://inovaprocess.app'}" style="background-color: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Acessar Sistema</a></p>
      
      <p style="margin-top: 20px; color: #6b7280; font-size: 14px;">
        Aprovado por: ${aprovadoPor}<br>
        Data: ${new Date().toLocaleString('pt-BR')}
      </p>
    `;

    // Enviar email (implementar com seu serviço preferido)
    console.log('📧 EMAIL APROVAÇÃO:', { to: userEmail, message: mensagem });
  } catch (error) {
    console.error('❌ Erro ao enviar email de aprovação:', error);
  }
};

/**
 * Envia email de reprovação para o usuário
 */
export const enviarEmailReprovacao = async (userEmail: string, userName: string, motivo?: string) => {
  try {
    const mensagem = `
      <h2>❌ Acesso não aprovado</h2>
      <p>Olá <strong>${userName}</strong>,</p>
      <p>Infelizmente sua solicitação de acesso ao sistema InovaProcess não foi aprovada.</p>
      
      ${motivo ? `<p><strong>Motivo:</strong> ${motivo}</p>` : ''}
      
      <p>Se acredita que isso foi um erro, entre em contato com o administrador do sistema.</p>
      
      <p style="margin-top: 20px; color: #6b7280; font-size: 14px;">
        Data: ${new Date().toLocaleString('pt-BR')}
      </p>
    `;

    // Enviar email (implementar com seu serviço preferido)
    console.log('📧 EMAIL REPROVAÇÃO:', { to: userEmail, message: mensagem });
  } catch (error) {
    console.error('❌ Erro ao enviar email de reprovação:', error);
  }
}; 