import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';
import { SolicitacaoAcesso, UsuarioSistema, NivelAcesso } from '@/types/organizacao';

// Coleções Firestore
const COLECAO_SOLICITACOES = 'solicitacoes_acesso';
const COLECAO_USUARIOS = 'usuarios_sistema';

/**
 * Criar nova solicitação de acesso
 */
export async function criarSolicitacaoAcesso(solicitacao: Omit<SolicitacaoAcesso, 'id' | 'data_solicitacao' | 'status'>): Promise<string> {
  try {
    // Verificar se já existe solicitação pendente para este email
    const solicitacaoExistente = await verificarSolicitacaoPendente(solicitacao.email);
    if (solicitacaoExistente) {
      throw new Error('Já existe uma solicitação pendente para este email');
    }

    const novaSolicitacao: Omit<SolicitacaoAcesso, 'id'> = {
      ...solicitacao,
      status: 'pendente',
      data_solicitacao: new Date()
    };

    const docRef = await addDoc(collection(db, COLECAO_SOLICITACOES), {
      ...novaSolicitacao,
      data_solicitacao: serverTimestamp()
    });

    console.log('Solicitação criada com ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Erro ao criar solicitação:', error);
    throw error;
  }
}

/**
 * Verificar se existe solicitação pendente para um email
 */
export async function verificarSolicitacaoPendente(email: string): Promise<boolean> {
  try {
    const q = query(
      collection(db, COLECAO_SOLICITACOES),
      where('email', '==', email),
      where('status', '==', 'pendente')
    );
    
    const snapshot = await getDocs(q);
    return !snapshot.empty;
  } catch (error) {
    console.error('Erro ao verificar solicitação pendente:', error);
    return false;
  }
}

/**
 * Buscar todas as solicitações pendentes
 */
export async function buscarSolicitacoesPendentes(): Promise<SolicitacaoAcesso[]> {
  try {
    const q = query(
      collection(db, COLECAO_SOLICITACOES),
      where('status', '==', 'pendente'),
      orderBy('data_solicitacao', 'desc')
    );
    
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      data_solicitacao: doc.data().data_solicitacao?.toDate() || new Date(),
      data_resposta: doc.data().data_resposta?.toDate()
    })) as SolicitacaoAcesso[];
  } catch (error) {
    console.error('Erro ao buscar solicitações pendentes:', error);
    return [];
  }
}

/**
 * Aprovar solicitação de acesso
 */
export async function aprovarSolicitacao(
  solicitacaoId: string, 
  aprovadoPor: string, 
  nivelAcesso?: NivelAcesso,
  observacoes?: string
): Promise<void> {
  try {
    // Buscar dados da solicitação
    const solicitacaoDoc = await getDoc(doc(db, COLECAO_SOLICITACOES, solicitacaoId));
    if (!solicitacaoDoc.exists()) {
      throw new Error('Solicitação não encontrada');
    }

    const solicitacao = solicitacaoDoc.data() as SolicitacaoAcesso;

    // Atualizar status da solicitação
    await updateDoc(doc(db, COLECAO_SOLICITACOES, solicitacaoId), {
      status: 'aprovado',
      data_resposta: serverTimestamp(),
      aprovado_por: aprovadoPor,
      observacoes_admin: observacoes || ''
    });

    // Criar usuário no sistema
    const novoUsuario: Omit<UsuarioSistema, 'uid'> = {
      email: solicitacao.email,
      nome: solicitacao.nome,
      organizacao: solicitacao.organizacao,
      nivel_acesso: nivelAcesso || solicitacao.nivel_acesso_solicitado,
      ativo: true,
      data_aprovacao: new Date(),
      aprovado_por: aprovadoPor,
      configuracoes: {
        notificacoes_email: true,
        tema: 'auto',
        idioma: 'pt-BR'
      }
    };

    // Usar email como ID do documento para facilitar consultas
    const emailId = solicitacao.email.replace(/[.@]/g, '_');
    await updateDoc(doc(db, COLECAO_USUARIOS, emailId), {
      ...novoUsuario,
      uid: emailId,
      data_aprovacao: serverTimestamp()
    });

    console.log('Solicitação aprovada e usuário criado');
  } catch (error) {
    console.error('Erro ao aprovar solicitação:', error);
    throw error;
  }
}

/**
 * Reprovar solicitação de acesso
 */
export async function reprovarSolicitacao(
  solicitacaoId: string, 
  reprovadoPor: string, 
  motivo: string
): Promise<void> {
  try {
    await updateDoc(doc(db, COLECAO_SOLICITACOES, solicitacaoId), {
      status: 'reprovado',
      data_resposta: serverTimestamp(),
      aprovado_por: reprovadoPor,
      observacoes_admin: motivo
    });

    console.log('Solicitação reprovada');
  } catch (error) {
    console.error('Erro ao reprovar solicitação:', error);
    throw error;
  }
}

/**
 * Buscar usuário do sistema por email
 */
export async function buscarUsuarioPorEmail(email: string): Promise<UsuarioSistema | null> {
  try {
    const emailId = email.replace(/[.@]/g, '_');
    const userDoc = await getDoc(doc(db, COLECAO_USUARIOS, emailId));
    
    if (!userDoc.exists()) {
      return null;
    }

    const userData = userDoc.data();
    return {
      ...userData,
      data_aprovacao: userData.data_aprovacao?.toDate() || new Date(),
      ultimo_acesso: userData.ultimo_acesso?.toDate()
    } as UsuarioSistema;
  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    return null;
  }
}

/**
 * Atualizar último acesso do usuário
 */
export async function atualizarUltimoAcesso(email: string): Promise<void> {
  try {
    const emailId = email.replace(/[.@]/g, '_');
    await updateDoc(doc(db, COLECAO_USUARIOS, emailId), {
      ultimo_acesso: serverTimestamp()
    });
  } catch (error) {
    console.error('Erro ao atualizar último acesso:', error);
  }
}

/**
 * Listener para solicitações pendentes (tempo real)
 */
export function ouvirSolicitacoesPendentes(callback: (solicitacoes: SolicitacaoAcesso[]) => void) {
  const q = query(
    collection(db, COLECAO_SOLICITACOES),
    where('status', '==', 'pendente'),
    orderBy('data_solicitacao', 'desc')
  );

  return onSnapshot(q, (snapshot) => {
    const solicitacoes = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      data_solicitacao: doc.data().data_solicitacao?.toDate() || new Date(),
      data_resposta: doc.data().data_resposta?.toDate()
    })) as SolicitacaoAcesso[];
    
    callback(solicitacoes);
  });
}

/**
 * Buscar todos os usuários do sistema
 */
export async function buscarTodosUsuarios(): Promise<UsuarioSistema[]> {
  try {
    const snapshot = await getDocs(collection(db, COLECAO_USUARIOS));
    return snapshot.docs.map(doc => ({
      ...doc.data(),
      data_aprovacao: doc.data().data_aprovacao?.toDate() || new Date(),
      ultimo_acesso: doc.data().ultimo_acesso?.toDate()
    })) as UsuarioSistema[];
  } catch (error) {
    console.error('Erro ao buscar usuários:', error);
    return [];
  }
}
