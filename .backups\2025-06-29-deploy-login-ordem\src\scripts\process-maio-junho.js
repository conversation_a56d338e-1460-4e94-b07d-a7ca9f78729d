const fs = require('fs');
const path = require('path');

console.log('🚀 INICIANDO PROCESSAMENTO DOS CSVs DE MAIO E JUNHO...');

// Função para processar um arquivo CSV
function processCSV(filePath, outputPath) {
  console.log(`📄 Processando: ${filePath}`);

  try {
    // Ler arquivo
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');

    if (lines.length < 2) {
      console.log(`⚠️ Arquivo vazio ou sem dados: ${filePath}`);
      return 0;
    }

    // Header
    const header = lines[0];

    // Processar linhas de dados
    const processedLines = [header];
    let count = 0;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        // Aplicar correções básicas se necessário
        processedLines.push(line);
        count++;
      }
    }

    // Salvar arquivo processado
    fs.writeFileSync(outputPath, processedLines.join('\n'), 'utf8');
    console.log(`✅ Processados ${count} registros de ${path.basename(filePath)}`);
    console.log(`💾 Salvo: ${outputPath}`);

    return count;
  } catch (error) {
    console.error(`❌ Erro processando ${filePath}:`, error.message);
    return 0;
  }
}

// Função principal
function main() {
  try {
    const dataDir = path.join(__dirname, '../../data');

    // Processar arquivos de maio
    console.log('\n📅 PROCESSANDO MAIO 2025...');
    const maioDir = path.join(dataDir, 'databackup-maio-v1');
    const maioFiles = fs.readdirSync(maioDir).filter(file => file.endsWith('.csv') && !file.includes('processado'));

    let totalMaio = 0;
    for (const file of maioFiles) {
      const inputPath = path.join(maioDir, file);
      const outputPath = path.join(maioDir, file.replace('.csv', '-processado.csv'));
      const count = processCSV(inputPath, outputPath);
      totalMaio += count;
    }

    // Processar arquivos de junho
    console.log('\n📅 PROCESSANDO JUNHO 2025...');
    const junhoDir = path.join(dataDir, 'databackup-junho-v1');
    const junhoFiles = fs.readdirSync(junhoDir).filter(file => file.endsWith('.csv') && !file.includes('processado'));

    let totalJunho = 0;
    for (const file of junhoFiles) {
      const inputPath = path.join(junhoDir, file);
      const outputPath = path.join(junhoDir, file.replace('.csv', '-processado.csv'));
      const count = processCSV(inputPath, outputPath);
      totalJunho += count;
    }

    // Processar arquivos de 29 e 30 de abril
    console.log('\n📅 PROCESSANDO 29-30 ABRIL 2025...');
    const abrilFinalDir = path.join(dataDir, 'databackup-29 e 30-04');
    const abrilFinalFiles = fs.readdirSync(abrilFinalDir).filter(file => file.endsWith('.csv') && !file.includes('processado'));

    let totalAbrilFinal = 0;
    for (const file of abrilFinalFiles) {
      const inputPath = path.join(abrilFinalDir, file);
      const outputPath = path.join(abrilFinalDir, file.replace('.csv', '-processado.csv'));
      const count = processCSV(inputPath, outputPath);
      totalAbrilFinal += count;
    }

    console.log('\n🎉 PROCESSAMENTO CONCLUÍDO!');
    console.log(`📊 RESUMO:`);
    console.log(`   📅 Maio: ${totalMaio} registros processados`);
    console.log(`   📅 Junho: ${totalJunho} registros processados`);
    console.log(`   📅 Abril Final: ${totalAbrilFinal} registros processados`);
    console.log(`   🎯 Total: ${totalMaio + totalJunho + totalAbrilFinal} registros`);

  } catch (error) {
    console.error('❌ Erro no processamento:', error);
    process.exit(1);
  }
}

main();
