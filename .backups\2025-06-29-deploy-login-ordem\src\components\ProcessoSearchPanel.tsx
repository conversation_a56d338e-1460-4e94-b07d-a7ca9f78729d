'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Hash, Building2, User, Calendar } from 'lucide-react';

interface ProcessoSearchPanelProps {
  onSearch: (filters: any) => void;
  totalProcessos: number;
}

export default function ProcessoSearchPanel({ onSearch, totalProcessos }: ProcessoSearchPanelProps) {
  const [activeFilter, setActiveFilter] = useState<string>('todos');
  const [searchTerm, setSearchTerm] = useState('');

  const filterButtons = [
    { id: 'todos', label: 'Todos', icon: Filter, count: totalProcessos },
    { id: 'numero', label: 'Por Número', icon: Hash },
    { id: 'secretaria', label: 'Por Secretaria', icon: Building2 },
    { id: 'responsavel', label: 'Por Responsável', icon: User },
    { id: 'status', label: 'Por Status', icon: Calendar },
  ];

  const handleFilterClick = (filterId: string) => {
    setActiveFilter(filterId);
    setSearchTerm('');
    
    if (filterId === 'todos') {
      onSearch({});
    }
  };

  const handleSearch = () => {
    if (!searchTerm.trim()) return;

    const filters: any = { search: searchTerm };
    
    switch (activeFilter) {
      case 'numero':
        filters.numeroProcesso = searchTerm;
        break;
      case 'secretaria':
        filters.requisitante = searchTerm;
        break;
      case 'responsavel':
        filters.responsavel = searchTerm;
        break;
      case 'status':
        filters.status = searchTerm;
        break;
      default:
        break;
    }

    onSearch(filters);
  };

  const getPlaceholder = () => {
    switch (activeFilter) {
      case 'numero':
        return 'Digite o número do processo (ex: 9078/2024)';
      case 'secretaria':
        return 'Digite o nome ou sigla da secretaria (ex: SSDAN)';
      case 'responsavel':
        return 'Digite o nome do responsável';
      case 'status':
        return 'Digite o status do processo';
      default:
        return 'Buscar em todos os campos...';
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        {/* Botões de filtro */}
        <div className="flex flex-wrap gap-2 mb-4">
          {filterButtons.map((filter) => {
            const Icon = filter.icon;
            return (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleFilterClick(filter.id)}
                className="flex items-center gap-2"
              >
                <Icon className="h-4 w-4" />
                {filter.label}
                {filter.count && (
                  <Badge variant="secondary" className="ml-1">
                    {filter.count}
                  </Badge>
                )}
              </Button>
            );
          })}
        </div>

        {/* Campo de busca */}
        {activeFilter !== 'todos' && (
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={getPlaceholder()}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} disabled={!searchTerm.trim()}>
              <Search className="h-4 w-4 mr-2" />
              Buscar
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm('');
                setActiveFilter('todos');
                onSearch({});
              }}
            >
              Limpar
            </Button>
          </div>
        )}

        {/* Dicas de busca */}
        <div className="mt-4 text-sm text-muted-foreground">
          <p className="font-medium mb-1">💡 Dicas de busca:</p>
          <ul className="text-xs space-y-1">
            <li>• <strong>Número:</strong> Digite apenas números (ex: 9078)</li>
            <li>• <strong>Secretaria:</strong> Use siglas (SSDAN, SEMAM) ou nomes completos</li>
            <li>• <strong>Status:</strong> Finalizado, Encaminhado, Para publicação, etc.</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
