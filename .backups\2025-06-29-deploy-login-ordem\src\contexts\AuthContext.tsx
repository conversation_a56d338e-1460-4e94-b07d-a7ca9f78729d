'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from 'firebase/auth';
import { useAuthState, signInWithGoogle, logoutUser, isValidGmailUser, initializeFirestoreCollections } from '@/lib/firebase';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, getDocs, collection, serverTimestamp, updateDoc } from 'firebase/firestore';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  error: string | null;
  clearError: () => void;
  userProfile: any | null;
  updateUserProfile: (data: any) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [userStatus, setUserStatus] = useState<'pendente' | 'aprovado' | 'reprovado' | null>(null);

  // Lista de emails confiáveis para aprovação automática
  const EMAILS_CONFIAVEIS = [
    '<EMAIL>',      // Email principal
    '<EMAIL>'   // Email de backup
  ];

  useEffect(() => {
    const unsubscribe = useAuthState(async (firebaseUser) => {
      setLoading(true);
      if (firebaseUser && !isValidGmailUser(firebaseUser)) {
        logoutUser();
        setUser(null);
        setUserProfile(null);
        setUserStatus(null);
        setError('Apenas contas Gmail são permitidas');
        setLoading(false);
        return;
      }
      setUser(firebaseUser);
      if (firebaseUser) {
        await verificarOuCriarUsuario(firebaseUser);
      } else {
        setUserProfile(null);
        setUserStatus(null);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  // Função para verificar/criar usuário e definir status
  const verificarOuCriarUsuario = async (firebaseUser: User) => {
    try {
      const usersRef = collection(db, 'usuarios');
      const userDocRef = doc(db, 'usuarios', firebaseUser.uid);
      const userDocSnap = await getDoc(userDocRef);
      const allUsersSnap = await getDocs(usersRef);
      let status: 'pendente' | 'aprovado' | 'reprovado' = 'pendente';
      let perfil: string = 'usuario';

      // Primeiro usuário do sistema vira admin aprovado automaticamente
      if (allUsersSnap.empty) {
        status = 'aprovado';
        perfil = 'admin';
        await setDoc(userDocRef, {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          nome: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'Usuário',
          status,
          perfil,
          dataCadastro: serverTimestamp(),
          dataAprovacao: serverTimestamp(),
          aprovadoPor: 'sistema',
          observacoes: 'Primeiro usuário - admin automático'
        });
      } else if (EMAILS_CONFIAVEIS.includes(firebaseUser.email || '')) {
        // Email confiável aprovado automaticamente
        status = 'aprovado';
        perfil = 'admin';
        await setDoc(userDocRef, {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          nome: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'Usuário',
          status,
          perfil,
          dataCadastro: serverTimestamp(),
          dataAprovacao: serverTimestamp(),
          aprovadoPor: 'sistema',
          observacoes: 'Email confiável - admin automático'
        }, { merge: true });
      } else if (!userDocSnap.exists()) {
        // Novo usuário pendente
        await setDoc(userDocRef, {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          nome: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'Usuário',
          status: 'pendente',
          perfil: 'usuario',
          dataCadastro: serverTimestamp()
        });
      } else {
        // Usuário já existe, pegar status e perfil
        const data = userDocSnap.data();
        status = data.status || 'pendente';
        perfil = data.perfil || 'usuario';
      }
      setUserStatus(status);
      setUserProfile({
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        nome: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'Usuário',
        perfil,
        status
      });
    } catch (err) {
      setError('Erro ao verificar status do usuário');
      setUserStatus(null);
      setUserProfile(null);
    }
  };

  // Redirecionamento automático conforme status
  useEffect(() => {
    if (!loading && user) {
      if (userStatus === 'pendente') {
        window.location.href = '/aguardando-aprovacao';
      } else if (userStatus === 'reprovado') {
        window.location.href = '/acesso-negado';
      }
      // Se aprovado, segue fluxo normal
    }
  }, [user, userStatus, loading]);

  const login = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Iniciando processo de login...');
      const user = await signInWithGoogle();
      
      if (!isValidGmailUser(user)) {
        await logoutUser();
        throw new Error('Apenas contas Gmail são permitidas');
      }
      
      setUser(user);
      console.log('✅ Login realizado com sucesso:', user.email);
    } catch (error: any) {
      console.error('❌ Erro no login:', error);
      setError(error.message || 'Erro desconhecido no login');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Iniciando logout...');
      await logoutUser();
      setUser(null);
      setUserProfile(null);
      console.log('✅ Logout realizado com sucesso');
    } catch (error: any) {
      console.error('❌ Erro no logout:', error);
      setError(error.message || 'Erro desconhecido no logout');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const updateUserProfile = async (data: any) => {
    try {
      if (!user) {
        throw new Error('Usuário não autenticado');
      }
      
      // Aqui você pode atualizar o perfil no Firestore
      const updatedProfile = { ...userProfile, ...data };
      setUserProfile(updatedProfile);
      
      console.log('✅ Perfil atualizado:', updatedProfile);
    } catch (error: any) {
      console.error('❌ Erro ao atualizar perfil:', error);
      setError(error.message || 'Erro ao atualizar perfil');
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user && userStatus === 'aprovado',
    error,
    clearError,
    userProfile,
    updateUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
