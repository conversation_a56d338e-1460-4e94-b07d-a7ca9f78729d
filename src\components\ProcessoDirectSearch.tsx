'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Removido import do Select - usando input com datalist
import { Badge } from '@/components/ui/badge';
import { Search, Hash, FileText, Building2, DollarSign, User, Filter } from 'lucide-react';

interface ProcessoDirectSearchProps {
  onSearch: (filters: any) => void;
  onShowAll: () => void;
  totalProcessos: number;
}

interface FilterOptions {
  responsaveis: string[];
  requisitantes: string[];
  fontes: string[];
  modalidades: string[];
}

export default function ProcessoDirectSearch({ onSearch, onShowAll, totalProcessos }: ProcessoDirectSearchProps) {
  const [filters, setFilters] = useState({
    numero: '',
    objeto: '',
    secretaria: '',
    fonte: '',
    modalidade: '',
    responsavel: ''
  });

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    responsaveis: [
      'Angela',
      '<PERSON>',
      'Arina',
      'Carla',
      'Gilson',
      'Maíra',
      'Marcos',
      'Maria',
      'Mariane',
      'Nice',
      'Nilva',
      'Rodrigo',
      'Sara',
      'Sônia',
      'Valter',
      'Vanessa'
    ],
    requisitantes: [
      'SAS',
      'SAJ',
      'SECOM',
      'CGM',
      'SC',
      'SDE',
      'SEL',
      'SE',
      'SF',
      'GP',
      'SG',
      'SH',
      'SMA',
      'SMU',
      'SO',
      'SPU',
      'SPPM',
      'SPDC',
      'SPDPD',
      'SRI',
      'SS',
      'SSAN',
      'SSP',
      'SSU',
      'STRE'
    ],
    fontes: [
      'Fonte 0001 (TESOURO)',
      'Fonte 0002 (ESTADUAL)',
      'Fonte 0003 (FUNDO)',
      'Fonte 0005 (FEDERAL)',
      'Fonte 0007 (FINISA)'
    ],
    modalidades: [
      'Pregão Eletrônico',
      'Pregão Presencial',
      'Concorrência',
      'Dispensa',
      'Inexigibilidade',
      'Chamamento Público'
    ]
  });

  const [loading, setLoading] = useState(false);

  // Carregar opções para dropdowns - DESATIVADO TEMPORARIAMENTE
  /*
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const response = await fetch('/api/processos/stats');
        const data = await response.json();

        if (data.success) {
          setFilterOptions({
            responsaveis: (data.data.filtros.responsaveis || []).sort(),
            requisitantes: (data.data.filtros.requisitantes || []).sort(),
            fontes: (data.data.filtros.fontes || []).sort()
          });
        }
      } catch (error) {
        console.error('Erro ao carregar opções de filtro:', error);
      }
    };

    loadFilterOptions();
  }, []);
  */

  // Usar fontes reais da API ou fallback
  const fontesRecursos = filterOptions.fontes.length > 0
    ? filterOptions.fontes.map(fonte => ({ value: fonte, label: fonte }))
    : [
        { value: 'Fonte 0001 (TESOURO)', label: 'Fonte 0001 (TESOURO)' },
        { value: 'Fonte 0002 (ESTADUAL)', label: 'Fonte 0002 (ESTADUAL)' },
        { value: 'Fonte 0003 (FUNDO)', label: 'Fonte 0003 (FUNDO)' },
        { value: 'Fonte 0005 (FEDERAL)', label: 'Fonte 0005 (FEDERAL)' },
        { value: 'Fonte 0007 (FINISA)', label: 'Fonte 0007 (FINISA)' }
      ];

  const handleFilterChange = (field: string, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    // Busca instantânea apenas se houver algum valor preenchido
    const hasAnyFilter = Object.values(newFilters).some(v => v.trim() !== '');
    
    if (hasAnyFilter) {
      // Construir filtros para a API
      const searchFilters: any = {};

      if (newFilters.numero) {
        searchFilters.search = newFilters.numero;
      }
      if (newFilters.objeto) {
        searchFilters.search = newFilters.objeto;
      }
      if (newFilters.secretaria) {
        searchFilters.requisitante = newFilters.secretaria;
      }
      if (newFilters.responsavel) {
        searchFilters.responsavel = newFilters.responsavel;
      }
      if (newFilters.modalidade) {
        searchFilters.modalidade = newFilters.modalidade;
      }
      // CORREÇÃO: Adicionar filtro de fonte de recursos
      if (newFilters.fonte) {
        searchFilters.fonte = newFilters.fonte;
      }

      onSearch(searchFilters);
    }
  };

  const handleClearFilters = () => {
    setFilters({
      numero: '',
      objeto: '',
      secretaria: '',
      fonte: '',
      modalidade: '',
      responsavel: ''
    });
    // Limpar a página também
    onSearch({});
  };

  const handleShowAll = () => {
    handleClearFilters();
    onShowAll();
  };

  return (
    <Card>
      <CardContent className="p-6">
        {/* Botão Todos e contador */}
        <div className="flex items-center justify-between mb-6">
          <Button
            size="sm"
            onClick={handleClearFilters}
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            Limpar Filtros
          </Button>

          <span className="text-white font-bold text-lg">Busca Específica</span>

          <Button
            onClick={handleShowAll}
            className="flex items-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            <Filter className="h-4 w-4" />
            Todos
            <Badge variant="secondary" className="ml-1 border-blue-500 text-white bg-blue-500">
              {totalProcessos}
            </Badge>
          </Button>
        </div>

        {/* Campos de busca direta - Linha 1 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* Busca por Número */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Número (Processo/Certame/Contrato)
            </label>
            <Input
              placeholder="Ex: 9078, 006/2025, 013/2025"
              value={filters.numero}
              onChange={(e) => handleFilterChange('numero', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Busca por Objeto */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Objeto
            </label>
            <Input
              placeholder="Ex: medicamentos, fraldas, material"
              value={filters.objeto}
              onChange={(e) => handleFilterChange('objeto', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Busca por Secretaria */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Secretaria
            </label>
            <Input
              placeholder="Ex: SS, SE, SG, SSAN, SSU"
              value={filters.secretaria}
              onChange={(e) => handleFilterChange('secretaria', e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        {/* Campos de busca direta - Linha 2 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Fonte de Recursos */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Fonte de Recursos
            </label>
            <div className="relative">
              <select
                value={filters.fonte}
                onChange={(e) => handleFilterChange('fonte', e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="">Todas as fontes</option>
                {fontesRecursos.map((fonte) => (
                  <option key={fonte.value} value={fonte.value}>
                    {fonte.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Modalidade */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Modalidade
            </label>
            <div className="relative">
              <select
                value={filters.modalidade}
                onChange={(e) => handleFilterChange('modalidade', e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="">Todas as modalidades</option>
                {(filterOptions.modalidades || []).map((modalidade) => (
                  <option key={modalidade} value={modalidade}>
                    {modalidade}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Responsável */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Responsável
            </label>
            <div className="relative">
              <select
                value={filters.responsavel}
                onChange={(e) => handleFilterChange('responsavel', e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="">Todos os responsáveis</option>
                {(filterOptions.responsaveis || []).map((responsavel) => (
                  <option key={responsavel} value={responsavel}>
                    {responsavel}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Dicas de busca */}
        <div className="mt-6 text-sm text-muted-foreground">
          <p className="font-medium mb-2">💡 Dicas de busca:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
            <div>
              <strong>Número:</strong> Digite apenas números ou códigos (ex: 9078, 006/2025)
            </div>
            <div>
              <strong>Objeto:</strong> Palavras-chave do que está sendo comprado
            </div>
            <div>
              <strong>Secretaria:</strong> Use siglas (SS, SE, SG, SSAN, SSU, SMA) ou nomes
            </div>
            <div>
              <strong>Busca instantânea:</strong> Resultados aparecem conforme você digita
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
