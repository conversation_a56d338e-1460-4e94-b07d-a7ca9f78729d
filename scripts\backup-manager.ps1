# 🎯 INOVAPROCESS - GERENCIADOR DE BACKUPS
param(
    [Parameter(Mandatory=$true)]
    [string]$Action,
    [string]$Description = "backup"
)

$BackupRoot = ".\.backups"
$Date = Get-Date -Format "yyyy-MM-dd-HH-mm"

Write-Host "🎯 BACKUP MANAGER - Ação: $Action" -ForegroundColor Cyan

    Write-Host "🚀 CRIANDO BACKUP PRE-DEPLOY..." -ForegroundColor Green

    $PreDeployPath = "$BackupRoot\pre-deploy\atual"

    # Criar estrutura se não existir
    if (!(Test-Path "$BackupRoot\pre-deploy")) {
        New-Item -ItemType Directory -Path "$BackupRoot\pre-deploy" -Force | Out-Null
    }

    # Remove backup anterior
    if (Test-Path $PreDeployPath) {
        Remove-Item $PreDeployPath -Recurse -Force
        Write-Host "📁 Backup anterior removido" -ForegroundColor Yellow
    }

    # Cria novo backup
    New-Item -ItemType Directory -Path $PreDeployPath -Force | Out-Null

    # Copia arquivos importantes
    $ExcludeFolders = @("node_modules", ".next", ".vercel", ".backups", "backup-*", "checkpoints")

    Get-ChildItem "." | Where-Object { $_.Name -notin $ExcludeFolders } | ForEach-Object {
        Copy-Item $_.FullName -Destination $PreDeployPath -Recurse -Force -ErrorAction SilentlyContinue
    }

    Write-Host "✅ BACKUP PRE-DEPLOY CRIADO: $PreDeployPath" -ForegroundColor Green
}

function Create-Checkpoint {
    Write-ColorOutput Green "📝 CRIANDO CHECKPOINT..."
    
    $CheckpointPath = "$BackupRoot\checkpoints\$Date-$Description"
    New-Item -ItemType Directory -Path $CheckpointPath -Force | Out-Null
    
    # Copia apenas arquivos essenciais
    $EssentialFolders = @("src", "public", "docs")
    $EssentialFiles = @("package.json", "next.config.js", ".env.example", "README.md")
    
    foreach ($folder in $EssentialFolders) {
        if (Test-Path $folder) {
            Copy-Item $folder -Destination $CheckpointPath -Recurse -Force
        }
    }
    
    foreach ($file in $EssentialFiles) {
        if (Test-Path $file) {
            Copy-Item $file -Destination $CheckpointPath -Force
        }
    }
    
    Write-ColorOutput Green "✅ CHECKPOINT CRIADO: $CheckpointPath"
}

function Migrate-PreDeploy {
    Write-ColorOutput Green "🔄 MIGRANDO PRE-DEPLOY PARA MARCOS..."
    
    $PreDeployPath = "$BackupRoot\pre-deploy\atual"
    $MarcosPath = "$BackupRoot\marcos\$Date-deploy-$Description"
    
    if (Test-Path $PreDeployPath) {
        Move-Item $PreDeployPath $MarcosPath
        Write-ColorOutput Green "✅ MIGRADO PARA MARCOS: $MarcosPath"
    } else {
        Write-ColorOutput Red "❌ Nenhum backup pre-deploy encontrado"
    }
}

function Clean-OldBackups {
    Write-ColorOutput Yellow "🧹 LIMPANDO BACKUPS ANTIGOS..."
    
    # Limpar checkpoints antigos (manter apenas 4)
    $CheckpointsPath = "$BackupRoot\checkpoints"
    if (Test-Path $CheckpointsPath) {
        $OldCheckpoints = Get-ChildItem $CheckpointsPath | Sort-Object LastWriteTime -Descending | Select-Object -Skip 4
        foreach ($checkpoint in $OldCheckpoints) {
            Remove-Item $checkpoint.FullName -Recurse -Force
            Write-ColorOutput Yellow "🗑️ Removido: $($checkpoint.Name)"
        }
    }
    
    Write-ColorOutput Green "✅ LIMPEZA CONCLUÍDA"
}

# Criar estrutura se não existir
if (!(Test-Path $BackupRoot)) {
    New-Item -ItemType Directory -Path $BackupRoot -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\pre-deploy" -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\checkpoints" -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\marcos" -Force | Out-Null
}

# Executar ação
switch ($Action) {
    "pre-deploy" { Create-PreDeploy }
    "checkpoint" { Create-Checkpoint }
    "migrate" { Migrate-PreDeploy }
    "clean" { Clean-OldBackups }
}

Write-ColorOutput Cyan "🎯 BACKUP MANAGER - OPERAÇÃO CONCLUÍDA!"
