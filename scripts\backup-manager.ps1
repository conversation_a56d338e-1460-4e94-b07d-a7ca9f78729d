# 🎯 INOVAPROCESS - GERENCIADOR DE BACKUPS
# Autor: Sistema de Backup Profissional
# Data: 2025-06-30

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("pre-deploy", "checkpoint", "migrate", "clean")]
    [string]$Action,
    
    [string]$Description = "backup"
)

$BackupRoot = ".\.backups"
$ProjectRoot = "."
$Date = Get-Date -Format "yyyy-MM-dd-HH-mm"

function Write-ColorOutput($ForegroundColor, $Message) {
    Write-Host $Message -ForegroundColor $ForegroundColor
}

function Create-PreDeploy {
    Write-ColorOutput Green "🚀 CRIANDO BACKUP PRE-DEPLOY..."
    
    $PreDeployPath = "$BackupRoot\pre-deploy\atual"
    
    # Remove backup anterior
    if (Test-Path $PreDeployPath) {
        Remove-Item $PreDeployPath -Recurse -Force
        Write-ColorOutput Yellow "📁 Backup anterior removido"
    }
    
    # Cria novo backup
    New-Item -ItemType Directory -Path $PreDeployPath -Force | Out-Null
    
    # Copia arquivos importantes (exceto node_modules, .next, etc)
    $ExcludeFolders = @("node_modules", ".next", ".vercel", ".backups", "backup-*")
    
    Get-ChildItem $ProjectRoot -Exclude $ExcludeFolders | ForEach-Object {
        Copy-Item $_.FullName -Destination $PreDeployPath -Recurse -Force
    }
    
    Write-ColorOutput Green "✅ BACKUP PRE-DEPLOY CRIADO: $PreDeployPath"
}

function Create-Checkpoint {
    Write-ColorOutput Green "📝 CRIANDO CHECKPOINT..."
    
    $CheckpointPath = "$BackupRoot\checkpoints\$Date-$Description"
    New-Item -ItemType Directory -Path $CheckpointPath -Force | Out-Null
    
    # Copia apenas arquivos essenciais
    $EssentialFolders = @("src", "public", "docs")
    $EssentialFiles = @("package.json", "next.config.js", ".env.example", "README.md")
    
    foreach ($folder in $EssentialFolders) {
        if (Test-Path $folder) {
            Copy-Item $folder -Destination $CheckpointPath -Recurse -Force
        }
    }
    
    foreach ($file in $EssentialFiles) {
        if (Test-Path $file) {
            Copy-Item $file -Destination $CheckpointPath -Force
        }
    }
    
    Write-ColorOutput Green "✅ CHECKPOINT CRIADO: $CheckpointPath"
}

function Migrate-PreDeploy {
    Write-ColorOutput Green "🔄 MIGRANDO PRE-DEPLOY PARA MARCOS..."
    
    $PreDeployPath = "$BackupRoot\pre-deploy\atual"
    $MarcosPath = "$BackupRoot\marcos\$Date-deploy-$Description"
    
    if (Test-Path $PreDeployPath) {
        Move-Item $PreDeployPath $MarcosPath
        Write-ColorOutput Green "✅ MIGRADO PARA MARCOS: $MarcosPath"
    } else {
        Write-ColorOutput Red "❌ Nenhum backup pre-deploy encontrado"
    }
}

function Clean-OldBackups {
    Write-ColorOutput Yellow "🧹 LIMPANDO BACKUPS ANTIGOS..."
    
    # Limpar checkpoints antigos (manter apenas 4)
    $CheckpointsPath = "$BackupRoot\checkpoints"
    if (Test-Path $CheckpointsPath) {
        $OldCheckpoints = Get-ChildItem $CheckpointsPath | Sort-Object LastWriteTime -Descending | Select-Object -Skip 4
        foreach ($checkpoint in $OldCheckpoints) {
            Remove-Item $checkpoint.FullName -Recurse -Force
            Write-ColorOutput Yellow "🗑️ Removido: $($checkpoint.Name)"
        }
    }
    
    Write-ColorOutput Green "✅ LIMPEZA CONCLUÍDA"
}

# Criar estrutura se não existir
if (!(Test-Path $BackupRoot)) {
    New-Item -ItemType Directory -Path $BackupRoot -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\pre-deploy" -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\checkpoints" -Force | Out-Null
    New-Item -ItemType Directory -Path "$BackupRoot\marcos" -Force | Out-Null
}

# Executar ação
switch ($Action) {
    "pre-deploy" { Create-PreDeploy }
    "checkpoint" { Create-Checkpoint }
    "migrate" { Migrate-PreDeploy }
    "clean" { Clean-OldBackups }
}

Write-ColorOutput Cyan "🎯 BACKUP MANAGER - OPERAÇÃO CONCLUÍDA!"
