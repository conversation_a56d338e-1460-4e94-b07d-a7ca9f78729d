// Tipos para sistema de organizações genérico

export type TipoOrganizacao = 'prefeitura' | 'empresa' | 'orgao_estadual' | 'autarquia' | 'ong' | 'outro';

export type NivelAcesso = 'admin' | 'gestor' | 'operador' | 'consulta';

export interface EstruturaOrganizacional {
  // Campos comuns
  nome: string;
  tipo: TipoOrganizacao;
  
  // Campos específicos por tipo
  estrutura: {
    // Para prefeituras
    secretaria?: string;
    setor?: string;
    funcao?: string;
    matricula?: string;
    
    // Para empresas
    departamento?: string;
    area?: string;
    cargo?: string;
    codigo_funcionario?: string;
    
    // Para órgãos estaduais
    diretoria?: string;
    coordenadoria?: string;
    
    // Campos genéricos
    outros_campos?: Record<string, string>;
  };
}

export interface SolicitacaoAcesso {
  id?: string;
  email: string;
  nome: string;
  telefone?: string;
  organizacao: EstruturaOrganizacional;
  nivel_acesso_solicitado: NivelAcesso;
  justificativa: string;
  status: 'pendente' | 'aprovado' | 'reprovado';
  data_solicitacao: Date;
  data_resposta?: Date;
  aprovado_por?: string;
  observacoes_admin?: string;
}

export interface UsuarioSistema {
  uid: string;
  email: string;
  nome: string;
  organizacao: EstruturaOrganizacional;
  nivel_acesso: NivelAcesso;
  ativo: boolean;
  data_aprovacao: Date;
  aprovado_por: string;
  ultimo_acesso?: Date;
  configuracoes?: {
    notificacoes_email: boolean;
    tema: 'light' | 'dark' | 'auto';
    idioma: 'pt-BR' | 'en';
  };
}

// Configurações de campos por tipo de organização
export const CONFIGURACOES_ORGANIZACAO: Record<TipoOrganizacao, {
  nome: string;
  campos_obrigatorios: string[];
  campos_opcionais: string[];
  estrutura_hierarquica: string[];
}> = {
  prefeitura: {
    nome: 'Prefeitura Municipal',
    campos_obrigatorios: ['secretaria', 'setor', 'funcao'],
    campos_opcionais: ['matricula'],
    estrutura_hierarquica: ['secretaria', 'setor', 'funcao']
  },
  empresa: {
    nome: 'Empresa Privada',
    campos_obrigatorios: ['departamento', 'cargo'],
    campos_opcionais: ['area', 'codigo_funcionario'],
    estrutura_hierarquica: ['departamento', 'area', 'cargo']
  },
  orgao_estadual: {
    nome: 'Órgão Estadual',
    campos_obrigatorios: ['diretoria', 'funcao'],
    campos_opcionais: ['coordenadoria', 'matricula'],
    estrutura_hierarquica: ['diretoria', 'coordenadoria', 'funcao']
  },
  autarquia: {
    nome: 'Autarquia',
    campos_obrigatorios: ['departamento', 'funcao'],
    campos_opcionais: ['setor', 'matricula'],
    estrutura_hierarquica: ['departamento', 'setor', 'funcao']
  },
  ong: {
    nome: 'ONG/Organização Social',
    campos_obrigatorios: ['area', 'funcao'],
    campos_opcionais: ['departamento'],
    estrutura_hierarquica: ['area', 'departamento', 'funcao']
  },
  outro: {
    nome: 'Outro Tipo',
    campos_obrigatorios: ['funcao'],
    campos_opcionais: ['departamento', 'area'],
    estrutura_hierarquica: ['departamento', 'area', 'funcao']
  }
};

// Opções específicas para Prefeitura de Mauá
export const OPCOES_PREFEITURA_MAUA = {
  secretarias: [
    'Governo',
    'Administração',
    'Finanças',
    'Educação',
    'Saúde',
    'Obras',
    'Meio Ambiente',
    'Assistência Social',
    'Cultura',
    'Esportes',
    'Planejamento'
  ],
  setores_governo: [
    'CLMP', // Coordenadoria de Licitações e Materiais Públicos
    'Gabinete',
    'Assessoria Jurídica',
    'Comunicação',
    'Protocolo',
    'Arquivo'
  ],
  funcoes_clmp: [
    'Coordenador',
    'Analista de Licitações',
    'Analista de Contratos',
    'Analista de Materiais',
    'Assistente Administrativo',
    'Estagiário'
  ]
};

// Permissões por nível de acesso
export const PERMISSOES_NIVEL: Record<NivelAcesso, {
  nome: string;
  descricao: string;
  pode_criar_processos: boolean;
  pode_editar_processos: boolean;
  pode_excluir_processos: boolean;
  pode_aprovar_processos: boolean;
  pode_ver_relatorios: boolean;
  pode_gerenciar_usuarios: boolean;
  pode_configurar_sistema: boolean;
}> = {
  admin: {
    nome: 'Administrador',
    descricao: 'Acesso total ao sistema',
    pode_criar_processos: true,
    pode_editar_processos: true,
    pode_excluir_processos: true,
    pode_aprovar_processos: true,
    pode_ver_relatorios: true,
    pode_gerenciar_usuarios: true,
    pode_configurar_sistema: true
  },
  gestor: {
    nome: 'Gestor',
    descricao: 'Pode gerenciar processos e ver relatórios',
    pode_criar_processos: true,
    pode_editar_processos: true,
    pode_excluir_processos: false,
    pode_aprovar_processos: true,
    pode_ver_relatorios: true,
    pode_gerenciar_usuarios: false,
    pode_configurar_sistema: false
  },
  operador: {
    nome: 'Operador',
    descricao: 'Pode criar e editar processos',
    pode_criar_processos: true,
    pode_editar_processos: true,
    pode_excluir_processos: false,
    pode_aprovar_processos: false,
    pode_ver_relatorios: false,
    pode_gerenciar_usuarios: false,
    pode_configurar_sistema: false
  },
  consulta: {
    nome: 'Consulta',
    descricao: 'Apenas visualização',
    pode_criar_processos: false,
    pode_editar_processos: false,
    pode_excluir_processos: false,
    pode_aprovar_processos: false,
    pode_ver_relatorios: false,
    pode_gerenciar_usuarios: false,
    pode_configurar_sistema: false
  }
};
