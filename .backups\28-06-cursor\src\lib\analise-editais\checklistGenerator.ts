// Gerador de checklist baseado nas análises

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

interface ChecklistItem {
  id: string;
  categoria: string;
  item: string;
  obrigatorio: boolean;
  atendido?: boolean;
  observacao?: string;
  fonte: 'LEI_14133' | 'IA' | 'PADRAO_MAUA';
  artigo?: string;
  confianca?: number;
}

export async function gerarChecklist(
  documentos: DocumentoAnalise[], 
  analiseConformidade: any, 
  analiseIA: any
): Promise<ChecklistItem[]> {
  
  const checklist: ChecklistItem[] = [];

  // Checklist baseado na Lei 14.133/21
  checklist.push(...gerarChecklistLei14133(documentos, analiseConformidade));

  // Checklist baseado na análise de IA
  checklist.push(...gerarChecklistIA(documentos, analiseIA));

  // Checklist padrão da Prefeitura de Mauá
  checklist.push(...gerarChecklistPadraoMaua(documentos));

  return checklist;
}

function gerarChecklistLei14133(documentos: DocumentoAnalise[], analiseConformidade: any): ChecklistItem[] {
  const checklist: ChecklistItem[] = [];

  // ETP - Art. 18
  const etp = documentos.find(d => d.tipo === 'etp');
  if (etp) {
    checklist.push({
      id: 'lei_etp_1',
      categoria: 'ETP',
      item: 'Justificativa da necessidade da contratação (Art. 18, I)',
      obrigatorio: true,
      atendido: verificarElemento(etp.conteudo, ['justificativa', 'necessidade']),
      observacao: !verificarElemento(etp.conteudo, ['justificativa']) ? 'Justificativa não identificada claramente' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 18, I'
    });

    checklist.push({
      id: 'lei_etp_2',
      categoria: 'ETP',
      item: 'Descrição do objeto a ser contratado (Art. 18, II)',
      obrigatorio: true,
      atendido: verificarElemento(etp.conteudo, ['objeto', 'descrição']),
      observacao: !verificarElemento(etp.conteudo, ['objeto']) ? 'Objeto não claramente definido' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 18, II'
    });

    checklist.push({
      id: 'lei_etp_3',
      categoria: 'ETP',
      item: 'Análise de riscos (Art. 18, V)',
      obrigatorio: true,
      atendido: verificarElemento(etp.conteudo, ['risco', 'análise']),
      observacao: !verificarElemento(etp.conteudo, ['risco']) ? 'Análise de riscos não identificada' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 18, V'
    });

    checklist.push({
      id: 'lei_etp_4',
      categoria: 'ETP',
      item: 'Estimativa de custos (Art. 18, VI)',
      obrigatorio: true,
      atendido: verificarElemento(etp.conteudo, ['custo', 'estimativa', 'valor', 'orçamento']),
      observacao: !verificarElemento(etp.conteudo, ['custo', 'valor']) ? 'Estimativa de custos não identificada' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 18, VI'
    });

    checklist.push({
      id: 'lei_etp_5',
      categoria: 'ETP',
      item: 'Cronograma de execução (Art. 18, VII)',
      obrigatorio: true,
      atendido: verificarElemento(etp.conteudo, ['cronograma', 'prazo', 'etapa']),
      observacao: !verificarElemento(etp.conteudo, ['cronograma']) ? 'Cronograma não especificado' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 18, VII'
    });
  }

  // EDITAL - Art. 40
  const edital = documentos.find(d => d.tipo === 'edital');
  if (edital) {
    checklist.push({
      id: 'lei_edital_1',
      categoria: 'EDITAL',
      item: 'Preâmbulo com identificação do órgão (Art. 40, I)',
      obrigatorio: true,
      atendido: verificarElemento(edital.conteudo, ['preâmbulo', 'prefeitura', 'órgão']),
      observacao: !verificarElemento(edital.conteudo, ['preâmbulo']) ? 'Preâmbulo não identificado' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, I'
    });

    checklist.push({
      id: 'lei_edital_2',
      categoria: 'EDITAL',
      item: 'Objeto da licitação claramente definido (Art. 40, II)',
      obrigatorio: true,
      atendido: verificarElemento(edital.conteudo, ['objeto']),
      observacao: !verificarElemento(edital.conteudo, ['objeto']) ? 'Objeto não claramente definido' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, II'
    });

    checklist.push({
      id: 'lei_edital_3',
      categoria: 'EDITAL',
      item: 'Critérios de julgamento (Art. 40, VII)',
      obrigatorio: true,
      atendido: verificarElemento(edital.conteudo, ['critério', 'julgamento', 'menor preço']),
      observacao: !verificarElemento(edital.conteudo, ['critério']) ? 'Critérios de julgamento não especificados' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, VII'
    });

    checklist.push({
      id: 'lei_edital_4',
      categoria: 'EDITAL',
      item: 'Documentação de habilitação (Art. 40, VIII)',
      obrigatorio: true,
      atendido: verificarElemento(edital.conteudo, ['habilitação', 'documentação', 'certidão']),
      observacao: !verificarElemento(edital.conteudo, ['habilitação']) ? 'Documentação de habilitação não especificada' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, VIII'
    });

    checklist.push({
      id: 'lei_edital_5',
      categoria: 'EDITAL',
      item: 'Condições de pagamento (Art. 40, XI)',
      obrigatorio: true,
      atendido: verificarElemento(edital.conteudo, ['pagamento', 'condições']),
      observacao: !verificarElemento(edital.conteudo, ['pagamento']) ? 'Condições de pagamento não especificadas' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, XI'
    });

    // Verificar modalidade adequada
    const modalidadeAdequada = !verificarElemento(edital.conteudo, ['convite', 'tomada de preço']);
    checklist.push({
      id: 'lei_edital_6',
      categoria: 'EDITAL',
      item: 'Modalidade conforme Lei 14.133/21 (Art. 28)',
      obrigatorio: true,
      atendido: modalidadeAdequada,
      observacao: !modalidadeAdequada ? 'Modalidade incompatível com a Lei 14.133/21' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 28'
    });
  }

  // TR - Termo de Referência
  const tr = documentos.find(d => d.tipo === 'tr');
  if (tr) {
    checklist.push({
      id: 'lei_tr_1',
      categoria: 'TR',
      item: 'Especificações técnicas detalhadas (Art. 40, VI)',
      obrigatorio: true,
      atendido: verificarElemento(tr.conteudo, ['especificação', 'técnica']),
      observacao: !verificarElemento(tr.conteudo, ['especificação']) ? 'Especificações técnicas inadequadas' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, VI'
    });

    checklist.push({
      id: 'lei_tr_2',
      categoria: 'TR',
      item: 'Quantitativos e unidades de medida (Art. 40, VI)',
      obrigatorio: true,
      atendido: verificarElemento(tr.conteudo, ['quantidade', 'unidade']),
      observacao: !verificarElemento(tr.conteudo, ['quantidade']) ? 'Quantitativos não especificados' : '',
      fonte: 'LEI_14133',
      artigo: 'Art. 40, VI'
    });

    checklist.push({
      id: 'lei_tr_3',
      categoria: 'TR',
      item: 'Critérios de aceitação dos bens/serviços',
      obrigatorio: true,
      atendido: verificarElemento(tr.conteudo, ['critério', 'aceitação', 'recebimento']),
      observacao: !verificarElemento(tr.conteudo, ['aceitação']) ? 'Critérios de aceitação não definidos' : '',
      fonte: 'LEI_14133'
    });

    checklist.push({
      id: 'lei_tr_4',
      categoria: 'TR',
      item: 'Obrigações do contratado',
      obrigatorio: true,
      atendido: verificarElemento(tr.conteudo, ['obrigação', 'contratado', 'responsabilidade']),
      observacao: !verificarElemento(tr.conteudo, ['obrigação']) ? 'Obrigações do contratado não especificadas' : '',
      fonte: 'LEI_14133'
    });

    checklist.push({
      id: 'lei_tr_5',
      categoria: 'TR',
      item: 'Fiscalização e acompanhamento',
      obrigatorio: true,
      atendido: verificarElemento(tr.conteudo, ['fiscalização', 'acompanhamento', 'gestor']),
      observacao: !verificarElemento(tr.conteudo, ['fiscalização']) ? 'Fiscalização não definida' : '',
      fonte: 'LEI_14133'
    });
  }

  return checklist;
}

function gerarChecklistIA(documentos: DocumentoAnalise[], analiseIA: any): ChecklistItem[] {
  const checklist: ChecklistItem[] = [];

  // Adicionar itens baseados nos riscos identificados pela IA
  analiseIA.riscosIdentificados?.forEach((risco: any, index: number) => {
    if (risco.probabilidade > 0.6) {
      checklist.push({
        id: `ia_risco_${index}`,
        categoria: 'IA_RISCOS',
        item: risco.descricao,
        obrigatorio: risco.probabilidade > 0.8,
        atendido: false,
        observacao: `Risco detectado pela IA (${Math.round(risco.probabilidade * 100)}% probabilidade)`,
        fonte: 'IA',
        confianca: risco.confianca
      });
    }
  });

  // Adicionar itens baseados nas sugestões da IA
  analiseIA.sugestoes?.forEach((sugestao: any, index: number) => {
    if (sugestao.prioridade === 'ALTA' || sugestao.prioridade === 'MEDIA') {
      checklist.push({
        id: `ia_sugestao_${index}`,
        categoria: 'IA_SUGESTOES',
        item: sugestao.titulo,
        obrigatorio: sugestao.prioridade === 'ALTA',
        atendido: false,
        observacao: sugestao.descricao,
        fonte: 'IA',
        confianca: sugestao.confianca
      });
    }
  });

  return checklist;
}

function gerarChecklistPadraoMaua(documentos: DocumentoAnalise[]): ChecklistItem[] {
  const checklist: ChecklistItem[] = [];

  // Padrões específicos da Prefeitura de Mauá
  checklist.push({
    id: 'maua_1',
    categoria: 'PADRÃO_MAUÁ',
    item: 'Identificação da CLMP como órgão responsável',
    obrigatorio: true,
    atendido: documentos.some(doc => verificarElemento(doc.conteudo, ['clmp', 'coordenadoria'])),
    observacao: '',
    fonte: 'PADRAO_MAUA'
  });

  checklist.push({
    id: 'maua_2',
    categoria: 'PADRÃO_MAUÁ',
    item: 'Referência à legislação municipal aplicável',
    obrigatorio: false,
    atendido: documentos.some(doc => verificarElemento(doc.conteudo, ['municipal', 'mauá'])),
    observacao: '',
    fonte: 'PADRAO_MAUA'
  });

  // Remover critérios de sustentabilidade - não há lei específica

  return checklist;
}

function verificarElemento(conteudo: string, termos: string[]): boolean {
  const conteudoLower = conteudo.toLowerCase();
  return termos.some(termo => conteudoLower.includes(termo.toLowerCase()));
}
