export interface Contrato {
  id: string;
  numero: string;
  objeto: string;
  contratada: string;
  valor: number;
  dataInicio: string;
  dataVencimento: string;
  status: 'VIGENTE' | 'VENCIDO' | 'SUSPENSO' | 'ENCERRADO';
  diasRestantes: number;
  secretaria: string;
  modalidade: string;
  processoOrigem: string;
  cnpj?: string;
  responsavel?: string;
  observacoes?: string;
  dataAssinatura?: string;
  valorExecutado?: number;
  percentualExecutado?: number;
  aditivos?: Aditivo[];
  garantia?: {
    tipo: 'SEGURO' | 'FIANCA' | 'CAUCAO';
    valor: number;
    dataVencimento: string;
  };
}

export interface Aditivo {
  id: string;
  numero: string;
  tipo: 'PRAZO' | 'VALOR' | 'QUALITATIVO';
  dataAssinatura: string;
  valorAcrescimo?: number;
  prazoAcrescimo?: number; // em dias
  justificativa: string;
  status: 'ATIVO' | 'CANCELADO';
}

export interface ContratoFilters {
  search?: string;
  status?: string;
  secretaria?: string;
  modalidade?: string;
  dataInicio?: string;
  dataVencimento?: string;
  valorMin?: number;
  valorMax?: number;
}

export interface ContratoStats {
  total: number;
  vigentes: number;
  vencidos: number;
  suspensos: number;
  encerrados: number;
  vencendoEm30Dias: number;
  valorTotal: number;
  valorVigentes: number;
  valorExecutado: number;
  percentualMedioExecucao: number;
}

export interface PesquisaPreco {
  id: string;
  item: string;
  categoria: string;
  precoMedio: number;
  menorPreco: number;
  maiorPreco: number;
  orgao: string;
  dataLicitacao: string;
  modalidade: string;
  status: string;
  uf: string;
  cidade: string;
  numeroLicitacao?: string;
  fornecedor?: string;
  unidade?: string;
  quantidade?: number;
  observacoes?: string;
}

export interface HistoricoPesquisa {
  id: string;
  termo: string;
  dataConsulta: string;
  resultados: number;
  usuario: string;
  fonte: 'PNCP' | 'MANUAL' | 'IMPORTACAO';
}

export interface PesquisaPrecoFilters {
  search?: string;
  categoria?: string;
  uf?: string;
  modalidade?: string;
  precoMin?: number;
  precoMax?: number;
  dataInicio?: string;
  dataFim?: string;
}

export interface PesquisaPrecoStats {
  totalResultados: number;
  precoMedio: number;
  menorPrecoGeral: number;
  maiorPrecoGeral: number;
  orgaosConsultados: number;
  economiaEstimada: number;
}
