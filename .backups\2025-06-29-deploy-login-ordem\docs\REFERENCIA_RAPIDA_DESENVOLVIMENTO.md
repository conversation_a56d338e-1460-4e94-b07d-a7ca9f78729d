# ⚡ **REFERÊNCIA RÁPIDA PARA DESENVOLVIMENTO**

> **🚨 CONSULTE SEMPRE ANTES DE IMPLEMENTAR QUALQUER FUNCIONALIDADE**

## 🎯 **REGRAS FUNDAMENTAIS**

### **🔴 PRIORIDADE ALTA - FONTES NÃO-TESOURO**
```typescript
// SEMPRE verificar fonte de recursos
if (processo.FONTE !== "Tesouro") {
  // PRIORIDADE ALTA OBRIGATÓRIA
  // Risco de perda definitiva do recurso
  // Implementar alertas especiais
}
```

### **🟠 RETRABALHO REAL (Apenas 2 status)**
```typescript
const RETRABALHO_REAL = [
  "Para Adequações",
  "Encaminhado Para a Secretaria"
];
// Outros "retornos" são fluxo normal!
```

### **⏱️ GARGALOS CRÍTICOS A MEDIR**
```typescript
const GARGALOS_CRITICOS = {
  SF: ["Para Análise Orçamentária", "Retornou Após Análise Orçamentária"],
  SAJ: ["Para Análise Jurídica", "Retornou Após Análise Jurídica"]
};
// Medir tempo especial para SF e SAJ
```

---

## 📊 **CLASSIFICAÇÃO DE STATUS**

### **🟡 EM MOVIMENTO**
- Para Pesquisa de Preços
- Para Análise Técnica
- Para Publicação de Edital
- Licitação em Andamento
- Para Elaboração de Contrato/Ata

### **🔵 AGUARDANDO TERCEIROS**
- Aguardando Documento do Fornecedor
- Aguardando Abertura da Licitação
- Para Homologação e AUDESP
- Para Assinatura da Homologação

### **🟢 FINALIZADOS**
- Finalizado
- Para Abertura de Processos de Gerenciamento

---

## 🛡️ **MÉTRICAS PARA DEFESA DA CLMP**

### **1. Tempo por Local**
```typescript
// Medir quanto tempo fica em cada local
const tempoLocal = calcularTempo(dataEntrada, dataSaida, local);
```

### **2. Processos que Demoraram para Entrar**
```typescript
// Tempo entre criação e chegada na CLMP
const tempoParaEntrar = calcularTempo(dataCriacao, dataChegadaCLMP);
```

### **3. Taxa de Retrabalho Real**
```typescript
// Apenas "Para Adequações" e "Encaminhado Para a Secretaria"
const retrabalho = processos.filter(p => RETRABALHO_REAL.includes(p.status));
```

---

## 🎨 **PADRÕES DE UI**

### **Cores por Status**
```typescript
const STATUS_COLORS = {
  // Prioridade alta (fontes não-Tesouro)
  prioridadeAlta: "text-red-600 bg-red-100",
  
  // Em movimento
  emMovimento: "text-blue-600 bg-blue-100",
  
  // Aguardando
  aguardando: "text-yellow-600 bg-yellow-100",
  
  // Retrabalho
  retrabalho: "text-orange-600 bg-orange-100",
  
  // Finalizado
  finalizado: "text-green-600 bg-green-100"
};
```

### **Badges de Alerta**
```typescript
// Para fontes não-Tesouro
<Badge variant="destructive">PRIORIDADE ALTA</Badge>

// Para gargalos SF/SAJ
<Badge variant="warning">GARGALO CRÍTICO</Badge>
```

---

## 📈 **MÉTRICAS OBRIGATÓRIAS**

### **Dashboard Principal**
1. Total de processos
2. Em andamento vs. finalizados
3. Processos com prioridade alta (fontes não-Tesouro)
4. Tempo médio na CLMP vs. outras secretarias
5. Taxa de retrabalho real
6. Gargalos SF e SAJ

### **Relatórios por Secretaria**
1. Tempo médio de resposta
2. Taxa de retrabalho gerado
3. Processos que demoraram para enviar
4. Comparativo de eficiência

---

## 🚨 **ALERTAS OBRIGATÓRIOS**

### **Prioridade Alta**
```typescript
if (processo.FONTE !== "Tesouro") {
  mostrarAlerta("RISCO DE PERDA DE RECURSO", "error");
}
```

### **Gargalos Críticos**
```typescript
if (tempoNaSF > LIMITE_SF || tempoNaSAJ > LIMITE_SAJ) {
  mostrarAlerta("GARGALO CRÍTICO DETECTADO", "warning");
}
```

### **Retrabalho**
```typescript
if (RETRABALHO_REAL.includes(processo.status)) {
  mostrarAlerta("PROCESSO COM RETRABALHO", "info");
}
```

---

## 📋 **CAMPOS OBRIGATÓRIOS**

### **Processo**
- PROCESSO (número único)
- OBJETO (descrição)
- MODALIDADE (Lei 14133/21)
- SITUACAO (status do dicionário)
- LOCAL (onde está fisicamente)
- RESPONSAVEL (pessoa na CLMP ou sigla da secretaria)
- FONTE (Tesouro ou outras)
- REQUISITANTE (secretaria de origem)

### **Controle Temporal**
- DATA_CRIACAO
- DATA_ENTRADA_CLMP
- DATA_ULTIMA_MOVIMENTACAO
- TEMPO_TOTAL
- TEMPO_POR_LOCAL

---

## 🔄 **FLUXO DE DESENVOLVIMENTO**

### **1. Antes de Implementar**
- ✅ Consultar documentação conceitual completa
- ✅ Verificar regras de negócio específicas
- ✅ Confirmar classificação de status
- ✅ Validar métricas necessárias

### **2. Durante Implementação**
- ✅ Aplicar padrões de UI consistentes
- ✅ Implementar alertas obrigatórios
- ✅ Testar com dados reais
- ✅ Validar responsividade

### **3. Após Implementação**
- ✅ Testar métricas de defesa da CLMP
- ✅ Verificar alertas de prioridade
- ✅ Validar performance
- ✅ Documentar mudanças

---

## 📚 **DOCUMENTOS DE REFERÊNCIA**

1. **[Documentação Conceitual Completa](DOCUMENTACAO_CONCEITUAL_COMPLETA.md)** - Base fundamental
2. **[Dicionário CSV](../Acompanhamento%20de%20Processos%20-%20CLMP%20-%20DICIONARIO.csv)** - Status oficiais
3. **[Dados Reais](../data/backup-abril-v1/)** - Dados para testes

---

**⚠️ LEMBRE-SE: O objetivo principal é DEFENDER A REPUTAÇÃO DA CLMP através de dados irrefutáveis!**
