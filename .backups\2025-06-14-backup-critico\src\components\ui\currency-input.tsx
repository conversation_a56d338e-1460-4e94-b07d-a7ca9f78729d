'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';

interface CurrencyInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
}

export default function CurrencyInput({ 
  value, 
  onChange, 
  placeholder = "R$ 0,00", 
  className = "",
  onKeyDown 
}: CurrencyInputProps) {
  const [displayValue, setDisplayValue] = useState('');

  // Formatar valor para exibição
  const formatCurrency = (value: string): string => {
    // Remove tudo que não é número
    const numbers = value.replace(/\D/g, '');
    
    if (!numbers) return '';
    
    // Converte para centavos
    const cents = parseInt(numbers);
    
    // Formata como moeda brasileira
    const formatted = (cents / 100).toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 2
    });
    
    return formatted;
  };

  // Extrair valor numérico limpo
  const getCleanValue = (formattedValue: string): string => {
    return formattedValue.replace(/[^\d,]/g, '').replace(',', '.');
  };

  // Atualizar display quando value prop muda
  useEffect(() => {
    if (value) {
      setDisplayValue(formatCurrency(value.replace(/[^\d]/g, '')));
    } else {
      setDisplayValue('');
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const formatted = formatCurrency(inputValue);
    
    setDisplayValue(formatted);
    
    // Retorna valor limpo para o componente pai
    const cleanValue = getCleanValue(formatted);
    onChange(cleanValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Permite Enter para salvar
    if (e.key === 'Enter') {
      e.preventDefault();
      if (onKeyDown) {
        onKeyDown(e);
      }
    }
  };

  return (
    <Input
      type="text"
      value={displayValue}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      placeholder={placeholder}
      className={className}
    />
  );
}
