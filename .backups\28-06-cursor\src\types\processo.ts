export interface Processo {
  [key: string]: string;
  ITEM?: string;
  PROCESSO?: string;
  REQUISITANTE?: string;
  OBJETO?: string;
  MODALIDADE?: string;
  PRIORIDADE?: string;
  "DATA DE INÍCIO DO PROCESSO"?: string;
  "DATA ENTRADA NA CLMP"?: string;
  "VALOR ESTIMADO"?: string;
  "Fonte 0001 (TESOURO)"?: string;
  "Fonte 0002 (ESTADUAL)"?: string;
  "Fonte 0003 (FUNDO)"?: string;
  "Fonte 0005 (FEDERAL)"?: string;
  "Fonte 0007 (FINISA)"?: string;
  // Datas de vencimento das fontes não-tesouro
  "Data Vencimento Estadual"?: string;
  "Data Vencimento Fundo"?: string;
  "Data Vencimento Federal"?: string;
  "Data Vencimento Finisa"?: string;
  "Nº DO CERTAME"?: string;
  "DATA PUBLICAÇÃO"?: string;
  "DATA ABERTURA"?: string;
  "VALOR CONTRATADO"?: string;
  "CONTRATO NÚMERO"?: string;
  "VENCIMENTO"?: string;
  "PROCESSO DE GERENCIAMENTO"?: string;
  STATUS?: string;
  DATA?: string;
  LOCAL?: string;
  RESPONSÁVEL?: string;

  // Campos adicionais para controle
  prioridadeGoverno?: boolean;
  prioridadeGovernoLog?: {
    usuario: string;
    timestamp: string;
    acao: 'ativou' | 'desativou';
  }[];

  // Campo para solicitações especiais
  SOLICITACAO_ESPECIAL?: {
    ativa: boolean;
    solicitante: string;
    motivo: string;
    data_hora: string;
  };

  // Campos para pesquisa de preços
  pesquisaPrecos?: {
    status: 'pendente' | 'em_andamento' | 'concluida';
    pesquisador?: string;
    dataInicio?: string;
    dataFim?: string;
    tipo: 'simples' | 'combinada';
    itens?: ItemPesquisa[];
    mapaPrecos?: string;
    despacho?: string;
  };
}

export interface ItemPesquisa {
  id: string;
  descricao: string;
  unidade: string;
  quantidade: number;
  especificacao: string;
  precosPNCP?: PrecoPNCP[];
  precosFornecedores?: PrecoFornecedor[];
  precoFinal?: number;
  excluido?: boolean;
  motivoExclusao?: string;
  alertasUnidade?: AlertaUnidade[];
}

export interface AlertaUnidade {
  tipo: 'conversao' | 'incompatibilidade' | 'atencao';
  fornecedor: string;
  itemDesejado: string;
  unidadeDesejada: string;
  unidadeFornecedor: string;
  conversaoNecessaria: string;
  fatorMultiplicacao: number;
  observacao: string;
}

export interface PrecoPNCP {
  id: string;
  fornecedor: string;
  cnpj: string;
  preco: number;
  dataContrato: string;
  orgao: string;
  relevante: boolean;
}

export interface PrecoFornecedor {
  id: string;
  fornecedor: string;
  cnpj: string;
  preco: number;
  dataOrcamento: string;
  origem: 'email' | 'planilha' | 'manual';
}

export interface ProcessoFilters {
  search?: string;
  status?: string;
  modalidade?: string;
  responsavel?: string;
  requisitante?: string;
  fonte?: string; // CORREÇÃO: Adicionar filtro de fonte
  prioridadeGoverno?: boolean;
}