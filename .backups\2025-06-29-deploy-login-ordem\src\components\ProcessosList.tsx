'use client';

import { useState, useEffect } from 'react';
import { Processo } from '@/types/processo';
import { stringify } from 'csv-stringify/sync';
// Remova a importação dinâmica do Chart.js
// const DynamicCharts = dynamic(
//   () => import('@/components/DynamicCharts'),
//   { ssr: false }
// );

interface ProcessosListProps {
  processos: Processo[];
}

export default function ProcessosList({ processos }: ProcessosListProps) {
  const [filtro, setFiltro] = useState('');
  const [ordenarPor, setOrdenarPor] = useState<keyof Processo | ''>('');
  const [ordem, setOrdem] = useState<'asc' | 'desc'>('asc');
  const [colunasSelecionadas, setColunasSelecionadas] = useState<(keyof Processo)[]>([]);
  const [mostrarSelecaoColunas, setMostrarSelecaoColunas] = useState(false);
  const [modoVisualizacao, setModoVisualizacao] = useState<'tabela' | 'dashboard'>('dashboard');
  const [metricas, setMetricas] = useState<{[key: string]: number}>({});
  const [statusDistribuicao, setStatusDistribuicao] = useState<{[key: string]: number}>({});
  const [modalidadeDistribuicao, setModalidadeDistribuicao] = useState<{[key: string]: number}>({});
  const [responsavelDistribuicao, setResponsavelDistribuicao] = useState<{[key: string]: number}>({});
  const [valoresTotais, setValoresTotais] = useState<{[key: string]: number}>({});
  const [progressoTemporal, setProgressoTemporal] = useState<{[key: string]: number[]}>({});

  // Lista de todas as colunas disponíveis
  const todasColunas: (keyof Processo)[] = processos.length > 0 
    ? Object.keys(processos[0]).filter(key => key !== '') as (keyof Processo)[]
    : [
        'ITEM', 'PROCESSO', 'REQUISITANTE', 'OBJETO', 'MODALIDADE', 'PRIORIDADE',
        'DATA DE INÍCIO DO PROCESSO', 'DATA ENTRADA NA CLMP', 'VALOR ESTIMADO',
        'STATUS', 'DATA', 'LOCAL', 'RESPONSÁVEL'
      ];

  // Inicializa as colunas selecionadas quando os processos são carregados
  useEffect(() => {
    if (processos.length > 0 && colunasSelecionadas.length === 0) {
      // Seleciona todas as colunas por padrão
      setColunasSelecionadas(todasColunas);
    }
    
    // Calcula métricas quando os processos mudam
    calcularMetricas();
  }, [processos]);

  // Calcula métricas baseadas nos processos
  const calcularMetricas = () => {
    if (processos.length === 0) return;

    // Contagem total de processos
    const totalProcessos = processos.length;
    
    // Distribuição por status
    const statusCount: {[key: string]: number} = {};
    processos.forEach(processo => {
      const status = processo.STATUS || 'Não definido';
      statusCount[status] = (statusCount[status] || 0) + 1;
    });
    
    // Distribuição por modalidade
    const modalidadeCount: {[key: string]: number} = {};
    processos.forEach(processo => {
      const modalidade = processo.MODALIDADE || 'Não definido';
      modalidadeCount[modalidade] = (modalidadeCount[modalidade] || 0) + 1;
    });
    
    // Distribuição por responsável
    const responsavelCount: {[key: string]: number} = {};
    processos.forEach(processo => {
      const responsavel = processo.RESPONSÁVEL || 'Não definido';
      responsavelCount[responsavel] = (responsavelCount[responsavel] || 0) + 1;
    });
    
    // Valores totais estimados e contratados
    let valorEstimadoTotal = 0;
    let valorContratadoTotal = 0;
    
    processos.forEach(processo => {
      // Extrai valores numéricos das strings de valor
      if (processo['VALOR ESTIMADO']) {
        const valorEstimado = extrairValorNumerico(processo['VALOR ESTIMADO']);
        valorEstimadoTotal += valorEstimado;
      }
      
      if (processo['VALOR CONTRATADO']) {
        const valorContratado = extrairValorNumerico(processo['VALOR CONTRATADO']);
        valorContratadoTotal += valorContratado;
      }
    });
    
    // Progresso temporal (simulado - em uma aplicação real, usaríamos datas reais)
    // Aqui estamos apenas criando dados de exemplo para o gráfico
    const progressoTemporal = {
      'Concluídos': [5, 8, 12, 15, 20, 25],
      'Em andamento': [10, 15, 12, 8, 5, 3],
      'Pendentes': [8, 6, 4, 3, 2, 1]
    };
    
    // Atualiza os estados com as métricas calculadas
    setMetricas({
      totalProcessos,
      processosConcluidos: statusCount['Concluído'] || 0,
      processosEmAndamento: Object.entries(statusCount)
        .filter(([status]) => status !== 'Concluído' && status !== 'Cancelado')
        .reduce((sum, [_, count]) => sum + count, 0),
      processosCancelados: statusCount['Cancelado'] || 0
    });
    
    setStatusDistribuicao(statusCount);
    setModalidadeDistribuicao(modalidadeCount);
    setResponsavelDistribuicao(responsavelCount);
    setValoresTotais({
      valorEstimadoTotal,
      valorContratadoTotal
    });
    setProgressoTemporal(progressoTemporal);
  };
  
  // Função para extrair valor numérico de uma string de valor (ex: "R$ 1.234,56")
  const extrairValorNumerico = (valorString: string): number => {
    // Remove símbolos de moeda, pontos de milhar e substitui vírgula por ponto
    const valorLimpo = valorString.replace(/[^\d,.-]/g, '').replace(',', '.');
    return parseFloat(valorLimpo) || 0;
  };

  // Filtra os processos
  const processosFiltrados = processos.filter(processo => {
    const termoLower = filtro.toLowerCase();
    return Object.entries(processo).some(([_, valor]) => 
      valor && typeof valor === 'string' && valor.toLowerCase().includes(termoLower)
    );
  });

  // Ordena os processos
  const processosOrdenados = [...processosFiltrados].sort((a, b) => {
    if (!ordenarPor) return 0;
    
    const valorA = a[ordenarPor] || '';
    const valorB = b[ordenarPor] || '';
    
    if (ordem === 'asc') {
      return valorA.localeCompare(valorB);
    } else {
      return valorB.localeCompare(valorA);
    }
  });

  // Função para alternar a ordenação
  const alternarOrdenacao = (coluna: keyof Processo) => {
    if (ordenarPor === coluna) {
      setOrdem(ordem === 'asc' ? 'desc' : 'asc');
    } else {
      setOrdenarPor(coluna);
      setOrdem('asc');
    }
  };

  // Função para exportar os dados filtrados
  const exportarCSV = () => {
    if (processosOrdenados.length === 0) return;
    
    // Converte os dados para CSV
    const csv = stringify(processosOrdenados, {
      header: true,
      columns: colunasSelecionadas as string[]
    });
    
    // Cria um blob e um link para download
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `processos-exportados-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Função para alternar a seleção de uma coluna
  const alternarColuna = (coluna: keyof Processo) => {
    if (colunasSelecionadas.includes(coluna)) {
      setColunasSelecionadas(colunasSelecionadas.filter(c => c !== coluna));
    } else {
      setColunasSelecionadas([...colunasSelecionadas, coluna]);
    }
  };

  // Seleciona todas as colunas
  const selecionarTodasColunas = () => {
    setColunasSelecionadas([...todasColunas]);
  };

  // Limpa a seleção de colunas
  const limparSelecaoColunas = () => {
    setColunasSelecionadas([]);
  };

  return (
    <div>
      <div className="mb-4 flex flex-col md:flex-row gap-2 justify-between items-center">
        <div className="flex flex-col md:flex-row gap-2 w-full md:w-2/3">
          <input
            type="text"
            placeholder="Filtrar processos..."
            value={filtro}
            onChange={(e) => setFiltro(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
          />
          <button
            onClick={() => setMostrarSelecaoColunas(!mostrarSelecaoColunas)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            {mostrarSelecaoColunas ? 'Ocultar Colunas' : 'Selecionar Colunas'}
          </button>
        </div>
        <div className="flex gap-2">
          <button 
            onClick={() => setModoVisualizacao('dashboard')}
            className={`px-4 py-2 rounded ${modoVisualizacao === 'dashboard' 
              ? 'bg-indigo-600 text-white' 
              : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}
          >
            Dashboard
          </button>
          <button 
            onClick={() => setModoVisualizacao('tabela')}
            className={`px-4 py-2 rounded ${modoVisualizacao === 'tabela' 
              ? 'bg-indigo-600 text-white' 
              : 'bg-gray-200 text-gray-800 hover:bg-gray-300'}`}
          >
            Tabela
          </button>
          <button 
            onClick={exportarCSV}
            disabled={processosOrdenados.length === 0}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
          >
            Exportar
          </button>
        </div>
      </div>

      {mostrarSelecaoColunas && (
        <div className="mb-4 p-4 border border-gray-300 rounded bg-gray-50">
          <div className="flex justify-between mb-2">
            <h3 className="font-semibold">Selecione as colunas para exibir:</h3>
            <div className="space-x-2">
              <button 
                onClick={selecionarTodasColunas}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Selecionar Todas
              </button>
              <button 
                onClick={limparSelecaoColunas}
                className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Limpar Seleção
              </button>
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {todasColunas.map(coluna => (
              <label key={coluna as string} className="flex items-center">
                <input
                  type="checkbox"
                  checked={colunasSelecionadas.includes(coluna)}
                  onChange={() => alternarColuna(coluna)}
                  className="mr-2"
                />
                {coluna as string}
              </label>
            ))}
          </div>
        </div>
      )}

      {modoVisualizacao === 'dashboard' && (
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-4">Dashboard em manutenção</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
              <h3 className="text-gray-500 text-sm">Total de Processos</h3>
              <p className="text-2xl font-bold">{processos.length}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-green-500">
              <h3 className="text-gray-500 text-sm">Processos Concluídos</h3>
              <p className="text-2xl font-bold">{metricas.processosConcluidos || 0}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-yellow-500">
              <h3 className="text-gray-500 text-sm">Processos em Andamento</h3>
              <p className="text-2xl font-bold">{metricas.processosEmAndamento || 0}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-red-500">
              <h3 className="text-gray-500 text-sm">Processos Cancelados</h3>
              <p className="text-2xl font-bold">{metricas.processosCancelados || 0}</p>
            </div>
          </div>
          <p className="text-gray-600">Os gráficos estão temporariamente indisponíveis. Estamos trabalhando para restaurá-los em breve.</p>
        </div>
      )}

      {modoVisualizacao === 'tabela' && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                {colunasSelecionadas.map(coluna => (
                  <th 
                    key={coluna as string} 
                    className="px-4 py-2 border cursor-pointer text-gray-800" 
                    onClick={() => alternarOrdenacao(coluna)}
                  >
                    {coluna as string} {ordenarPor === coluna && (ordem === 'asc' ? '↑' : '↓')}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {processosOrdenados.length > 0 ? (
                processosOrdenados.map((processo, index) => (
                  <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    {colunasSelecionadas.map(coluna => (
                      <td key={`${index}-${coluna as string}`} className="px-4 py-2 border text-gray-800">
                        {processo[coluna] || ''}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={colunasSelecionadas.length} className="px-4 py-2 text-center text-gray-800">
                    Nenhum processo encontrado
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-700">
        Total de processos: {processosOrdenados.length}
      </div>
    </div>
  );
}







