'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  CheckCircle, 
  DollarSign, 
  FileText,
  Settings,
  Save,
  RotateCcw
} from 'lucide-react';

interface BannerConfig {
  id: string;
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
  badge?: {
    text: string;
    variant: string;
  };
}

interface DraggableBannersProps {
  metricas: any;
  totalItems: number;
  isAdmin?: boolean;
}

export default function DraggableBanners({ 
  metricas, 
  totalItems, 
  isAdmin = false 
}: DraggableBannersProps) {
  const [isConfigMode, setIsConfigMode] = useState(false);
  const [bannerOrder, setBannerOrder] = useState<string[]>([]);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);

  // Configuração padrão dos banners
  const defaultBanners: BannerConfig[] = [
    {
      id: 'total',
      title: 'Total',
      value: totalItems,
      subtitle: 'Processos',
      icon: <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />,
      color: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50'
    },
    {
      id: 'risco',
      title: 'Risco de Perda',
      value: metricas.prioridadeAlta,
      subtitle: 'Fontes não-Tesouro',
      icon: <DollarSign className="h-8 w-8 text-red-600 dark:text-red-400" />,
      color: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50',
      badge: metricas.prioridadeAlta > 0 ? {
        text: 'RECURSOS EXTERNOS',
        variant: 'destructive'
      } : undefined
    },
    {
      id: 'gargalos',
      title: 'Gargalos Críticos',
      value: metricas.gargalosSF + metricas.gargalosSAJ,
      subtitle: `SF: ${metricas.gargalosSF} | SAJ: ${metricas.gargalosSAJ}`,
      icon: <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />,
      color: 'border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50'
    },
    {
      id: 'sucesso',
      title: 'Taxa de Sucesso',
      value: `${metricas.taxaFinalizacao}%`,
      subtitle: 'Eficiência CLMP',
      icon: <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />,
      color: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50',
      badge: {
        text: 'GANHO INSTITUCIONAL',
        variant: 'success'
      }
    },
    {
      id: 'tempo-sf',
      title: 'Tempo Médio SF',
      value: '8.5 dias',
      subtitle: 'Análise Orçamentária',
      icon: <Clock className="h-8 w-8 text-amber-600 dark:text-amber-400" />,
      color: 'border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50',
      badge: {
        text: 'GARGALO EXTERNO',
        variant: 'outline'
      }
    },
    {
      id: 'tempo-saj',
      title: 'Tempo Médio SAJ',
      value: '12.3 dias',
      subtitle: 'Parecer Jurídico',
      icon: <Clock className="h-8 w-8 text-purple-600 dark:text-purple-400" />,
      color: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50',
      badge: {
        text: 'GARGALO EXTERNO',
        variant: 'outline'
      }
    }
  ];

  // Carregar ordem salva ou usar padrão
  useEffect(() => {
    const savedOrder = localStorage.getItem('bannerOrder');
    if (savedOrder) {
      setBannerOrder(JSON.parse(savedOrder));
    } else {
      setBannerOrder(defaultBanners.map(b => b.id));
    }
  }, []);

  // Salvar ordem no localStorage
  const saveBannerOrder = () => {
    localStorage.setItem('bannerOrder', JSON.stringify(bannerOrder));
    setIsConfigMode(false);
  };

  // Resetar para ordem padrão
  const resetBannerOrder = () => {
    const defaultOrder = defaultBanners.map(b => b.id);
    setBannerOrder(defaultOrder);
    localStorage.removeItem('bannerOrder');
  };

  // Handlers para drag and drop
  const handleDragStart = (e: React.DragEvent, bannerId: string) => {
    setDraggedItem(bannerId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetId) return;

    const newOrder = [...bannerOrder];
    const draggedIndex = newOrder.indexOf(draggedItem);
    const targetIndex = newOrder.indexOf(targetId);

    // Remove o item arrastado e insere na nova posição
    newOrder.splice(draggedIndex, 1);
    newOrder.splice(targetIndex, 0, draggedItem);

    setBannerOrder(newOrder);
    setDraggedItem(null);
  };

  // Ordenar banners conforme a ordem salva
  const orderedBanners = bannerOrder
    .map(id => defaultBanners.find(b => b.id === id))
    .filter(Boolean) as BannerConfig[];

  return (
    <div className="space-y-4">
      {/* Controles de configuração (apenas para admins) */}
      {isAdmin && (
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Estatísticas Estratégicas da CLMP</h3>
          
          <div className="flex items-center gap-2">
            {isConfigMode ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetBannerOrder}
                  className="text-xs"
                >
                  <RotateCcw className="mr-1 h-3 w-3" />
                  Resetar
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={saveBannerOrder}
                  className="text-xs"
                >
                  <Save className="mr-1 h-3 w-3" />
                  Salvar
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsConfigMode(true)}
                className="text-xs"
              >
                <Settings className="mr-1 h-3 w-3" />
                Configurar
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Grid de banners */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {orderedBanners.map((banner) => (
          <Card
            key={banner.id}
            className={`${banner.color} ${
              isConfigMode 
                ? 'cursor-move hover:shadow-lg transition-shadow border-2 border-dashed' 
                : ''
            }`}
            draggable={isConfigMode}
            onDragStart={(e) => handleDragStart(e, banner.id)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, banner.id)}
          >
            <CardContent className="p-4 min-h-[120px]">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium">{banner.title}</p>
                  <p className="text-2xl font-bold mt-1">{banner.value}</p>
                  {banner.subtitle && (
                    <p className="text-xs text-muted-foreground mt-1">{banner.subtitle}</p>
                  )}
                </div>
                <div className="ml-3">
                  {banner.icon}
                </div>
              </div>
              {banner.badge && (
                <Badge
                  variant={banner.badge.variant as any}
                  className="mt-3 text-xs"
                >
                  {banner.badge.text}
                </Badge>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {isConfigMode && (
        <div className="text-center text-sm text-muted-foreground">
          💡 Arraste e solte os banners para reorganizar o layout
        </div>
      )}
    </div>
  );
}
