"use client"

import React from 'react'
import { Bar } from 'react-chartjs-2'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

interface ModalidadeChartProps {
  data: number[]
  labels: string[]
  className?: string
}

export function ModalidadeChart({ data, labels, className = "" }: ModalidadeChartProps) {
  const chartData = {
    labels,
    datasets: [
      {
        label: 'Quantidade',
        data,
        backgroundColor: 'rgba(75, 192, 192, 0.7)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
    ],
  }

  const options: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    },
    animation: {
      duration: 1000
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Distribuição por Modalidade</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] flex items-center justify-center">
          <Bar data={chartData} options={options} />
        </div>
      </CardContent>
    </Card>
  )
}