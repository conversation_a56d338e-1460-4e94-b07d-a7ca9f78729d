'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  FileText
} from 'lucide-react';

interface Pesquisador {
  id: string;
  nome: string;
  email: string;
  processosAtivos: number;
  ultimaAtribuicao: string;
  disponivel: boolean;
}

interface DistribuirPesquisaProps {
  processoId: string;
  processoObjeto: string;
  onDistribuir: (pesquisadorId: string) => void;
  onCancelar: () => void;
}

export default function DistribuirPesquisa({ 
  processoId, 
  processoObjeto, 
  onDistribuir, 
  onCancelar 
}: DistribuirPesquisaProps) {
  const [pesquisadorSelecionado, setPesquisadorSelecionado] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Lista de pesquisadores em ordem alfabética
  const pesquisadores: Pesquisador[] = [
    {
      id: 'carla',
      nome: 'Carla',
      email: '<EMAIL>',
      processosAtivos: 3,
      ultimaAtribuicao: '2024-06-03',
      disponivel: true
    },
    {
      id: 'fernando',
      nome: 'Fernando',
      email: '<EMAIL>',
      processosAtivos: 2,
      ultimaAtribuicao: '2024-06-04',
      disponivel: true
    },
    {
      id: 'gilson',
      nome: 'Gilson',
      email: '<EMAIL>',
      processosAtivos: 4,
      ultimaAtribuicao: '2024-06-02',
      disponivel: true
    },
    {
      id: 'mariane',
      nome: 'Mariane',
      email: '<EMAIL>',
      processosAtivos: 1,
      ultimaAtribuicao: '2024-06-05',
      disponivel: true
    },
    {
      id: 'paulo',
      nome: 'Paulo',
      email: '<EMAIL>',
      processosAtivos: 3,
      ultimaAtribuicao: '2024-06-01',
      disponivel: false // Exemplo de indisponível
    },
    {
      id: 'sonia',
      nome: 'Sonia',
      email: '<EMAIL>',
      processosAtivos: 2,
      ultimaAtribuicao: '2024-06-04',
      disponivel: true
    }
  ];

  const handleDistribuir = async () => {
    if (!pesquisadorSelecionado) {
      alert('Selecione um pesquisador');
      return;
    }

    setLoading(true);
    try {
      // Simular atribuição
      await new Promise(resolve => setTimeout(resolve, 1000));
      onDistribuir(pesquisadorSelecionado);
    } catch (error) {
      console.error('Erro ao distribuir:', error);
      alert('Erro ao distribuir pesquisa');
    } finally {
      setLoading(false);
    }
  };

  const getPesquisadorMenosOcupado = () => {
    const disponíveis = pesquisadores.filter(p => p.disponivel);
    return disponíveis.reduce((menor, atual) => 
      atual.processosAtivos < menor.processosAtivos ? atual : menor
    );
  };

  const sugestao = getPesquisadorMenosOcupado();

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Distribuir Pesquisa de Preços
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Processo:</span>
              <Badge variant="outline">{processoId}</Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              <strong>Objeto:</strong> {processoObjeto}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sugestão Automática */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="text-green-800 flex items-center">
            <CheckCircle className="mr-2 h-5 w-5" />
            Sugestão Automática
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-green-800">{sugestao.nome}</p>
              <p className="text-sm text-green-600">
                {sugestao.processosAtivos} processos ativos | Menos ocupado(a)
              </p>
            </div>
            <Button 
              variant="outline" 
              className="border-green-300 text-green-700 hover:bg-green-100"
              onClick={() => setPesquisadorSelecionado(sugestao.id)}
            >
              Selecionar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Pesquisadores */}
      <Card>
        <CardHeader>
          <CardTitle>Selecionar Pesquisador</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {pesquisadores.map((pesquisador) => (
              <div
                key={pesquisador.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  pesquisadorSelecionado === pesquisador.id
                    ? 'border-primary bg-primary/5'
                    : pesquisador.disponivel
                    ? 'border-gray-200 hover:border-gray-300'
                    : 'border-gray-100 bg-gray-50 cursor-not-allowed'
                }`}
                onClick={() => {
                  if (pesquisador.disponivel) {
                    setPesquisadorSelecionado(pesquisador.id);
                  }
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      pesquisador.disponivel ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    
                    <div>
                      <h4 className="font-medium flex items-center">
                        <User className="mr-2 h-4 w-4" />
                        {pesquisador.nome}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {pesquisador.email}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-2 mb-1">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">
                        {pesquisador.processosAtivos} processos
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        Último: {new Date(pesquisador.ultimaAtribuicao).toLocaleDateString('pt-BR')}
                      </span>
                    </div>
                  </div>
                </div>
                
                {!pesquisador.disponivel && (
                  <div className="mt-2 flex items-center text-red-600">
                    <AlertCircle className="mr-2 h-4 w-4" />
                    <span className="text-sm">Indisponível temporariamente</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Ações */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onCancelar}>
          Cancelar
        </Button>
        
        <div className="flex items-center space-x-3">
          {pesquisadorSelecionado && (
            <div className="text-sm text-muted-foreground">
              Selecionado: <strong>
                {pesquisadores.find(p => p.id === pesquisadorSelecionado)?.nome}
              </strong>
            </div>
          )}
          
          <Button 
            onClick={handleDistribuir}
            disabled={!pesquisadorSelecionado || loading}
          >
            {loading ? (
              <>
                <Clock className="mr-2 h-4 w-4 animate-spin" />
                Distribuindo...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Distribuir Pesquisa
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Informações Adicionais */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Próximas Melhorias:</p>
              <ul className="text-xs space-y-1">
                <li>• Distribuição automática por carga de trabalho</li>
                <li>• Notificações por email automáticas</li>
                <li>• Interface mais intuitiva</li>
                <li>• Balanceamento inteligente de demandas</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
